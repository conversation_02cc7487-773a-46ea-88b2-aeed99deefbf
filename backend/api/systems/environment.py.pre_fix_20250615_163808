#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境管理API - MetaGPT第三阶段环境集成
提供环境管理器的API接口
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import logging
from datetime import datetime

# 导入模型
from ..models.common import ApiResponse

# 导入环境管理器
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.environment_manager import environment_manager

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/status", response_model=ApiResponse)
async def get_environment_status():
    """获取环境状态"""
    try:
    status = environment_manager.get_environment_status()

    return ApiResponse(
    success=True,
    message="环境状态获取成功",
    data=status
    )

    except Exception as e:
    logger.error(f"获取环境状态失败: {e}")
    return ApiResponse(
    success=False,
    message=f"获取环境状态失败: {str(e)}"
    )

@router.post("/initialize", response_model=ApiResponse)
async def initialize_environment():
    """初始化环境管理器"""
    try:
    logger.info("开始初始化环境管理器")

    success = await environment_manager.initialize()

    if success:
    return ApiResponse(
    success=True,
    message="环境管理器初始化成功",
    data=environment_manager.get_environment_status()
    )
    else:
    return ApiResponse(
    success=False,
    message="环境管理器初始化失败"
    )

    except Exception as e:
    logger.error(f"环境管理器初始化失败: {e}")
    return ApiResponse(
    success=False,
    message=f"环境管理器初始化失败: {str(e)}"
    )

@router.post("/execute-workflow", response_model=ApiResponse)
async def execute_investment_workflow(request: dict):
    """执行投资工作流"""
    try:
    user_input = request.get("user_input", "")
    context = request.get("context", {})

    logger.info(f"执行投资工作流: {user_input}")

    # 执行工作流
    result = await environment_manager.execute_investment_workflow(
    user_input=user_input,
    context=context
    )

    return ApiResponse(
    success=True,
    message="投资工作流执行完成",
    data=result
    )

    except Exception as e:
    logger.error(f"投资工作流执行失败: {e}")
    return ApiResponse(
    success=False,
    message=f"投资工作流执行失败: {str(e)}"
    )

@router.get("/roles", response_model=ApiResponse)
async def get_roles_status():
    """获取角色状态"""
    try:
    status = environment_manager.get_environment_status()
    roles_info = status.get("roles", {})

    return ApiResponse(
    success=True,
    message="角色状态获取成功",
    data={
    "roles": roles_info,
    "total_roles": len(roles_info),
    "healthy_roles": len([r for r in roles_info.values() if r["status"] == "healthy"]),
    "role_list": list(roles_info.keys())
    }
    )

    except Exception as e:
    logger.error(f"获取角色状态失败: {e}")
    return ApiResponse(
    success=False,
    message=f"获取角色状态失败: {str(e)}"
    )

@router.get("/performance", response_model=ApiResponse)
async def get_performance_metrics():
    """获取性能指标"""
    try:
    status = environment_manager.get_environment_status()
    performance = status.get("performance_stats", {})

    # 计算成功率
    total_workflows = performance.get("total_workflows", 0)
    successful_workflows = performance.get("successful_workflows", 0)
    success_rate = (successful_workflows / total_workflows * 100) if total_workflows > 0 else 0

    enhanced_performance = {
    **performance,
    "success_rate": round(success_rate, 2),
    "failure_rate": round(100 - success_rate, 2),
    "uptime": status.get("uptime", 0)
    }

    return ApiResponse(
    success=True,
    message="性能指标获取成功",
    data=enhanced_performance
    )

    except Exception as e:
    logger.error(f"获取性能指标失败: {e}")
    return ApiResponse(
    success=False,
    message=f"获取性能指标失败: {str(e)}"
    )

@router.post("/health-check", response_model=ApiResponse)
async def perform_health_check():
    """执行健康检查"""
    try:
    # 执行健康检查
    await environment_manager._perform_health_check()

    # 获取最新状态
    status = environment_manager.get_environment_status()

    # 分析健康状态
    roles = status.get("roles", {})
    healthy_count = len([r for r in roles.values() if r["status"] == "healthy"])
    total_count = len(roles)

    health_status = {
    "overall_health": "healthy" if healthy_count == total_count else "degraded",
    "healthy_roles": healthy_count,
    "total_roles": total_count,
    "health_percentage": round((healthy_count / total_count * 100) if total_count > 0 else 0, 2),
    "manager_status": status.get("manager_status"),
    "environment_status": status.get("environment", {}).get("state"),
    "check_time": datetime.now().isoformat()
    }

    return ApiResponse(
    success=True,
    message="健康检查完成",
    data=health_status
    )

    except Exception as e:
    logger.error(f"健康检查失败: {e}")
    return ApiResponse(
    success=False,
    message=f"健康检查失败: {str(e)}"
    )

@router.post("/shutdown", response_model=ApiResponse)
async def shutdown_environment():
    """关闭环境管理器"""
    try:
    logger.info("开始关闭环境管理器")

    await environment_manager.shutdown()

    return ApiResponse(
    success=True,
    message="环境管理器已关闭"
    )

    except Exception as e:
    logger.error(f"关闭环境管理器失败: {e}")
    return ApiResponse(
    success=False,
    message=f"关闭环境管理器失败: {str(e)}"
    )

@router.get("/config", response_model=ApiResponse)
async def get_environment_config():
    """获取环境配置"""
    try:
    status = environment_manager.get_environment_status()
    config = status.get("config", {})

    return ApiResponse(
    success=True,
    message="环境配置获取成功",
    data=config
    )

    except Exception as e:
    logger.error(f"获取环境配置失败: {e}")
    return ApiResponse(
    success=False,
    message=f"获取环境配置失败: {str(e)}"
    )

@router.get("/workflow-coordinators", response_model=ApiResponse)
async def get_workflow_coordinators():
    """获取工作流协调器状态"""
    try:
    status = environment_manager.get_environment_status()
    coordinators = status.get("workflow_coordinators", [])

    coordinator_info = {
    "coordinators": coordinators,
    "total_coordinators": len(coordinators),
    "main_coordinator_available": "main_coordinator" in coordinators
    }

    return ApiResponse(
    success=True,
    message="工作流协调器状态获取成功",
    data=coordinator_info
    )

    except Exception as e:
    logger.error(f"获取工作流协调器状态失败: {e}")
    return ApiResponse(
    success=False,
    message=f"获取工作流协调器状态失败: {str(e)}"
    )
