#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度修复验证测试
验证所有深度修复是否成功
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_backend_services():
    """测试backend.services模块"""
    try:
        logger.info("🔧 测试backend.services模块...")
        
        from backend.services import multi_source_data_manager
        
        if multi_source_data_manager:
            logger.info("✅ multi_source_data_manager导入成功")
            return True
        else:
            logger.error("❌ multi_source_data_manager为None")
            return False
            
    except Exception as e:
        logger.error(f"❌ backend.services测试失败: {e}")
        return False

async def test_core_domain():
    """测试core.domain模块"""
    try:
        logger.info("🏗️ 测试core.domain模块...")
        
        # 测试传奇记忆系统
        from core.domain.memory.legendary.interface import legendary_memory_interface
        logger.info("✅ 传奇记忆系统导入成功")
        
        # 测试绩效监控系统
        from core.performance.star_performance_monitor import star_performance_monitor
        logger.info("✅ 绩效监控系统导入成功")
        
        # 测试层级权限系统
        from core.enhanced_seven_stars_hierarchy import enhanced_seven_stars_hierarchy
        logger.info("✅ 层级权限系统导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ core.domain测试失败: {e}")
        return False

async def test_deepseek_service():
    """测试deepseek_service"""
    try:
        logger.info("🤖 测试deepseek_service...")
        
        from shared.infrastructure import deepseek_service
        
        if deepseek_service:
            logger.info("✅ deepseek_service导入成功")
            return True
        else:
            logger.error("❌ deepseek_service为None")
            return False
            
    except Exception as e:
        logger.error(f"❌ deepseek_service测试失败: {e}")
        return False

async def test_tianshu_core_service():
    """测试天枢星核心服务"""
    try:
        logger.info("⭐ 测试天枢星核心服务...")
        
        from roles.tianshu_star.services.tianshu_core_service import tianshu_core_service
        
        # 测试get_stock_basic_info方法
        if hasattr(tianshu_core_service, 'get_stock_basic_info'):
            logger.info("✅ get_stock_basic_info方法存在")
            
            # 测试方法调用
            result = await tianshu_core_service.get_stock_basic_info("000001.XSHE")
            if result and result.get("stock_code") == "000001.XSHE":
                logger.info(f"✅ get_stock_basic_info方法正常工作: {result.get('company_name')}")
                return True
            else:
                logger.error("❌ get_stock_basic_info方法返回异常")
                return False
        else:
            logger.error("❌ get_stock_basic_info方法不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 天枢星核心服务测试失败: {e}")
        return False

async def test_yaoguang_core_systems():
    """测试瑶光星核心系统"""
    try:
        logger.info("🌟 测试瑶光星核心系统...")
        
        from roles.yaoguang_star.core.core_systems_integration import YaoguangCoreSystemsIntegration
        
        # 创建实例
        core_systems = YaoguangCoreSystemsIntegration()
        
        # 检查deepseek_service属性
        if hasattr(core_systems, 'deepseek_service'):
            logger.info("✅ deepseek_service属性存在")
            return True
        else:
            logger.error("❌ deepseek_service属性不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 瑶光星核心系统测试失败: {e}")
        return False

async def test_automation_systems_imports():
    """测试自动化系统导入"""
    try:
        logger.info("🤖 测试自动化系统导入...")
        
        success_count = 0
        total_count = 4
        
        # 测试天枢星自动化系统
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            logger.info("✅ 天枢星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天枢星自动化系统导入失败: {e}")
        
        # 测试天玑星自动化系统
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            logger.info("✅ 天玑星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天玑星自动化系统导入失败: {e}")
        
        # 测试天璇星自动化系统
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            logger.info("✅ 天璇星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天璇星自动化系统导入失败: {e}")
        
        # 测试玉衡星自动化系统
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            logger.info("✅ 玉衡星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 玉衡星自动化系统导入失败: {e}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"📊 自动化系统导入成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"❌ 自动化系统导入测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始深度修复验证测试")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 测试1：backend.services模块
    test_results["backend_services"] = await test_backend_services()
    
    # 测试2：core.domain模块
    test_results["core_domain"] = await test_core_domain()
    
    # 测试3：deepseek_service
    test_results["deepseek_service"] = await test_deepseek_service()
    
    # 测试4：天枢星核心服务
    test_results["tianshu_core_service"] = await test_tianshu_core_service()
    
    # 测试5：瑶光星核心系统
    test_results["yaoguang_core_systems"] = await test_yaoguang_core_systems()
    
    # 测试6：自动化系统导入
    test_results["automation_systems"] = await test_automation_systems_imports()
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 深度修复验证结果:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"📈 深度修复成功率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        logger.info("🎉 所有深度修复完成，系统完全正常！")
    elif success_rate >= 80:
        logger.info("✅ 主要深度修复完成，系统基本正常！")
    else:
        logger.info("⚠️ 深度修复不完整，需要进一步处理")
    
    return success_rate

if __name__ == "__main__":
    asyncio.run(main())
