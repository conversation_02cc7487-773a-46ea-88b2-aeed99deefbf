#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API请求模型
定义所有API端点的请求参数模型
"""

from pydantic import BaseModel, Field
from typing import Any, Dict, List, Optional, Union
from .common import BaseRequest

# ==================== 角色任务请求模型 ====================

class RoleTaskRequest(BaseRequest):
    """角色任务请求"""
    role: str
    task_type: str
    parameters: Dict[str, Any] = Field(default_factory=dict)

# ==================== 情报官请求模型 ====================

class NewsCollectionRequest(BaseRequest):
    """新闻收集请求"""
    sources: List[str] = Field(default_factory=lambda: ["财经网", "新浪财经"])
    keywords: List[str] = Field(default_factory=lambda: ["AI", "科技股"])
    time_range: Optional[str] = "24h"
    max_articles: Optional[int] = 100
    output_path: Optional[str] = "./output/news"

class SentimentAnalysisRequest(BaseRequest):
    """情绪分析请求"""
    time_range: str = "24h"
    analysis_type: str = "market"
    data_sources: Optional[List[str]] = None
    sentiment_model: Optional[str] = "bert_chinese"
    output_path: Optional[str] = "./output/sentiment"

class WebCrawlRequest(BaseRequest):
    """网络爬取请求"""
    urls: List[str]
    crawl_type: str = "news" # news, financial, research
    keywords: Optional[List[str]] = None
    data_type: Optional[str] = None
    output_path: Optional[str] = "./output/crawl"

class FinancialSentimentRequest(BaseRequest):
    """金融情感分析请求"""
    text_data: Union[str, List[str]]
    model_name: str = "disc-finllm"

class NewsClassificationRequest(BaseRequest):
    """新闻分类请求"""
    news_data: Union[str, List[str]]
    classification_type: str = "category"
    model_name: str = "disc-finllm"

class NegativeNewsDetectionRequest(BaseRequest):
    """负面新闻检测请求"""
    news_data: Union[str, List[str]]
    threshold: float = 0.5
    model_name: str = "disc-finllm"

# ==================== 架构师请求模型 ====================

class FactorRequest(BaseRequest):
    """因子研究请求"""
    dataset: str
    start_date: str
    end_date: str
    universe: List[str]
    factors: Optional[List[str]] = None
    benchmark: Optional[str] = "000300.SH"

class ModelRequest(BaseRequest):
    """模型研究请求"""
    model_type: str
    dataset: str
    start_date: str
    end_date: str
    features: List[str]
    target: str
    split_ratio: float = 0.8

class FactorResearchRequest(BaseRequest):
    """因子研究请求"""
    research_idea: str
    data_path: str = "./data"
    output_path: str = "./output/factor_research"

class ModelResearchRequest(BaseRequest):
    """模型研究请求"""
    research_idea: str
    data_path: str = "./data"
    output_path: str = "./output/model_research"

class ReportFactorRequest(BaseRequest):
    """报告因子提取请求"""
    report_path: str
    output_path: str = "./output/report_factors"

class ModelBenchmarkRequest(BaseRequest):
    """模型基准测试请求"""
    model_name: str
    benchmark_type: str = "performance"
    data_path: str = "./data"

class ComprehensiveEvaluationRequest(BaseRequest):
    """综合评估请求"""
    evaluation_type: str = "financial"
    data_path: str = "./data"
    output_path: str = "./output/evaluation"

# ==================== 交易相关请求模型 ====================

class TradeExecutionRequest(BaseRequest):
    """交易执行请求"""
    symbol: str
    action: str # buy, sell
    quantity: int
    price: Optional[float] = None
    order_type: str = "market" # market, limit

class RiskAssessmentRequest(BaseRequest):
    """风险评估请求"""
    portfolio: Dict[str, Any]
    assessment_type: str = "comprehensive"

class ClientQueryRequest(BaseRequest):
    """客户查询请求"""
    query: str
    query_type: str = "general"
    client_id: Optional[str] = None

# ==================== 系统管理请求模型 ====================

class SessionCreateRequest(BaseRequest):
    """会话创建请求"""
    role: str
    task_type: str
    parameters: Optional[Dict[str, Any]] = None

class WorkflowRequest(BaseRequest):
    """工作流请求"""
    workflow_type: str
    parameters: Dict[str, Any] = Field(default_factory=dict)

class MonitoringRequest(BaseRequest):
    """监控请求"""
    monitor_type: str = "system"
    duration: Optional[int] = 3600 # 秒
