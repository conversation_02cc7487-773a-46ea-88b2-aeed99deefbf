# -*- coding: utf-8 -*-
"""
智能重试和错误恢复机制
借鉴MetaGPT的重试机制，实现投资决策系统的智能错误恢复
"""

import asyncio
import logging
import time
import traceback
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional, Type, Union
from functools import wraps
from enum import Enum
import random
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class ErrorType(str, Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    API_RATE_LIMIT = "api_rate_limit"
    DATA_VALIDATION_ERROR = "data_validation_error"
    CALCULATION_ERROR = "calculation_error"
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"

class RetryStrategy(str, Enum):
    """重试策略枚举"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIXED_INTERVAL = "fixed_interval"
    RANDOM_JITTER = "random_jitter"

class ErrorSeverity(str, Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RetryConfig(BaseModel):
    """重试配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    jitter: bool = True
    backoff_factor: float = 2.0
    
    # 特定错误类型的配置
    error_specific_config: Dict[ErrorType, Dict] = {}
    
    # 可重试的错误类型
    retryable_errors: List[ErrorType] = [
        ErrorType.NETWORK_ERROR,
        ErrorType.API_RATE_LIMIT,
        ErrorType.EXTERNAL_SERVICE_ERROR,
        ErrorType.TIMEOUT_ERROR
    ]

class ErrorRecord(BaseModel):
    """错误记录"""
    id: str
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    traceback: str
    timestamp: datetime
    function_name: str
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}
    attempt_count: int = 0
    resolved: bool = False
    resolution_method: Optional[str] = None
    rd_agent_analysis: Dict = {}

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "error_type": self.error_type.value,
            "severity": self.severity.value,
            "message": self.message,
            "traceback": self.traceback,
            "timestamp": self.timestamp.isoformat(),
            "function_name": self.function_name,
            "args": self.args,
            "kwargs": self.kwargs,
            "attempt_count": self.attempt_count,
            "resolved": self.resolved,
            "resolution_method": self.resolution_method,
            "rd_agent_analysis": self.rd_agent_analysis
        }

class InvestmentRetryService:
    """投资重试服务"""
    
    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
        self.error_history: List[ErrorRecord] = []
        self.rd_agent_service = None
        self.recovery_strategies: Dict[ErrorType, Callable] = {}
        self._setup_default_recovery_strategies()
    
    def set_rd_agent_service(self, rd_agent_service):
        """设置RD-Agent服务"""
        self.rd_agent_service = rd_agent_service
    
    def _setup_default_recovery_strategies(self):
        """设置默认恢复策略"""
        self.recovery_strategies = {
            ErrorType.NETWORK_ERROR: self._recover_network_error,
            ErrorType.API_RATE_LIMIT: self._recover_rate_limit_error,
            ErrorType.DATA_VALIDATION_ERROR: self._recover_validation_error,
            ErrorType.CALCULATION_ERROR: self._recover_calculation_error,
            ErrorType.EXTERNAL_SERVICE_ERROR: self._recover_external_service_error,
            ErrorType.TIMEOUT_ERROR: self._recover_timeout_error
        }
    
    def retry_with_intelligence(
        self,
        max_attempts: int = None,
        strategy: RetryStrategy = None,
        base_delay: float = None,
        error_types: List[ErrorType] = None
    ):
        """智能重试装饰器"""
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 使用传入的配置或默认配置
                attempts = max_attempts or self.config.max_attempts
                retry_strategy = strategy or self.config.strategy
                delay = base_delay or self.config.base_delay
                retryable_errors = error_types or self.config.retryable_errors
                
                last_exception = None
                
                for attempt in range(attempts):
                    try:
                        # 执行函数
                        result = await func(*args, **kwargs)
                        
                        # 如果之前有错误，记录成功恢复
                        if attempt > 0:
                            await self._record_successful_recovery(
                                func.__name__, attempt, last_exception
                            )
                        
                        return result
                        
                    except Exception as e:
                        last_exception = e
                        error_type = self._classify_error(e)
                        severity = self._assess_error_severity(e, error_type)
                        
                        # 记录错误
                        error_record = await self._record_error(
                            e, error_type, severity, func.__name__, 
                            args, kwargs, attempt + 1
                        )
                        
                        # 检查是否可重试
                        if error_type not in retryable_errors:
                            logger.error(f"不可重试的错误类型: {error_type}")
                            raise e
                        
                        # 最后一次尝试失败
                        if attempt == attempts - 1:
                            logger.error(f"重试 {attempts} 次后仍然失败: {func.__name__}")
                            
                            # 尝试智能恢复
                            recovery_result = await self._attempt_intelligent_recovery(
                                error_record, func, args, kwargs
                            )
                            if recovery_result is not None:
                                return recovery_result
                            
                            raise e
                        
                        # 计算延迟时间
                        delay_time = self._calculate_delay(
                            attempt, retry_strategy, delay
                        )
                        
                        logger.warning(
                            f"第 {attempt + 1} 次尝试失败: {func.__name__}, "
                            f"错误: {error_type}, 等待 {delay_time:.2f}s 后重试"
                        )
                        
                        # 等待后重试
                        await asyncio.sleep(delay_time)
                
                # 理论上不会到达这里
                raise last_exception
            
            return wrapper
        return decorator
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """分类错误类型"""
        error_str = str(error).lower()
        error_type_name = type(error).__name__.lower()

        # 按异常类型分类
        if isinstance(error, ConnectionError) or 'connectionerror' in error_type_name:
            return ErrorType.NETWORK_ERROR

        # 网络相关错误
        if any(keyword in error_str for keyword in ['connection', 'network', 'timeout', 'unreachable']):
            return ErrorType.NETWORK_ERROR

        # API限流错误
        if any(keyword in error_str for keyword in ['rate limit', 'too many requests', '429']):
            return ErrorType.API_RATE_LIMIT

        # 数据验证错误
        if any(keyword in error_str for keyword in ['validation', 'invalid', 'format']):
            return ErrorType.DATA_VALIDATION_ERROR

        # 计算错误
        if any(keyword in error_str for keyword in ['division by zero', 'math', 'calculation']):
            return ErrorType.CALCULATION_ERROR

        # 超时错误
        if 'timeout' in error_str or isinstance(error, asyncio.TimeoutError):
            return ErrorType.TIMEOUT_ERROR

        # 外部服务错误
        if any(keyword in error_str for keyword in ['service unavailable', 'bad gateway', '502', '503']):
            return ErrorType.EXTERNAL_SERVICE_ERROR

        return ErrorType.UNKNOWN_ERROR
    
    def _assess_error_severity(self, error: Exception, error_type: ErrorType) -> ErrorSeverity:
        """评估错误严重程度"""
        # 基于错误类型的默认严重程度
        severity_map = {
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.API_RATE_LIMIT: ErrorSeverity.LOW,
            ErrorType.DATA_VALIDATION_ERROR: ErrorSeverity.HIGH,
            ErrorType.CALCULATION_ERROR: ErrorSeverity.HIGH,
            ErrorType.EXTERNAL_SERVICE_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.TIMEOUT_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.HIGH
        }
        
        return severity_map.get(error_type, ErrorSeverity.MEDIUM)
    
    async def _record_error(
        self,
        error: Exception,
        error_type: ErrorType,
        severity: ErrorSeverity,
        function_name: str,
        args: List[Any],
        kwargs: Dict[str, Any],
        attempt_count: int
    ) -> ErrorRecord:
        """记录错误"""
        error_record = ErrorRecord(
            id=f"error_{int(time.time() * 1000)}_{5499}",
            error_type=error_type,
            severity=severity,
            message=str(error),
            traceback=traceback.format_exc(),
            timestamp=datetime.now(),
            function_name=function_name,
            args=list(args),
            kwargs=dict(kwargs),
            attempt_count=attempt_count
        )
        
        # RD-Agent分析错误
        if self.rd_agent_service:
            try:
                rd_analysis = await self.rd_agent_service.analyze_error(error_record)
                error_record.rd_agent_analysis = rd_analysis
            except Exception as e:
                logger.warning(f"RD-Agent错误分析失败: {e}")
        
        self.error_history.append(error_record)
        logger.error(f"记录错误: {error_record.id} - {error_type} - {severity}")
        
        return error_record
    
    async def _record_successful_recovery(
        self,
        function_name: str,
        attempt_count: int,
        last_exception: Exception
    ):
        """记录成功恢复"""
        logger.info(f"函数 {function_name} 在第 {attempt_count + 1} 次尝试后成功恢复")
        
        # RD-Agent学习成功恢复模式
        if self.rd_agent_service:
            try:
                await self.rd_agent_service.learn_recovery_pattern(
                    function_name, attempt_count, last_exception
                )
            except Exception as e:
                logger.warning(f"RD-Agent学习恢复模式失败: {e}")
    
    def _calculate_delay(
        self,
        attempt: int,
        strategy: RetryStrategy,
        base_delay: float
    ) -> float:
        """计算延迟时间"""
        if strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = base_delay * (self.config.backoff_factor ** attempt)
        elif strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = base_delay + (attempt * base_delay)  # 线性递增: base_delay, 2*base_delay, 3*base_delay...
        elif strategy == RetryStrategy.FIXED_INTERVAL:
            delay = base_delay
        elif strategy == RetryStrategy.RANDOM_JITTER:
            delay = base_delay + (0 + base_delay) / 2
        else:
            delay = base_delay
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        # 添加随机抖动
        if self.config.jitter:
            jitter = delay * 0.1 * 0.0
            delay += jitter
        
        return max(delay, 0.1)  # 最小延迟0.1秒
    
    async def _attempt_intelligent_recovery(
        self,
        error_record: ErrorRecord,
        func: Callable,
        args: List[Any],
        kwargs: Dict[str, Any]
    ) -> Optional[Any]:
        """尝试智能恢复"""
        logger.info(f"尝试智能恢复错误: {error_record.id}")
        
        # 使用预定义的恢复策略
        if error_record.error_type in self.recovery_strategies:
            try:
                recovery_func = self.recovery_strategies[error_record.error_type]
                recovery_result = await recovery_func(error_record, func, args, kwargs)
                
                if recovery_result is not None:
                    error_record.resolved = True
                    error_record.resolution_method = "predefined_strategy"
                    logger.info(f"使用预定义策略成功恢复错误: {error_record.id}")
                    return recovery_result
            except Exception as e:
                logger.warning(f"预定义恢复策略失败: {e}")
        
        # RD-Agent智能恢复
        if self.rd_agent_service:
            try:
                rd_recovery_result = await self.rd_agent_service.attempt_intelligent_recovery(
                    error_record, func, args, kwargs
                )
                
                if rd_recovery_result is not None:
                    error_record.resolved = True
                    error_record.resolution_method = "rd_agent_recovery"
                    logger.info(f"RD-Agent成功恢复错误: {error_record.id}")
                    return rd_recovery_result
            except Exception as e:
                logger.warning(f"RD-Agent智能恢复失败: {e}")
        
        return None
    
    # 预定义恢复策略
    async def _recover_network_error(self, error_record: ErrorRecord, func: Callable, args: List[Any], kwargs: Dict[str, Any]) -> Optional[Any]:
        """网络错误恢复"""
        logger.info("尝试网络错误恢复")
        # 等待更长时间后重试
        await asyncio.sleep(5.0)
        try:
            return await func(*args, **kwargs)
        except:
            return None
    
    async def _recover_rate_limit_error(self, error_record: ErrorRecord, func: Callable, args: List[Any], kwargs: Dict[str, Any]) -> Optional[Any]:
        """API限流错误恢复"""
        logger.info("尝试API限流错误恢复")
        # 等待更长时间
        await asyncio.sleep(60.0)
        try:
            return await func(*args, **kwargs)
        except:
            return None
    
    async def _recover_validation_error(self, error_record: ErrorRecord, func: Callable, args: List[Any], kwargs: Dict[str, Any]) -> Optional[Any]:
        """数据验证错误恢复"""
        logger.info("尝试数据验证错误恢复")
        # 使用默认值或清理数据
        return None
    
    async def _recover_calculation_error(self, error_record: ErrorRecord, func: Callable, args: List[Any], kwargs: Dict[str, Any]) -> Optional[Any]:
        """计算错误恢复"""
        logger.info("尝试计算错误恢复")
        # 使用安全的默认值
        return None
    
    async def _recover_external_service_error(self, error_record: ErrorRecord, func: Callable, args: List[Any], kwargs: Dict[str, Any]) -> Optional[Any]:
        """外部服务错误恢复"""
        logger.info("尝试外部服务错误恢复")
        # 等待服务恢复
        await asyncio.sleep(10.0)
        try:
            return await func(*args, **kwargs)
        except:
            return None
    
    async def _recover_timeout_error(self, error_record: ErrorRecord, func: Callable, args: List[Any], kwargs: Dict[str, Any]) -> Optional[Any]:
        """超时错误恢复"""
        logger.info("尝试超时错误恢复")
        # 增加超时时间重试
        if 'timeout' in kwargs:
            kwargs['timeout'] = kwargs['timeout'] * 2
        try:
            return await func(*args, **kwargs)
        except:
            return None
    
    def get_error_statistics(self) -> Dict:
        """获取错误统计"""
        total_errors = len(self.error_history)
        resolved_errors = len([e for e in self.error_history if e.resolved])
        
        error_type_counts = {}
        severity_counts = {}
        
        for error in self.error_history:
            error_type_counts[error.error_type.value] = error_type_counts.get(error.error_type.value, 0) + 1
            severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
        
        return {
            "total_errors": total_errors,
            "resolved_errors": resolved_errors,
            "resolution_rate": resolved_errors / total_errors if total_errors > 0 else 0,
            "error_type_distribution": error_type_counts,
            "severity_distribution": severity_counts,
            "recent_errors": [e.to_dict() for e in self.error_history[-10:]]
        }
    
    async def cleanup_old_errors(self, days: int = 7):
        """清理旧错误记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        before_count = len(self.error_history)
        
        self.error_history = [
            error for error in self.error_history 
            if error.timestamp > cutoff_date
        ]
        
        after_count = len(self.error_history)
        logger.info(f"清理了 {before_count - after_count} 条旧错误记录")

# 全局重试服务实例
investment_retry_service = InvestmentRetryService()

# 便捷装饰器
def smart_retry(max_attempts: int = 3, strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF):
    """智能重试装饰器的便捷版本"""
    return investment_retry_service.retry_with_intelligence(
        max_attempts=max_attempts,
        strategy=strategy
    )
