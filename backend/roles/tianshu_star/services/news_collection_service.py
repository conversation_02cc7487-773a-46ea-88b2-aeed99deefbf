#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星新闻收集服务 - 精简版真实服务
专注核心功能，移除重复代码和模拟API
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class NewsCollectionService:
    """天枢星新闻收集服务 - 精简版"""
    
    def __init__(self):
        self.service_name = "NewsCollectionService"
        self.version = "3.0.0"
        
        # 初始化新闻收集器
        self.news_crawler = None
        self._initialize_crawler()
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _initialize_crawler(self):
        """初始化新闻爬虫"""
        try:
            # 修复导入路径问题
            import sys
            import os

            # 确保backend目录在Python路径中
            backend_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            if backend_path not in sys.path:
                sys.path.insert(0, backend_path)

            from shared.news_sources.professional_news_crawler import professional_news_crawler
            self.news_crawler = professional_news_crawler
            logger.info("✅ 专业新闻爬虫初始化成功")
        except ImportError as e:
            logger.error(f"❌ 专业新闻爬虫初始化失败: {e}")
            self.news_crawler = None
    
    async def collect_stock_news(self, symbol: str, limit: int = 10) -> Dict[str, Any]:
        """收集股票新闻"""
        try:
            if not self.news_crawler:
                return {
                    "success": False,
                    "error": "新闻爬虫不可用",
                    "symbol": symbol,
                    "news_count": 0,
                    "news_data": []
                }
            
            logger.info(f"🔍 开始收集股票 {symbol} 的新闻")
            
            # 获取股票信息
            try:
                from shared.utils.stock_code_converter import stock_converter
                stock_info = stock_converter.get_stock_info(symbol)
                stock_name = stock_info.get('name', f'股票{symbol}')
            except ImportError:
                # 备用方案
                stock_name = f'股票{symbol}'
            
            # 使用专业新闻爬虫收集新闻
            async with self.news_crawler as crawler:
                result = await crawler.crawl_stock_news(symbol, stock_name, limit)
                
                if result.get("success"):
                    news_data = result.get("news_data", [])
                    logger.info(f"✅ 成功收集股票 {symbol} 新闻: {len(news_data)}条")
                    
                    return {
                        "success": True,
                        "symbol": symbol,
                        "stock_name": stock_name,
                        "news_count": len(news_data),
                        "news_data": news_data,
                        "collection_time": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"⚠️ 股票 {symbol} 新闻收集失败: {result.get('error', '未知错误')}")
                    return {
                        "success": False,
                        "error": result.get('error', '新闻收集失败'),
                        "symbol": symbol,
                        "news_count": 0,
                        "news_data": []
                    }
                    
        except Exception as e:
            logger.error(f"❌ 收集股票 {symbol} 新闻异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "news_count": 0,
                "news_data": []
            }
    
    async def collect_market_news(self, limit: int = 20) -> Dict[str, Any]:
        """收集市场新闻"""
        try:
            if not self.news_crawler:
                return {
                    "success": False,
                    "error": "新闻爬虫不可用",
                    "news_count": 0,
                    "market_news": []
                }
            
            logger.info(f"🔍 开始收集市场新闻")
            
            # 使用专业新闻爬虫收集市场新闻
            async with self.news_crawler as crawler:
                result = await crawler.crawl_market_news(limit)
                
                if result.get("success"):
                    news_data = result.get("news_data", [])
                    logger.info(f"✅ 成功收集市场新闻: {len(news_data)}条")
                    
                    return {
                        "success": True,
                        "news_count": len(news_data),
                        "market_news": news_data,
                        "collection_time": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"⚠️ 市场新闻收集失败: {result.get('error', '未知错误')}")
                    return {
                        "success": False,
                        "error": result.get('error', '市场新闻收集失败'),
                        "news_count": 0,
                        "market_news": []
                    }
                    
        except Exception as e:
            logger.error(f"❌ 收集市场新闻异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "news_count": 0,
                "market_news": []
            }
    
    async def get_latest_news(self, symbols: List[str] = None, limit: int = 10) -> Dict[str, Any]:
        """获取最新新闻"""
        try:
            all_news = []
            
            if symbols:
                # 收集指定股票的新闻
                for symbol in symbols[:3]:  # 限制股票数量
                    result = await self.collect_stock_news(symbol, limit//len(symbols))
                    if result.get("success"):
                        news_data = result.get("news_data", [])
                        # 添加股票标识
                        for news in news_data:
                            news["related_symbol"] = symbol
                        all_news.extend(news_data)
            else:
                # 收集市场新闻
                result = await self.collect_market_news(limit)
                if result.get("success"):
                    all_news = result.get("market_news", [])
            
            # 按时间排序
            sorted_news = sorted(all_news, key=lambda x: x.get('timestamp', ''), reverse=True)
            final_news = sorted_news[:limit]
            
            return {
                "success": True,
                "news_count": len(final_news),
                "news": final_news,
                "symbols": symbols or [],
                "collection_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取最新新闻失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "news_count": 0,
                "news": []
            }
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "news_crawler_available": self.news_crawler is not None,
            "capabilities": [
                "股票新闻收集",
                "市场新闻收集",
                "最新新闻获取"
            ],
            "timestamp": datetime.now().isoformat()
        }

# 全局实例
news_collection_service = NewsCollectionService()

# 向后兼容
professional_news_collection_service = news_collection_service
ProfessionalNewsCollectionService = NewsCollectionService

__all__ = [
    "NewsCollectionService", 
    "news_collection_service", 
    "professional_news_collection_service",
    "ProfessionalNewsCollectionService"
]
