#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间控制引擎
防止未来函数的核心组件，确保回测的真实性和可靠性
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import pandas as pd
import logging

logger = logging.getLogger(__name__)

@dataclass
class LearningSession:
    """学习会话数据结构"""
    session_id: str
    role_id: str
    start_date: str
    end_date: str
    current_time: str
    strategy_type: str
    data_access_log: List[Dict]
    decision_log: List[Dict]
    performance_metrics: Dict
    rd_agent_config: Dict
    status: str = "active"
    created_time: datetime = None

@dataclass
class DataAccessLog:
    """数据访问日志"""
    timestamp: datetime
    session_id: str
    stock_code: str
    data_type: str
    access_time: str
    data_shape: tuple

@dataclass
class TimeBarrier:
    """时间屏障"""
    session_id: str
    barrier_type: str
    max_access_time: str
    constraints: Dict

class TimeViolationError(Exception):
    """时间违规错误"""
    pass

class TimeControlEngine:
    """时间控制引擎 - 防止未来函数的核心"""
    
    def __init__(self):
        self.learning_sessions: Dict[str, LearningSession] = {}
        self.time_barriers: Dict[str, Dict[str, TimeBarrier]] = {}
        self.data_access_permissions: Dict[str, Dict] = {}
        
        # RD-Agent集成
        self.rd_agent_time_manager = None  # 延迟初始化
        
        logger.info("时间控制引擎初始化完成")

    async def set_time_range(self, start_time: str, end_time: str) -> Dict[str, Any]:
        """设置时间范围"""
        try:
            # 验证时间格式
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)

            if start_dt >= end_dt:
                return {
                    "success": False,
                    "error": "开始时间必须早于结束时间"
                }

            # 设置全局时间范围
            self.global_time_range = {
                "start_time": start_time,
                "end_time": end_time,
                "current_time": start_time,
                "set_time": datetime.now().isoformat()
            }

            logger.info(f"时间范围设置成功: {start_time} ~ {end_time}")

            return {
                "success": True,
                "start_time": start_time,
                "end_time": end_time,
                "duration_days": (end_dt - start_dt).days,
                "message": "时间范围设置成功"
            }

        except Exception as e:
            logger.error(f"设置时间范围失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def advance_to_date(self, target_date: str) -> Dict[str, Any]:
        """推进到指定日期"""
        try:
            if not hasattr(self, 'global_time_range'):
                return {
                    "success": False,
                    "error": "未设置时间范围"
                }

            target_dt = pd.to_datetime(target_date)
            start_dt = pd.to_datetime(self.global_time_range["start_time"])
            end_dt = pd.to_datetime(self.global_time_range["end_time"])
            current_dt = pd.to_datetime(self.global_time_range["current_time"])

            # 验证目标日期
            if target_dt < start_dt:
                return {
                    "success": False,
                    "error": "目标日期早于开始时间"
                }

            if target_dt > end_dt:
                return {
                    "success": False,
                    "error": "目标日期晚于结束时间"
                }

            if target_dt < current_dt:
                return {
                    "success": False,
                    "error": "不能回退时间"
                }

            # 更新当前时间
            old_time = self.global_time_range["current_time"]
            self.global_time_range["current_time"] = target_date

            # 更新所有活跃会话的时间
            for session_id, session in self.learning_sessions.items():
                if session.status == "active":
                    session.current_time = target_date

            logger.info(f"时间推进成功: {old_time} -> {target_date}")

            return {
                "success": True,
                "old_time": old_time,
                "current_date": target_date,
                "days_advanced": (target_dt - current_dt).days,
                "message": f"时间推进成功: {target_date}"
            }

        except Exception as e:
            logger.error(f"时间推进失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def set_acceleration(self, acceleration: float) -> Dict[str, Any]:
        """设置时间加速"""
        try:
            if acceleration <= 0:
                return {
                    "success": False,
                    "error": "加速倍数必须大于0"
                }

            if acceleration > 1000:
                return {
                    "success": False,
                    "error": "加速倍数不能超过1000倍"
                }

            # 设置时间加速
            self.time_acceleration = {
                "factor": acceleration,
                "set_time": datetime.now().isoformat(),
                "active": True
            }

            # 更新所有活跃会话的加速设置
            for session_id, session in self.learning_sessions.items():
                if session.status == "active":
                    session.performance_metrics["time_acceleration"] = acceleration

            logger.info(f"时间加速设置成功: {acceleration}x")

            return {
                "success": True,
                "acceleration": acceleration,
                "message": f"时间加速设置为 {acceleration}x"
            }

        except Exception as e:
            logger.error(f"设置时间加速失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_current_time_status(self) -> Dict[str, Any]:
        """获取当前时间状态"""
        try:
            if not hasattr(self, 'global_time_range'):
                return {
                    "success": False,
                    "error": "未设置时间范围"
                }

            time_range = self.global_time_range
            acceleration = getattr(self, 'time_acceleration', {"factor": 1.0, "active": False})

            return {
                "success": True,
                "current_time": time_range["current_time"],
                "start_time": time_range["start_time"],
                "end_time": time_range["end_time"],
                "acceleration": acceleration["factor"],
                "acceleration_active": acceleration["active"],
                "active_sessions": len([s for s in self.learning_sessions.values() if s.status == "active"]),
                "total_sessions": len(self.learning_sessions)
            }

        except Exception as e:
            logger.error(f"获取时间状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def create_learning_session(self,
                                    role_id: str,
                                    start_date: str, 
                                    end_date: str,
                                    strategy_type: str,
                                    rd_agent_config: Dict = None) -> str:
        """为角色创建学习会话"""
        
        session_id = f"{role_id}_{start_date}_{end_date}_{strategy_type}_{uuid.uuid4().hex[:8]}"
        
        # 创建时间控制环境
        session = LearningSession(
            session_id=session_id,
            role_id=role_id,
            start_date=start_date,
            end_date=end_date,
            current_time=start_date,
            strategy_type=strategy_type,
            data_access_log=[],
            decision_log=[],
            performance_metrics={},
            rd_agent_config=rd_agent_config or {},
            created_time=datetime.now()
        )
        
        # 设置时间屏障
        await self._setup_time_barriers(session)
        
        # 初始化RD-Agent实验环境
        if rd_agent_config:
            await self._setup_rd_agent_environment(session, rd_agent_config)
        
        self.learning_sessions[session_id] = session
        logger.info(f"创建学习会话: {session_id}")
        
        return session_id
    
    async def get_historical_data(self,
                                session_id: str,
                                stock_code: str, 
                                data_type: str,
                                lookback_days: int = None) -> pd.DataFrame:
        """获取历史数据 - 严格控制时间边界"""
        
        if session_id not in self.learning_sessions:
            raise ValueError(f"学习会话不存在: {session_id}")
        
        session = self.learning_sessions[session_id]
        current_time = session.current_time
        
        # 时间边界检查
        if not await self._check_time_access_permission(session_id, current_time, data_type):
            raise TimeViolationError(f"时间访问违规: {session_id}, {current_time}")
        
        # 计算数据范围
        if lookback_days:
            start_date = (pd.to_datetime(current_time) - 
                         pd.Timedelta(days=lookback_days)).strftime('%Y-%m-%d')
        else:
            start_date = session.start_date
        
        # 只能获取当前时间之前的数据
        data = await self._fetch_data(
            stock_code=stock_code,
            data_type=data_type,
            start_date=start_date,
            end_date=current_time  # 关键：不能看到未来数据
        )
        
        # 记录数据访问日志
        access_log = DataAccessLog(
            timestamp=datetime.now(),
            session_id=session_id,
            stock_code=stock_code,
            data_type=data_type,
            access_time=current_time,
            data_shape=data.shape if not data.empty else (0, 0)
        )
        session.data_access_log.append(asdict(access_log))
        
        return data
    
    async def advance_time(self, session_id: str, new_time: str):
        """推进时间 - 模拟真实交易环境"""
        
        if session_id not in self.learning_sessions:
            raise ValueError(f"学习会话不存在: {session_id}")
        
        session = self.learning_sessions[session_id]
        old_time = session.current_time
        
        # 时间推进验证
        if pd.to_datetime(new_time) <= pd.to_datetime(old_time):
            raise TimeViolationError(f"时间不能倒退: {old_time} -> {new_time}")
        
        if pd.to_datetime(new_time) > pd.to_datetime(session.end_date):
            raise TimeViolationError(f"时间超出会话范围: {new_time} > {session.end_date}")
        
        session.current_time = new_time
        
        # 触发各角色的决策更新
        await self._notify_roles_time_advance(session_id, new_time)
        
        # 更新RD-Agent实验环境
        if session.rd_agent_config:
            await self._update_rd_agent_time(session_id, new_time)
        
        logger.info(f"时间推进: {session_id} {old_time} -> {new_time}")
    
    async def _setup_time_barriers(self, session: LearningSession):
        """设置时间屏障"""
        
        session_barriers = {}
        
        # 1. 数据访问屏障
        data_barrier = TimeBarrier(
            session_id=session.session_id,
            barrier_type="data_access",
            max_access_time=session.current_time,
            constraints={
                "allowed_data_types": ["price", "volume", "fundamental", "news"],
                "max_lookback_days": 365
            }
        )
        session_barriers["data"] = data_barrier
        
        # 2. 决策时间屏障
        decision_barrier = TimeBarrier(
            session_id=session.session_id,
            barrier_type="decision",
            max_access_time=session.current_time,
            constraints={
                "decision_lag_minutes": 1,
                "execution_lag_minutes": 5
            }
        )
        session_barriers["decision"] = decision_barrier
        
        # 3. 信息传播屏障
        info_barrier = TimeBarrier(
            session_id=session.session_id,
            barrier_type="information",
            max_access_time=session.current_time,
            constraints={
                "news_delay_minutes": 15,
                "announcement_delay_hours": 1
            }
        )
        session_barriers["information"] = info_barrier
        
        self.time_barriers[session.session_id] = session_barriers
        
    async def _check_time_access_permission(self, session_id: str, access_time: str, data_type: str) -> bool:
        """检查时间访问权限"""
        
        if session_id not in self.time_barriers:
            return False
        
        barriers = self.time_barriers[session_id]
        data_barrier = barriers.get("data")
        
        if not data_barrier:
            return False
        
        # 检查数据类型是否允许
        allowed_types = data_barrier.constraints.get("allowed_data_types", [])
        if data_type not in allowed_types:
            return False
        
        # 检查时间是否在允许范围内
        max_time = pd.to_datetime(data_barrier.max_access_time)
        request_time = pd.to_datetime(access_time)
        
        return request_time <= max_time
    
    async def _fetch_data(self, stock_code: str, data_type: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取数据的实际实现"""
        # 这里应该调用实际的数据获取服务
        # 返回真实数据
        pass

    async def _setup_rd_agent_environment(self, session: LearningSession, rd_agent_config: Dict):
        """设置RD-Agent环境"""
        try:
            # 初始化RD-Agent实验环境
            session.rd_agent_config = {
                "experiment_id": f"exp_{session.session_id}",
                "data_path": rd_agent_config.get("data_path", "/data/stock_library"),
                "model_path": rd_agent_config.get("model_path", "/models"),
                "factor_library": rd_agent_config.get("factor_library", "/factors"),
                "learning_rate": rd_agent_config.get("learning_rate", 0.01),
                "batch_size": rd_agent_config.get("batch_size", 32),
                "epochs": rd_agent_config.get("epochs", 100),
                "validation_split": rd_agent_config.get("validation_split", 0.2)
            }

            logger.info(f"RD-Agent环境设置完成: {session.session_id}")

        except Exception as e:
            logger.error(f"设置RD-Agent环境失败: {e}")
            raise

    async def _setup_time_barriers(self, session: LearningSession):
        """设置时间屏障"""
        try:
            # 设置时间控制屏障
            session.time_barriers = {
                "news_delay": 15,  # 新闻延迟15分钟
                "decision_lag": 1,  # 决策延迟1分钟
                "execution_lag": 5,  # 执行延迟5分钟
                "strict_mode": True  # 严格模式
            }

            logger.info(f"时间屏障设置完成: {session.session_id}")

        except Exception as e:
            logger.error(f"设置时间屏障失败: {e}")
            raise

    async def start_learning_session(self, session_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动学习会话"""
        try:
            session_type = config.get("session_type", "learning")
            estimated_duration = config.get("estimated_duration", 1800)
            max_duration = config.get("max_duration", 3600)

            # 创建学习会话记录
            session_record = {
                "session_id": session_id,
                "session_type": session_type,
                "start_time": datetime.now().isoformat(),
                "estimated_duration": estimated_duration,
                "max_duration": max_duration,
                "status": "active"
            }

            # 存储会话记录
            if not hasattr(self, 'active_sessions'):
                self.active_sessions = {}

            self.active_sessions[session_id] = session_record

            logger.info(f"⏰ 时间控制引擎启动学习会话: {session_id}")

            return {
                "success": True,
                "session_id": session_id,
                "start_time": session_record["start_time"],
                "estimated_duration": estimated_duration
            }

        except Exception as e:
            logger.error(f"启动学习会话失败: {e}")
            return {"success": False, "error": str(e)}

    async def _notify_roles_time_advance(self, session_id: str, new_time: str):
        """通知角色时间推进"""
        # 实现角色通知逻辑
        pass

    async def _update_rd_agent_time(self, session_id: str, new_time: str):
        """更新RD-Agent时间"""
        # 实现RD-Agent时间更新逻辑
        pass

# 全局时间控制引擎实例
time_control_engine = TimeControlEngine()