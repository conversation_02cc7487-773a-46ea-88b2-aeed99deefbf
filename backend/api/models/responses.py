#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API响应模型
定义所有API端点的响应数据模型
"""

from pydantic import BaseModel
from typing import Any, Dict, List, Optional
from datetime import datetime
from .common import ApiResponse, TaskStatus, SessionInfo

# ==================== 任务响应模型 ====================

class TaskResponse(BaseModel):
    """任务响应"""
    task_id: str
    task_type: str
    role: str
    status: str
    message: str
    result: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None

class SessionResponse(BaseModel):
    """会话响应"""
    session_id: str
    status: str
    role: str
    created_at: datetime
    last_activity: datetime
    tasks: List[TaskStatus] = []

# ==================== 分析响应模型 ====================

class AnalysisResponse(BaseModel):
    """分析响应"""
    analysis_id: str
    analysis_type: str
    result: Dict[str, Any]
    confidence: float
    timestamp: datetime

class MarketScanResponse(BaseModel):
    """市场扫描响应"""
    scan_id: str
    market_status: str
    key_findings: List[str]
    recommendations: List[str]
    risk_level: str
    timestamp: datetime

class SentimentAnalysisResponse(BaseModel):
    """情绪分析响应"""
    analysis_id: str
    overall_sentiment: str
    sentiment_score: float
    confidence: float
    key_topics: List[Dict[str, Any]]
    timestamp: datetime

class NewsCollectionResponse(BaseModel):
    """新闻收集响应"""
    collection_id: str
    sources: List[str]
    articles_collected: int
    keywords_matched: List[str]
    time_range: str
    status: str

class FactorResearchResponse(BaseModel):
    """因子研究响应"""
    research_id: str
    factors_generated: int
    top_factors: List[Dict[str, Any]]
    performance_metrics: Dict[str, float]
    recommendations: List[str]

class ModelResearchResponse(BaseModel):
    """模型研究响应"""
    research_id: str
    models_tested: int
    best_model: Dict[str, Any]
    performance_comparison: Dict[str, Any]
    recommendations: List[str]

class RiskAssessmentResponse(BaseModel):
    """风险评估响应"""
    assessment_id: str
    risk_level: str
    risk_score: float
    risk_factors: List[Dict[str, Any]]
    recommendations: List[str]
    compliance_status: str

class TradeExecutionResponse(BaseModel):
    """交易执行响应"""
    execution_id: str
    symbol: str
    action: str
    quantity: int
    executed_price: float
    status: str
    execution_time: datetime

class SystemStatusResponse(BaseModel):
    """系统状态响应"""
    system_health: str
    active_sessions: int
    running_tasks: int
    mcp_connected: bool
    services_status: Dict[str, str]
    timestamp: datetime
