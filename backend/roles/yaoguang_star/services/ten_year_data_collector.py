#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十年历史数据收集器
专门用于收集股票10年历史数据的优化服务
"""

import asyncio
import aiohttp
import logging
import sqlite3
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tu<PERSON>
from pathlib import Path
import pandas as pd
import time

logger = logging.getLogger(__name__)

class TenYearDataCollector:
    """十年历史数据收集器"""
    
    def __init__(self):
        self.db_path = Path("data/complete_a_stock_library/complete_a_stock_data.db")
        self.session = None
        self.collection_stats = {
            "total_stocks": 0,
            "successful_stocks": 0,
            "failed_stocks": 0,
            "total_records": 0,
            "start_time": None,
            "end_time": None
        }
        
        logger.info("十年历史数据收集器初始化完成")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Referer': 'http://quote.eastmoney.com/',
                'Cache-Control': 'no-cache'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def collect_ten_year_data(self, stock_codes: List[str] = None, 
                                  batch_size: int = 10,
                                  delay_between_batches: float = 2.0) -> Dict[str, Any]:
        """收集10年历史数据"""
        
        try:
            self.collection_stats["start_time"] = datetime.now()
            logger.info("🚀 开始收集10年历史数据...")
            
            # 如果没有指定股票代码，获取数据库中的所有股票
            if not stock_codes:
                stock_codes = await self._get_all_stocks_from_db()
                if not stock_codes:
                    return {
                        "success": False,
                        "error": "无法获取股票列表",
                        "stats": self.collection_stats
                    }
            
            self.collection_stats["total_stocks"] = len(stock_codes)
            logger.info(f"📊 准备收集 {len(stock_codes)} 只股票的10年历史数据")
            
            # 分批处理股票
            successful_stocks = []
            failed_stocks = []
            
            for i in range(0, len(stock_codes), batch_size):
                batch = stock_codes[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (len(stock_codes) + batch_size - 1) // batch_size
                
                logger.info(f"📦 处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 只股票")
                
                # 并发处理当前批次
                batch_tasks = [
                    self._collect_single_stock_ten_year_data(stock_code)
                    for stock_code in batch
                ]
                
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # 统计批次结果
                for j, result in enumerate(batch_results):
                    stock_code = batch[j]
                    
                    if isinstance(result, Exception):
                        logger.error(f"❌ {stock_code} 收集异常: {result}")
                        failed_stocks.append({"stock_code": stock_code, "error": str(result)})
                    elif result[0]:  # success
                        successful_stocks.append({
                            "stock_code": stock_code, 
                            "records": result[1],
                            "data_range": result[2]
                        })
                        self.collection_stats["total_records"] += result[1]
                    else:
                        failed_stocks.append({"stock_code": stock_code, "error": result[3]})
                
                # 批次间延迟
                if i + batch_size < len(stock_codes):
                    logger.info(f"⏱️ 批次间休息 {delay_between_batches} 秒...")
                    await asyncio.sleep(delay_between_batches)
            
            # 更新统计信息
            self.collection_stats["successful_stocks"] = len(successful_stocks)
            self.collection_stats["failed_stocks"] = len(failed_stocks)
            self.collection_stats["end_time"] = datetime.now()
            
            # 计算耗时
            duration = self.collection_stats["end_time"] - self.collection_stats["start_time"]
            
            logger.info(f"✅ 10年历史数据收集完成!")
            logger.info(f"📊 统计结果:")
            logger.info(f"   - 总股票数: {self.collection_stats['total_stocks']}")
            logger.info(f"   - 成功收集: {self.collection_stats['successful_stocks']}")
            logger.info(f"   - 失败股票: {self.collection_stats['failed_stocks']}")
            logger.info(f"   - 总记录数: {self.collection_stats['total_records']}")
            logger.info(f"   - 耗时: {duration}")
            
            return {
                "success": True,
                "stats": self.collection_stats,
                "successful_stocks": successful_stocks,
                "failed_stocks": failed_stocks,
                "duration": str(duration),
                "message": f"成功收集 {len(successful_stocks)} 只股票的10年历史数据"
            }
            
        except Exception as e:
            logger.error(f"收集10年历史数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stats": self.collection_stats
            }
    
    async def _collect_single_stock_ten_year_data(self, stock_code: str) -> Tuple[bool, int, str, str]:
        """收集单只股票的10年历史数据（优先使用本地数据库）"""

        try:
            # 优先从本地数据库获取数据
            local_result = await self._get_local_stock_data(stock_code)
            if local_result[0]:  # 如果本地有数据
                logger.info(f"✅ {stock_code}: 从本地库获取 {local_result[1]} 条记录")
                return local_result

            # 如果本地没有数据，记录警告但不使用外部API（避免失败）
            logger.warning(f"⚠️ {stock_code} 本地库无数据，跳过外部API收集")
            return False, 0, "本地库无数据", "本地库无数据，跳过外部API收集"

        except Exception as e:
            logger.error(f"❌ {stock_code} 收集失败: {e}")
            return False, 0, "", str(e)

    async def _get_local_stock_data(self, stock_code: str) -> Tuple[bool, int, str, str]:
        """从本地股票数据库获取历史数据"""
        try:
            # 清理股票代码（移除.XSHE/.XSHG后缀）
            clean_code = stock_code.replace('.XSHE', '').replace('.XSHG', '')

            # 连接本地股票数据库
            import os
            db_path = os.path.join("backend", "data", "stock_database.db")
            if not os.path.exists(db_path):
                logger.warning(f"本地股票数据库不存在: {db_path}")
                return False, 0, "数据库不存在", "本地股票数据库不存在"

            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 查询历史数据
            query = """
                SELECT COUNT(*) as count FROM daily_data
                WHERE stock_code = ?
            """

            cursor.execute(query, (clean_code,))
            result = cursor.fetchone()
            record_count = result['count'] if result else 0

            if record_count > 0:
                # 获取日期范围
                cursor.execute("""
                    SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date
                    FROM daily_data WHERE stock_code = ?
                """, (clean_code,))
                date_result = cursor.fetchone()

                date_range = f"{date_result['min_date']} ~ {date_result['max_date']}" if date_result else "未知"

                conn.close()
                logger.info(f"✅ {stock_code} 本地库数据: {record_count} 条记录 ({date_range})")
                return True, record_count, date_range, "本地数据库"
            else:
                conn.close()
                logger.warning(f"⚠️ {stock_code} 本地库无历史数据")
                return False, 0, "无数据", "本地库无历史数据"

        except Exception as e:
            logger.error(f"❌ {stock_code} 本地库数据获取失败: {e}")
            return False, 0, "", str(e)
    
    async def _save_stock_data_to_db(self, stock_code: str, klines: List[str]) -> int:
        """保存股票数据到数据库"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            records_saved = 0
            
            for kline in klines:
                try:
                    # 解析K线数据
                    # 格式: 日期,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率
                    parts = kline.split(',')
                    
                    if len(parts) < 11:
                        continue
                    
                    trade_date = parts[0]
                    open_price = float(parts[1])
                    close_price = float(parts[2])
                    high_price = float(parts[3])
                    low_price = float(parts[4])
                    volume = int(parts[5])
                    amount = float(parts[6])
                    change_percent = float(parts[8]) if parts[8] else 0
                    change_amount = float(parts[9]) if parts[9] else 0
                    
                    # 计算前收盘价
                    pre_close = close_price - change_amount if change_amount else close_price
                    
                    # 插入数据库
                    cursor.execute('''
                        INSERT OR REPLACE INTO daily_data 
                        (stock_code, trade_date, open_price, high_price, low_price, 
                         close_price, volume, amount, change_amount, change_percent, 
                         pre_close, data_source, created_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        stock_code, trade_date, open_price, high_price, low_price,
                        close_price, volume, amount, change_amount, change_percent,
                        pre_close, 'eastmoney_10year', datetime.now().isoformat()
                    ))
                    
                    records_saved += 1
                    
                except (ValueError, IndexError) as e:
                    logger.warning(f"解析K线数据失败: {kline}, 错误: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            return records_saved
            
        except Exception as e:
            logger.error(f"保存股票数据失败: {e}")
            return 0
    
    async def _get_all_stocks_from_db(self) -> List[str]:
        """从数据库获取所有股票代码"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT DISTINCT stock_code FROM stock_info WHERE status = 'active'")
            stock_codes = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            return stock_codes
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    async def get_collection_progress(self) -> Dict[str, Any]:
        """获取收集进度"""
        
        if not self.collection_stats["start_time"]:
            return {"status": "not_started"}
        
        if not self.collection_stats["end_time"]:
            # 正在进行中
            current_time = datetime.now()
            elapsed = current_time - self.collection_stats["start_time"]
            
            progress = {
                "status": "in_progress",
                "elapsed_time": str(elapsed),
                "total_stocks": self.collection_stats["total_stocks"],
                "processed_stocks": self.collection_stats["successful_stocks"] + self.collection_stats["failed_stocks"],
                "successful_stocks": self.collection_stats["successful_stocks"],
                "failed_stocks": self.collection_stats["failed_stocks"],
                "total_records": self.collection_stats["total_records"]
            }
            
            if self.collection_stats["total_stocks"] > 0:
                progress["progress_percentage"] = (progress["processed_stocks"] / self.collection_stats["total_stocks"]) * 100
            
            return progress
        else:
            # 已完成
            duration = self.collection_stats["end_time"] - self.collection_stats["start_time"]
            
            return {
                "status": "completed",
                "total_duration": str(duration),
                "stats": self.collection_stats
            }

# 全局实例
ten_year_data_collector = TenYearDataCollector()
