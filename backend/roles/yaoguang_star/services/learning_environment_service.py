#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习环境服务
瑶光星的学习环境管理服务 - 仅基于本地股票库
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path

# 导入四大核心系统
try:
    from core.domain.memory.legendary.interface import legendary_memory_interface
    from core.domain.memory.legendary.models import MessageType, MemoryScope, MemoryPriority
    from roles.yaoguang_star.config.deepseek_config import get_memory_config
    CORE_SYSTEMS_AVAILABLE = True
except ImportError:
    legendary_memory_interface = None
    MessageType = None
    get_memory_config = None
    CORE_SYSTEMS_AVAILABLE = False

try:
    from core.performance.star_performance_monitor import StarPerformanceMonitor
    performance_monitor = StarPerformanceMonitor()
except ImportError:
    performance_monitor = None

try:
    from core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
except ImportError:
    EnhancedSevenStarsHierarchy = None

from ..core.learning_environment_provider import LearningEnvironmentProvider

logger = logging.getLogger(__name__)

class LearningEnvironmentService:
    """学习环境服务 - 仅基于本地股票库"""

    def __init__(self):
        self.environment_provider = LearningEnvironmentProvider()
        self.local_data_only = True  # 强制只使用本地数据

        # 初始化四大核心系统
        self._init_core_systems()

        logger.info("学习环境服务初始化完成 - 仅使用本地股票库数据")

    def _init_core_systems(self):
        """初始化四大核心系统"""
        try:
            # 1. 传奇记忆系统
            if legendary_memory_interface:
                self.memory_system = legendary_memory_interface
            else:
                self.memory_system = None

            # 2. DeepSeek人设配置
            if get_memory_config:
                self.deepseek_memory_config = get_memory_config()
            else:
                self.deepseek_memory_config = None

            # 3. 绩效监控系统
            self.performance_monitor = performance_monitor

            # 4. 层级权限系统
            if EnhancedSevenStarsHierarchy:
                self.permission_system = EnhancedSevenStarsHierarchy()
            else:
                self.permission_system = None

            if self.memory_system and self.deepseek_memory_config:
                logger.info("瑶光星四大核心系统初始化完成")
            else:
                logger.warning("部分核心系统不可用，使用降级模式")

        except Exception as e:
            logger.error(f"核心系统初始化失败: {e}")
            self.memory_system = None
            self.deepseek_memory_config = None
            self.performance_monitor = None
            self.permission_system = None

    async def _trigger_deepseek_memory(self, trigger_name: str, content: str, context: Dict[str, Any] = None):
        """根据DeepSeek配置触发记忆"""
        try:
            if not self.memory_system or not self.deepseek_memory_config:
                return

            # 获取触发器对应的记忆类型
            memory_type_mapping = {
                "factor_discovery": "factor_research_memory",
                "experiment_breakthrough": "experiment_results_memory",
                "learning_improvement": "learning_optimization_memory",
                "knowledge_update": "knowledge_base_memory"
            }

            memory_type = memory_type_mapping.get(trigger_name)
            if memory_type:
                # 根据记忆类型选择消息类型
                message_type_mapping = {
                    "factor_research_memory": MessageType.GENERAL,
                    "experiment_results_memory": MessageType.SYSTEM_NOTIFICATION,
                    "learning_optimization_memory": MessageType.SYSTEM_NOTIFICATION,
                    "knowledge_base_memory": MessageType.GENERAL
                }

                message_type = message_type_mapping.get(memory_type, MessageType.GENERAL)

                # 添加到传奇记忆系统
                result = await self.memory_system.add_yaoguang_memory(
                    content=content,
                    message_type=message_type
                )

                if result.success:
                    logger.info(f"瑶光星记忆触发成功: {trigger_name} -> {memory_type}")
                else:
                    logger.error(f"瑶光星记忆触发失败: {result.message}")

        except Exception as e:
            logger.error(f"DeepSeek记忆触发失败: {e}")

    async def create_learning_environment(self, env_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建学习环境"""
        try:
            environment_type = env_config.get("environment_type", "stock_analysis")
            stock_codes = env_config.get("stock_codes", [])
            time_range = env_config.get("time_range", "6months")
            learning_objectives = env_config.get("learning_objectives", [])

            # 生成环境ID
            env_id = f"env_{environment_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 验证股票数据可用性
            if stock_codes:
                validation_result = await self.validate_local_data_availability(
                    stock_codes, "2020-01-01", "2024-12-31"
                )

                if not validation_result.get("success"):
                    return {
                        "success": False,
                        "error": "股票数据验证失败",
                        "details": validation_result
                    }

            # 创建学习配置
            learning_config = {
                "environment_type": environment_type,
                "stock_codes": stock_codes,
                "time_range": time_range,
                "learning_objectives": learning_objectives,
                "data_source": "local_only",
                "external_data_disabled": True,
                "created_time": datetime.now().isoformat()
            }

            # 创建隔离环境
            actual_env_id = await self.create_isolated_environment(
                role_id="yaoguang_star",
                environment_type=environment_type,
                learning_config=learning_config
            )

            # 触发记忆和绩效记录
            await self._trigger_deepseek_memory(
                "learning_improvement",
                f"创建学习环境成功: {environment_type} 包含{len(stock_codes)}只股票",
                {"environment_id": actual_env_id, "stock_count": len(stock_codes)}
            )

            await self._record_performance_metric(
                "learning_efficiency",
                0.9,
                {"environment_type": environment_type, "stock_count": len(stock_codes)}
            )

            return {
                "success": True,
                "environment_id": actual_env_id,
                "environment_type": environment_type,
                "stock_codes": stock_codes,
                "learning_objectives": learning_objectives,
                "data_validation": validation_result if stock_codes else None,
                "message": f"学习环境创建成功: {actual_env_id}"
            }

        except Exception as e:
            logger.error(f"创建学习环境失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_environment_status(self, env_id: str) -> Dict[str, Any]:
        """获取环境状态"""
        try:
            env_info = await self.get_environment_info(env_id)

            if not env_info:
                return {
                    "success": False,
                    "error": "环境不存在"
                }

            # 计算环境状态
            status = "active" if env_info else "inactive"

            return {
                "success": True,
                "environment_id": env_id,
                "status": status,
                "environment_info": env_info,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"获取环境状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _record_performance_metric(self, metric_name: str, value: float, context: Dict[str, Any] = None):
        """记录绩效指标"""
        try:
            if self.performance_monitor:
                from core.performance.star_performance_monitor import PerformanceMetricType

                # 映射指标名称到枚举类型
                metric_type_mapping = {
                    "learning_efficiency": PerformanceMetricType.EFFICIENCY,
                    "factor_research_quality": PerformanceMetricType.QUALITY_SCORE,
                    "experiment_success_rate": PerformanceMetricType.SUCCESS_RATE,
                    "knowledge_management": PerformanceMetricType.ACCURACY
                }

                metric_type = metric_type_mapping.get(metric_name, PerformanceMetricType.EFFICIENCY)

                await self.performance_monitor.record_performance(
                    star_name="瑶光星",
                    metric_type=metric_type,
                    value=value,
                    context=context or {}
                )
                logger.debug(f"瑶光星绩效记录: {metric_name}={value}")
        except Exception as e:
            logger.error(f"绩效记录失败: {e}")

    async def create_isolated_environment(self,
                                        role_id: str,
                                        environment_type: str,
                                        learning_config: Dict) -> str:
        """创建隔离学习环境 - 强制本地数据源"""

        # 强制设置为本地数据源
        learning_config = learning_config.copy()
        learning_config["data_source"] = "local_only"
        learning_config["external_data_disabled"] = True
        learning_config["local_db_path"] = str(Path(__file__).parent.parent.parent.parent / "backend" / "data" / "complete_a_stock_library" / "complete_a_stock_data.db")

        logger.info(f"创建学习环境 - 角色:{role_id}, 类型:{environment_type}, 数据源:仅本地")

        env_id = await self.environment_provider.create_isolated_environment(
            role_id=role_id,
            environment_type=environment_type,
            learning_config=learning_config
        )

        # 触发DeepSeek记忆和记录绩效
        await self._trigger_deepseek_memory(
            "learning_improvement",
            f"创建学习环境: 角色{role_id} 环境类型{environment_type} 环境ID{env_id}",
            {"role_id": role_id, "environment_type": environment_type}
        )

        # 记录绩效指标
        await self._record_performance_metric(
            "learning_efficiency",
            1.0,
            {"role_id": role_id, "environment_type": environment_type}
        )

        return env_id

    async def get_environment_info(self, env_id: str) -> Optional[Dict]:
        """获取环境信息"""
        env_info = self.environment_provider.get_environment_info(env_id)

        if env_info:
            # 添加本地数据源信息
            env_info["data_source"] = "local_only"
            env_info["external_data_disabled"] = True
            env_info["learning_mode"] = "local_stock_library_only"

        return env_info

    async def destroy_environment(self, env_id: str) -> bool:
        """销毁学习环境"""
        return await self.environment_provider.destroy_environment(env_id)

    async def list_environments_by_role(self, role_id: str) -> List[str]:
        """列出角色的所有环境"""
        return self.environment_provider.list_environments_by_role(role_id)

    async def create_local_backtest_environment(self,
                                              role_id: str,
                                              stocks: List[str],
                                              start_date: str,
                                              end_date: str,
                                              strategy_config: Dict) -> str:
        """创建本地回测环境"""

        learning_config = {
            "environment_type": "local_backtest",
            "data_source": "local_only",
            "external_data_disabled": True,
            "stocks": stocks,
            "start_date": start_date,
            "end_date": end_date,
            "strategy_config": strategy_config,
            "local_db_path": str(Path(__file__).parent.parent.parent.parent / "data" / "complete_a_stock_library" / "complete_a_stock_data.db")
        }

        logger.info(f"创建本地回测环境 - 角色:{role_id}, 股票数:{len(stocks)}, 时间范围:{start_date}到{end_date}")

        return await self.create_isolated_environment(
            role_id=role_id,
            environment_type="local_backtest",
            learning_config=learning_config
        )

    async def validate_local_data_availability(self, stocks: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """验证本地数据可用性"""
        try:
            from .data_management_service import DataManagementService

            data_service = DataManagementService()

            # 检查每只股票的数据可用性
            stock_availability = {}

            for stock_code in stocks:
                stock_data = await data_service.get_local_stock_data(stock_code, start_date, end_date)

                if stock_data["success"]:
                    record_count = stock_data["record_count"]
                    stock_availability[stock_code] = {
                        "available": True,
                        "record_count": record_count,
                        "data_quality": "good" if record_count > 100 else "limited"
                    }
                else:
                    stock_availability[stock_code] = {
                        "available": False,
                        "error": stock_data["error"]
                    }

            available_stocks = [stock for stock, info in stock_availability.items() if info["available"]]

            return {
                "success": True,
                "total_stocks": len(stocks),
                "available_stocks": len(available_stocks),
                "availability_rate": len(available_stocks) / len(stocks) if stocks else 0,
                "stock_details": stock_availability,
                "data_source": "local_database_only"
            }

        except Exception as e:
            logger.error(f"验证本地数据可用性失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
