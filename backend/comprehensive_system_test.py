#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七星量化交易系统全方面深入测试
测试传奇记忆、自动化、进化、角色层级、战法、辩论、绩效、角色定义等所有核心功能
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveSystemTester:
    """全方面系统测试器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8003"):
        self.base_url = base_url
        self.session = None
        self.test_results = {}
        self.start_time = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        self.start_time = datetime.now()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        try:
            async with self.session.request(method, url, **kwargs) as response:
                if response.content_type == 'application/json':
                    data = await response.json()
                else:
                    data = {"text": await response.text()}
                
                return {
                    "status_code": response.status,
                    "data": data,
                    "success": 200 <= response.status < 300
                }
        except Exception as e:
            return {
                "status_code": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    async def test_basic_health(self) -> Dict[str, Any]:
        """测试基础健康检查"""
        logger.info("🔍 测试基础健康检查...")
        
        endpoints = [
            "/health",
            "/api/system/status",
            "/api/system/statistics"
        ]
        
        results = {}
        for endpoint in endpoints:
            result = await self.make_request("GET", endpoint)
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_legendary_memory_system(self) -> Dict[str, Any]:
        """测试传奇记忆系统"""
        logger.info("🧠 测试传奇记忆系统...")
        
        # 测试记忆添加
        memory_data = {
            "content": "测试传奇记忆系统功能",
            "role": "天权星",
            "memory_type": "decision",
            "importance": 0.8
        }
        
        # 查找记忆相关的API端点
        memory_endpoints = [
            "/api/memory/add",
            "/api/memory/search",
            "/api/memory/statistics",
            "/api/legendary-memory/status"
        ]
        
        results = {}
        for endpoint in memory_endpoints:
            if "add" in endpoint:
                result = await self.make_request("POST", endpoint, json=memory_data)
            else:
                result = await self.make_request("GET", endpoint)
            
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_seven_stars_roles(self) -> Dict[str, Any]:
        """测试七星角色系统"""
        logger.info("⭐ 测试七星角色系统...")
        
        # 七星角色API端点
        star_endpoints = {
            "天枢星": ["/api/intelligence/status", "/api/intelligence/performance"],
            "天璇星": ["/api/architect/status", "/api/architect/performance"],
            "天玑星": ["/api/tianji/status", "/api/tianji/health"],
            "天权星": ["/api/tianquan/status", "/api/tianquan/performance"],
            "玉衡星": ["/api/yuheng/status", "/api/yuheng/performance"],
            "开阳星": ["/api/kaiyang-star/health", "/api/kaiyang-star/status"],
            "瑶光星": ["/api/yaoguang-star/health", "/api/yaoguang-star/system-info"]
        }
        
        results = {}
        for star_name, endpoints in star_endpoints.items():
            star_results = {}
            for endpoint in endpoints:
                result = await self.make_request("GET", endpoint)
                star_results[endpoint] = result
                status = "✅" if result["success"] else "❌"
                logger.info(f"   {status} {star_name} - {endpoint}: {result['status_code']}")
            
            results[star_name] = star_results
        
        return results
    
    async def test_automation_system(self) -> Dict[str, Any]:
        """测试自动化系统"""
        logger.info("🤖 测试自动化系统...")
        
        automation_endpoints = [
            "/api/automation/status",
            "/api/automation/learning/status",
            "/api/automation/trading/status",
            "/api/yaoguang-automation/learning/status",
            "/api/unified-automation/status"
        ]
        
        results = {}
        for endpoint in automation_endpoints:
            result = await self.make_request("GET", endpoint)
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_debate_system(self) -> Dict[str, Any]:
        """测试四星辩论系统"""
        logger.info("🗣️ 测试四星辩论系统...")
        
        # 测试辩论相关端点
        debate_endpoints = [
            "/api/debate/status",
            "/api/debate/start",
            "/api/tianquan/debate/status"
        ]
        
        results = {}
        for endpoint in debate_endpoints:
            if "start" in endpoint:
                debate_data = {
                    "topic": "测试股票投资决策",
                    "stock_code": "000001",
                    "participants": ["天枢星", "天璇星", "天玑星", "玉衡星"]
                }
                result = await self.make_request("POST", endpoint, json=debate_data)
            else:
                result = await self.make_request("GET", endpoint)
            
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_rd_agent_integration(self) -> Dict[str, Any]:
        """测试RD-Agent集成和进化系统"""
        logger.info("🧬 测试RD-Agent集成和进化系统...")
        
        rd_agent_endpoints = [
            "/api/rd-agent/status",
            "/api/rd-agent/factors/generate",
            "/api/yaoguang-star/rd-agent/status",
            "/api/architect/rd-agent/status"
        ]
        
        results = {}
        for endpoint in rd_agent_endpoints:
            if "generate" in endpoint:
                factor_data = {
                    "stock_codes": ["000001", "600519"],
                    "factor_types": ["technical", "fundamental"]
                }
                result = await self.make_request("POST", endpoint, json=factor_data)
            else:
                result = await self.make_request("GET", endpoint)
            
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_performance_monitoring(self) -> Dict[str, Any]:
        """测试绩效监控系统"""
        logger.info("📊 测试绩效监控系统...")
        
        performance_endpoints = [
            "/api/performance/overview",
            "/api/performance/roles",
            "/api/monitoring/system",
            "/api/monitoring/performance"
        ]
        
        results = {}
        for endpoint in performance_endpoints:
            result = await self.make_request("GET", endpoint)
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_data_systems(self) -> Dict[str, Any]:
        """测试数据系统（股票库、新闻库、技能库等）"""
        logger.info("💾 测试数据系统...")
        
        data_endpoints = [
            "/api/data/stocks/list",
            "/api/data/stocks/000001",
            "/api/knowledge/news/latest",
            "/api/skills/list",
            "/api/market-data/status"
        ]
        
        results = {}
        for endpoint in data_endpoints:
            result = await self.make_request("GET", endpoint)
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def test_trading_execution(self) -> Dict[str, Any]:
        """测试交易执行系统"""
        logger.info("💰 测试交易执行系统...")
        
        trading_endpoints = [
            "/api/yuheng/trading/status",
            "/api/yuheng/virtual-trading/status",
            "/api/trading/portfolio/status"
        ]
        
        results = {}
        for endpoint in trading_endpoints:
            result = await self.make_request("GET", endpoint)
            results[endpoint] = result
            status = "✅" if result["success"] else "❌"
            logger.info(f"   {status} {endpoint}: {result['status_code']}")
        
        return results
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行全方面测试"""
        logger.info("🚀 开始全方面深入测试...")
        logger.info("=" * 80)
        
        test_suite = [
            ("基础健康检查", self.test_basic_health),
            ("传奇记忆系统", self.test_legendary_memory_system),
            ("七星角色系统", self.test_seven_stars_roles),
            ("自动化系统", self.test_automation_system),
            ("四星辩论系统", self.test_debate_system),
            ("RD-Agent进化系统", self.test_rd_agent_integration),
            ("绩效监控系统", self.test_performance_monitoring),
            ("数据系统", self.test_data_systems),
            ("交易执行系统", self.test_trading_execution)
        ]
        
        all_results = {}
        
        for test_name, test_func in test_suite:
            try:
                logger.info(f"\n📋 执行测试: {test_name}")
                result = await test_func()
                all_results[test_name] = result
                
                # 统计成功率
                total_tests = 0
                successful_tests = 0
                
                def count_results(data):
                    nonlocal total_tests, successful_tests
                    if isinstance(data, dict):
                        if "success" in data:
                            total_tests += 1
                            if data["success"]:
                                successful_tests += 1
                        else:
                            for value in data.values():
                                count_results(value)
                
                count_results(result)
                
                if total_tests > 0:
                    success_rate = (successful_tests / total_tests) * 100
                    logger.info(f"   📈 {test_name} 成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
                
            except Exception as e:
                logger.error(f"   ❌ {test_name} 测试失败: {e}")
                all_results[test_name] = {"error": str(e)}
        
        # 生成测试报告
        await self.generate_test_report(all_results)
        
        return all_results
    
    async def generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 测试报告生成")
        logger.info("=" * 80)
        
        total_duration = (datetime.now() - self.start_time).total_seconds()
        
        # 统计总体结果
        total_tests = 0
        successful_tests = 0
        failed_tests = 0
        
        def count_all_results(data):
            nonlocal total_tests, successful_tests, failed_tests
            if isinstance(data, dict):
                if "success" in data:
                    total_tests += 1
                    if data["success"]:
                        successful_tests += 1
                    else:
                        failed_tests += 1
                else:
                    for value in data.values():
                        count_all_results(value)
        
        count_all_results(results)
        
        overall_success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"🎯 总体测试结果:")
        logger.info(f"   ✅ 成功测试: {successful_tests}")
        logger.info(f"   ❌ 失败测试: {failed_tests}")
        logger.info(f"   📊 总成功率: {overall_success_rate:.1f}%")
        logger.info(f"   ⏱️ 测试耗时: {total_duration:.2f}秒")
        
        # 保存详细报告
        report_file = f"backend/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "test_time": self.start_time.isoformat(),
                "duration_seconds": total_duration,
                "summary": {
                    "total_tests": total_tests,
                    "successful_tests": successful_tests,
                    "failed_tests": failed_tests,
                    "success_rate": overall_success_rate
                },
                "detailed_results": results
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 详细报告已保存: {report_file}")

async def main():
    """主函数"""
    async with ComprehensiveSystemTester() as tester:
        await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
