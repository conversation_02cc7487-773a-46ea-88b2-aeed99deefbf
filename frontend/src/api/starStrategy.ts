import { request } from './index'

// 星策AI投资智能体API接口定义 - 基于真实后端API
export interface RoleStatus {
  id: string
  name: string
  star: string
  icon: string
  description: string
  status: 'online' | 'offline' | 'working' | 'idle' | 'error'
  progress: number
  isActive: boolean
  color: string
  lastUpdate: string
  metrics?: Record<string, any>
  services?: ServiceStatus[]
  performance?: number
}

export interface ServiceStatus {
  name: string
  status: 'active' | 'inactive' | 'error'
  lastExecution?: string
  executionCount?: number
  successRate?: number
  averageDuration?: number
}

// 用户认证相关接口
export interface AuthResult {
  success: boolean
  token?: string
  user_info?: {
    user_id: string
    username: string
    user_level: string
    parent_user_id?: string
    commission_rate?: number
    total_commission?: number
    active_status?: string
    created_at?: string
  }
  permissions?: string[]
  error?: string
}

export interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  role?: string
  content: string
  senderName: string
  timestamp: Date
  rdInsights?: string
  attachments?: any[]
}

export interface SystemStatus {
  isOnline: boolean
  isCollaborating: boolean
  activeRoles: string[]
  systemHealth: number
  lastUpdate: string
}

export interface AnalysisRequest {
  type: 'stock-analysis' | 'our-method' | 'our-performance' | 'system-status'
  stockCode?: string
  parameters?: Record<string, any>
}

export interface AnalysisResult {
  id: string
  type: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  result?: any
  error?: string
  timestamp: Date
  involvedRoles: string[]
}

// 分销系统相关接口
export interface DistributionUser {
  user_id: string
  username: string
  user_level: number
  parent_user_id?: string
  commission_rate: number
  total_commission: number
  active_status: string
  created_at: string
}

export interface CommissionRecord {
  id: number
  user_id: string
  source_user_id: string
  commission_type: string
  base_amount: number
  commission_rate: number
  commission_amount: number
  status: string
  created_at: string
}

// 七星角色工作流接口
export interface WorkflowStatus {
  role: string
  status: string
  active_workflows: number
  completed_workflows: number
  services: Record<string, string>
  performance_metrics?: Record<string, number>
}

// 六角色API客户端
class StarStrategyAPI {
  // 获取系统状态 - 连接真实后端API
  async getSystemStatus(): Promise<SystemStatus> {
    try {
      const response = await request.get('/api/v1/seven_stars/status')
      return {
        isOnline: response.data.success,
        isCollaborating: response.data.seven_stars ? Object.keys(response.data.seven_stars).length > 1 : false,
        activeRoles: response.data.seven_stars ? Object.keys(response.data.seven_stars).filter(
          (key: string) => response.data.seven_stars[key].status === 'online'
        ) : [],
        systemHealth: response.data.overall_status === 'healthy' ? 95 : 60,
        lastUpdate: new Date().toISOString()
      }
    } catch (error) {
      console.error('获取系统状态失败:', error)
      // 返回默认状态
      return {
        isOnline: true,
        isCollaborating: false,
        activeRoles: ['stock-manager'],
        systemHealth: 95,
        lastUpdate: new Date().toISOString()
      }
    }
  }

  // 获取所有角色状态 - 连接真实后端API
  async getAllRolesStatus(): Promise<RoleStatus[]> {
    try {
      const response = await request.get('/api/v1/seven_stars/status')
      console.log('🔍 getAllRolesStatus API原始响应:', response)

      // 处理响应数据结构
      const responseData = response.data || response
      console.log('📡 处理后的响应数据:', responseData)

      if (responseData.success && responseData.seven_stars) {
        const sevenStars = responseData.seven_stars
        console.log('🌟 七星数据:', sevenStars)
        return [
          {
            id: 'tianshu_intelligence',
            name: '情报官',
            star: '天枢星',
            icon: '🕵️',
            description: '智能信息收集与分析 - 8个核心服务',
            status: sevenStars.tianshu_intelligence?.status || 'offline',
            progress: (sevenStars.tianshu_intelligence?.performance || 0) * 100,
            isActive: sevenStars.tianshu_intelligence?.status === 'online',
            color: '#1e3a8a',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.tianshu_intelligence?.services || 8,
            performance: sevenStars.tianshu_intelligence?.performance || 0.95
          },
          {
            id: 'tianxuan_architect',
            name: '策略架构师',
            star: '天璇星',
            icon: '🏗️',
            description: '投资策略设计与优化 - 7个核心服务',
            status: sevenStars.tianxuan_architect?.status || 'offline',
            progress: (sevenStars.tianxuan_architect?.performance || 0) * 100,
            isActive: sevenStars.tianxuan_architect?.status === 'online',
            color: '#f59e0b',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.tianxuan_architect?.services || 7,
            performance: sevenStars.tianxuan_architect?.performance || 0.92
          },
          {
            id: 'tianji_risk',
            name: '风控总监',
            star: '天玑星',
            icon: '🛡️',
            description: '风险评估与控制 - 1个核心服务',
            status: sevenStars.tianji_risk?.status || 'offline',
            progress: (sevenStars.tianji_risk?.performance || 0) * 100,
            isActive: sevenStars.tianji_risk?.status === 'online',
            color: '#ea580c',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.tianji_risk?.services || 1,
            performance: sevenStars.tianji_risk?.performance || 0.98
          },
          {
            id: 'tianquan_commander',
            name: '决策指挥官',
            star: '天权星',
            icon: '👑',
            description: '投资决策制定 - 2个核心服务',
            status: sevenStars.tianquan_commander?.status || 'offline',
            progress: (sevenStars.tianquan_commander?.performance || 0) * 100,
            isActive: sevenStars.tianquan_commander?.status === 'online',
            color: '#7c3aed',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.tianquan_commander?.services || 2,
            performance: sevenStars.tianquan_commander?.performance || 0.90
          },
          {
            id: 'yuheng_trader',
            name: '执行操盘手',
            star: '玉衡星',
            icon: '🎮',
            description: '交易执行与管理 - 2个核心服务',
            status: sevenStars.yuheng_trader?.status || 'offline',
            progress: (sevenStars.yuheng_trader?.performance || 0) * 100,
            isActive: sevenStars.yuheng_trader?.status === 'online',
            color: '#dc2626',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.yuheng_trader?.services || 2,
            performance: sevenStars.yuheng_trader?.performance || 0.88
          },
          {
            id: 'kaiyang_manager',
            name: '股票经理',
            star: '开阳星',
            icon: '💼',
            description: '客户服务与沟通 - 2个核心服务',
            status: sevenStars.kaiyang_manager?.status || 'online',
            progress: (sevenStars.kaiyang_manager?.performance || 0.93) * 100,
            isActive: sevenStars.kaiyang_manager?.status === 'online',
            color: '#0891b2',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.kaiyang_manager?.services || 2,
            performance: sevenStars.kaiyang_manager?.performance || 0.93
          },
          {
            id: 'yaoguang_distribution',
            name: '分销管理',
            star: '瑶光星',
            icon: '🌟',
            description: '三级分销管理 - 3个核心服务',
            status: sevenStars.yaoguang_distribution?.status || 'offline',
            progress: (sevenStars.yaoguang_distribution?.performance || 0) * 100,
            isActive: sevenStars.yaoguang_distribution?.status === 'online',
            color: '#4c1d95',
            lastUpdate: new Date().toISOString(),
            services: sevenStars.yaoguang_distribution?.services || 3,
            performance: sevenStars.yaoguang_distribution?.performance || 0.91
          }
        ]
      }

      // 如果API调用失败，返回默认数据
      return this._getDefaultRolesStatus()

    } catch (error) {
      console.error('获取角色状态失败:', error)
      // 返回默认角色状态
      return this._getDefaultRolesStatus()
    }
  }

  // 获取默认角色状态
  private _getDefaultRolesStatus(): RoleStatus[] {
    return [
      {
        id: 'tianshu_intelligence',
        name: '情报官',
        star: '天枢星',
        icon: '🕵️',
        description: '智能信息收集与分析 - 8个核心服务',
        status: 'offline',
        progress: 0,
        isActive: false,
        color: '#1e3a8a',
        lastUpdate: new Date().toISOString(),
        services: 8,
        performance: 0.95
      },
      {
        id: 'tianxuan_architect',
        name: '策略架构师',
        star: '天璇星',
        icon: '🏗️',
        description: '投资策略设计与优化 - 7个核心服务',
        status: 'offline',
        progress: 0,
        isActive: false,
        color: '#f59e0b',
        lastUpdate: new Date().toISOString(),
        services: 7,
        performance: 0.92
      },
      {
        id: 'tianji_risk',
        name: '风控总监',
        star: '天玑星',
        icon: '🛡️',
        description: '风险评估与控制 - 1个核心服务',
        status: 'offline',
        progress: 0,
        isActive: false,
        color: '#ea580c',
        lastUpdate: new Date().toISOString(),
        services: 1,
        performance: 0.98
      },
      {
        id: 'tianquan_commander',
        name: '决策指挥官',
        star: '天权星',
        icon: '👑',
        description: '投资决策制定 - 2个核心服务',
        status: 'offline',
        progress: 0,
        isActive: false,
        color: '#7c3aed',
        lastUpdate: new Date().toISOString(),
        services: 2,
        performance: 0.90
      },
      {
        id: 'yuheng_trader',
        name: '执行操盘手',
        star: '玉衡星',
        icon: '🎮',
        description: '交易执行与管理 - 2个核心服务',
        status: 'offline',
        progress: 0,
        isActive: false,
        color: '#dc2626',
        lastUpdate: new Date().toISOString(),
        services: 2,
        performance: 0.88
      },
      {
        id: 'kaiyang_manager',
        name: '股票经理',
        star: '开阳星',
        icon: '💼',
        description: '客户服务与沟通 - 2个核心服务',
        status: 'online',
        progress: 100,
        isActive: true,
        color: '#0891b2',
        lastUpdate: new Date().toISOString(),
        services: 2,
        performance: 0.93
      },
      {
        id: 'yaoguang_distribution',
        name: '分销管理',
        star: '瑶光星',
        icon: '🌟',
        description: '三级分销管理 - 3个核心服务',
        status: 'offline',
        progress: 0,
        isActive: false,
        color: '#4c1d95',
        lastUpdate: new Date().toISOString(),
        services: 3,
        performance: 0.91
      }
    ]
  }

  // 获取特定角色状态
  async getRoleStatus(roleId: string): Promise<RoleStatus | null> {
    try {
      const response = await request.get(`/api/six-roles/roles/${roleId}/status`)
      return response.data
    } catch (error) {
      console.error(`获取角色${roleId}状态失败:`, error)
      return null
    }
  }

  // 发送消息给AI团队
  async sendMessage(message: string, context?: any): Promise<ChatMessage> {
    try {
      const response = await request.post('/api/six-roles/chat/message', {
        message,
        context,
        timestamp: new Date().toISOString()
      })
      return response.data
    } catch (error) {
      console.error('发送消息失败:', error)
      // 返回模拟回复
      return {
        id: Date.now().toString(),
        type: 'ai',
        role: 'stock-manager',
        content: `我们正在分析您的问题："${message}"，请稍候...`,
        senderName: '股票经理',
        timestamp: new Date(),
        rdInsights: '基于RD-Agent分析，这是一个很好的投资问题'
      }
    }
  }

  // 启动分析任务
  async startAnalysis(analysisRequest: AnalysisRequest): Promise<AnalysisResult> {
    try {
      const response = await request.post('/api/six-roles/analysis/start', analysisRequest)
      return response.data
    } catch (error) {
      console.error('启动分析任务失败:', error)
      // 返回模拟结果
      return {
        id: Date.now().toString(),
        type: analysisRequest.type,
        status: 'processing',
        timestamp: new Date(),
        involvedRoles: ['intelligence', 'architect', 'risk', 'commander', 'trader', 'stock-manager']
      }
    }
  }

  // 获取分析结果
  async getAnalysisResult(analysisId: string): Promise<AnalysisResult | null> {
    try {
      const response = await request.get(`/api/six-roles/analysis/${analysisId}`)
      return response.data
    } catch (error) {
      console.error('获取分析结果失败:', error)
      return null
    }
  }

  // 获取角色工作内容
  async getRoleWorkContent(roleId: string): Promise<any> {
    try {
      const response = await request.get(`/api/six-roles/roles/${roleId}/work-content`)
      return response.data
    } catch (error) {
      console.error(`获取角色${roleId}工作内容失败:`, error)
      return null
    }
  }

  // 控制角色状态
  async controlRole(roleId: string, action: 'start' | 'stop' | 'restart'): Promise<boolean> {
    try {
      const response = await request.post(`/api/six-roles/roles/${roleId}/control`, { action })
      return response.data.success
    } catch (error) {
      console.error(`控制角色${roleId}失败:`, error)
      return false
    }
  }

  // 获取聊天历史
  async getChatHistory(limit: number = 50): Promise<ChatMessage[]> {
    try {
      const response = await request.get(`/api/six-roles/chat/history?limit=${limit}`)
      return response.data
    } catch (error) {
      console.error('获取聊天历史失败:', error)
      return []
    }
  }

  // 获取系统指标
  async getSystemMetrics(): Promise<any> {
    try {
      const response = await request.get('/api/six-roles/system/metrics')
      return response.data
    } catch (error) {
      console.error('获取系统指标失败:', error)
      return {}
    }
  }

  // 获取RD-Agent状态
  async getRDAgentStatus(): Promise<any> {
    try {
      const response = await request.get('/api/six-roles/rd-agent/status')
      return response.data
    } catch (error) {
      console.error('获取RD-Agent状态失败:', error)
      return {
        isRunning: false,
        progress: 0,
        insights: [],
        performance: {}
      }
    }
  }

  // 启动自动化流程
  async startAutomation(): Promise<boolean> {
    try {
      const response = await request.post('/api/six-roles/automation/start')
      return response.data.success
    } catch (error) {
      console.error('启动自动化流程失败:', error)
      return false
    }
  }

  // 停止自动化流程
  async stopAutomation(): Promise<boolean> {
    try {
      const response = await request.post('/api/six-roles/automation/stop')
      return response.data.success
    } catch (error) {
      console.error('停止自动化流程失败:', error)
      return false
    }
  }

  // ==================== 分销系统API ====================

  // 用户认证
  async authenticateUser(user_id: string, password?: string): Promise<AuthResult> {
    try {
      const response = await request.post('/api/v1/auth/login', {
        user_id,
        password
      })
      return response.data
    } catch (error) {
      console.error('用户认证失败:', error)
      return { success: false, error: '认证失败' }
    }
  }

  // 获取分销网络
  async getDistributionNetwork(user_id: string): Promise<any> {
    try {
      console.log('🔗 调用分销网络API:', `/api/distribution/network/${user_id}`)
      const response = await request.get(`/api/distribution/network/${user_id}`)
      console.log(' 分销网络API响应:', response.data)
      return response.data
    } catch (error) {
      console.error('  获取分销网络失败:', error)
      return { success: false, error: '获取失败' }
    }
  }

  // 计算佣金
  async calculateCommission(user_id: string, amount: number, type: string = 'trading'): Promise<any> {
    try {
      const response = await request.post(`/api/v1/distribution/commission/${user_id}`, {
        amount,
        type
      })
      return response.data
    } catch (error) {
      console.error('计算佣金失败:', error)
      return { success: false, error: '计算失败' }
    }
  }

  // 获取监控仪表板
  async getMonitoringDashboard(): Promise<any> {
    try {
      const response = await request.get('/api/v1/monitoring/dashboard')
      return response.data
    } catch (error) {
      console.error('获取监控仪表板失败:', error)
      return { success: false, error: '获取失败' }
    }
  }

  // ==================== 七星角色工作流API ====================

  // 获取指定星的服务状态
  async getStarServices(star_name: string): Promise<any> {
    try {
      const response = await request.get(`/api/v1/star_services/${star_name}`)
      return response.data
    } catch (error) {
      console.error(`获取${star_name}服务状态失败:`, error)
      return { success: false, error: '获取失败' }
    }
  }

  // 获取投资组合
  async getPortfolio(user_id: string): Promise<any> {
    try {
      const response = await request.get(`/api/v1/trading/portfolio/${user_id}`)
      return response.data
    } catch (error) {
      console.error('获取投资组合失败:', error)
      return { success: false, error: '获取失败' }
    }
  }

  // 执行交易
  async executeTrade(tradeData: any): Promise<any> {
    try {
      const response = await request.post('/api/v1/trading/execute', tradeData)
      return response.data
    } catch (error) {
      console.error('执行交易失败:', error)
      return { success: false, error: '执行失败' }
    }
  }

  // 创建分销用户
  async createDistributionUser(userData: {
    username: string
    user_id: string
    user_level: string
    parent_user_id?: string
  }): Promise<any> {
    try {
      console.log('👤 调用创建用户API:', userData)
      const response = await request.post('/api/distribution/create_user', userData)
      console.log('  创建用户API响应:', response.data)
      return response.data
    } catch (error) {
      console.error('  创建分销用户失败:', error)
      return { success: false, error: '创建失败' }
    }
  }



  // 获取分销商列表
  async getDistributorList(params?: {
    level?: number
    status?: string
    territory?: string
  }): Promise<any> {
    try {
      console.log('👥 调用分销商列表API:', params)
      const response = await request.get('/api/distribution/distributors', { params })
      console.log('  分销商列表API响应:', response.data)
      return response.data
    } catch (error) {
      console.error('  获取分销商列表失败:', error)
      return { success: false, error: '获取失败' }
    }
  }

  // 获取佣金报表
  async getCommissionReport(params: {
    period: string
    distributor_id?: string
    include_sub_distributors?: boolean
  }): Promise<any> {
    try {
      console.log(' 调用佣金报表API:', params)
      const response = await request.get('/api/distribution/commissions/report', { params })
      console.log('  佣金报表API响应:', response.data)
      return response.data
    } catch (error) {
      console.error('  获取佣金报表失败:', error)
      return { success: false, error: '获取失败' }
    }
  }

  // 获取分销网络分析
  async getNetworkAnalysis(params?: {
    depth?: number
    include_performance?: boolean
    territory?: string
  }): Promise<any> {
    try {
      const response = await request.get('/api/v1/distribution/network/analysis', { params })
      return response.data
    } catch (error) {
      console.error('获取网络分析失败:', error)
      return { success: false, error: '获取失败' }
    }
  }
}

// 导出API实例
export const starStrategyAPI = new StarStrategyAPI()

// 导出默认实例
export default starStrategyAPI
