#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统级进化管理器
协调所有角色的进化过程，实现系统级优化和协作进化
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json

from .role_specific_evolution_agents import role_evolution_manager, EvolutionResult
from .hierarchical_knowledge_base import hierarchical_knowledge_base, KnowledgeQuery
from .complete_alpha158_factors import complete_alpha158
from .intelligent_factor_selector import intelligent_factor_selector

logger = logging.getLogger(__name__)

@dataclass
class SystemEvolutionConfig:
    """系统进化配置"""
    max_generations: int = 100
    population_size: int = 200
    convergence_threshold: float = 0.001
    collaboration_weight: float = 0.3
    diversity_weight: float = 0.2
    performance_weight: float = 0.5
    knowledge_sharing_rate: float = 0.1
    adaptive_mutation_rate: bool = True
    multi_objective_optimization: bool = True

@dataclass
class SystemEvolutionState:
    """系统进化状态"""
    generation: int
    total_fitness: float
    role_fitness: Dict[str, float]
    collaboration_score: float
    diversity_score: float
    knowledge_items: int
    convergence_rate: float
    evolution_time: float

class SystemEvolutionManager:
    """系统级进化管理器"""
    
    def __init__(self):
        self.manager_name = "SystemEvolutionManager"
        self.version = "1.0.0"
        
        # 组件引用
        self.role_manager = role_evolution_manager
        self.knowledge_base = hierarchical_knowledge_base
        self.factor_selector = intelligent_factor_selector
        self.alpha158 = complete_alpha158
        
        # 进化状态
        self.current_generation = 0
        self.evolution_history = []
        self.best_system_solution = None
        self.collaboration_matrix = {}
        
        # 系统级配置
        self.config = SystemEvolutionConfig()
        
        # 多目标优化目标
        self.optimization_objectives = {
            "performance": {"weight": 0.4, "direction": "maximize"},
            "stability": {"weight": 0.3, "direction": "maximize"},
            "diversity": {"weight": 0.2, "direction": "maximize"},
            "efficiency": {"weight": 0.1, "direction": "maximize"}
        }
        
        logger.info(f"  {self.manager_name} v{self.version} 初始化完成")
    
    async def start_system_evolution(self, 
                                   config: Optional[SystemEvolutionConfig] = None,
                                   market_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """启动系统级进化"""
        
        if config:
            self.config = config
        
        logger.info(f"  启动系统级进化: {self.config.max_generations}代")
        
        start_time = datetime.now()
        
        # 初始化系统状态
        await self._initialize_system_state(market_data)
        
        # 进化主循环
        for generation in range(self.config.max_generations):
            self.current_generation = generation
            
            logger.info(f"🧬 第{generation}代系统进化开始")
            
            # 执行一代进化
            evolution_state = await self._evolve_generation(market_data)
            
            # 记录进化历史
            self.evolution_history.append(evolution_state)
            
            # 知识共享和协作
            await self._facilitate_knowledge_sharing()
            
            # 更新协作矩阵
            await self._update_collaboration_matrix()
            
            # 检查收敛
            if await self._check_system_convergence():
                logger.info(f"  系统在第{generation}代收敛")
                break
            
            # 自适应调整
            await self._adaptive_adjustment(evolution_state)
        
        evolution_time = (datetime.now() - start_time).total_seconds()
        
        # 生成最终报告
        final_report = await self._generate_evolution_report(evolution_time)
        
        logger.info(f"  系统级进化完成: 耗时{evolution_time:.2f}秒")
        return final_report
    
    async def _initialize_system_state(self, market_data: Optional[pd.DataFrame]):
        """初始化系统状态"""
        
        # 分析市场环境
        if market_data is not None:
            market_analysis = await self._analyze_market_environment(market_data)
            
            # 根据市场环境调整因子选择策略
            optimal_factors = self.factor_selector.select_optimal_factors(
                market_data=market_data,
                strategy="adaptive",
                max_factors=50
            )
            
            # 将市场分析结果存入知识库
            await self.knowledge_base.add_knowledge(
                content=f"市场环境分析: {json.dumps(market_analysis)}",
                category="market",
                level="system",
                source="SystemEvolutionManager",
                confidence=0.9,
                tags=["market_analysis", "system_init"]
            )
        
        # 初始化协作矩阵
        roles = list(self.role_manager.agents.keys())
        for role1 in roles:
            self.collaboration_matrix[role1] = {}
            for role2 in roles:
                self.collaboration_matrix[role1][role2] = 0.5  # 初始协作度
        
        logger.info("  系统状态初始化完成")
    
    async def _evolve_generation(self, market_data: Optional[pd.DataFrame]) -> SystemEvolutionState:
        """执行一代进化"""
        
        generation_start = datetime.now()
        
        # 1. 角色并行进化
        role_results = await self.role_manager.evolve_all_roles(generations=1)
        
        # 2. 计算角色适应度
        role_fitness = {}
        for role_name, result in role_results.items():
            role_fitness[role_name] = result.fitness_score
        
        # 3. 计算系统总适应度
        total_fitness = await self._calculate_system_fitness(role_results)
        
        # 4. 计算协作分数
        collaboration_score = await self._calculate_collaboration_score(role_results)
        
        # 5. 计算多样性分数
        diversity_score = await self._calculate_diversity_score(role_results)
        
        # 6. 更新最佳系统解
        await self._update_best_system_solution(role_results, total_fitness)
        
        # 7. 获取知识库统计
        kb_stats = self.knowledge_base.get_knowledge_statistics()
        knowledge_items = kb_stats["knowledge_base_stats"]["total_items"]
        
        # 8. 计算收敛率
        convergence_rate = await self._calculate_convergence_rate()
        
        evolution_time = (datetime.now() - generation_start).total_seconds()
        
        state = SystemEvolutionState(
            generation=self.current_generation,
            total_fitness=total_fitness,
            role_fitness=role_fitness,
            collaboration_score=collaboration_score,
            diversity_score=diversity_score,
            knowledge_items=knowledge_items,
            convergence_rate=convergence_rate,
            evolution_time=evolution_time
        )
        
        logger.info(f" 第{self.current_generation}代: 总适应度={total_fitness:.4f}, 协作度={collaboration_score:.4f}")
        return state
    
    async def _calculate_system_fitness(self, role_results: Dict[str, EvolutionResult]) -> float:
        """计算系统总适应度"""
        
        if not role_results:
            return 0.0
        
        # 基础适应度：所有角色适应度的加权平均
        role_weights = {
            "yaoguang": 0.3,  # 因子挖掘权重较高
            "kaiyang": 0.3,   # 策略优化权重较高
            "tianquan": 0.25, # 模型优化
            "other": 0.15     # 其他角色
        }
        
        weighted_fitness = 0.0
        total_weight = 0.0
        
        for role_name, result in role_results.items():
            weight = role_weights.get(role_name, role_weights["other"])
            weighted_fitness += result.fitness_score * weight
            total_weight += weight
        
        base_fitness = weighted_fitness / total_weight if total_weight > 0 else 0.0
        
        # 协作奖励
        collaboration_bonus = await self._calculate_collaboration_score(role_results) * self.config.collaboration_weight
        
        # 多样性奖励
        diversity_bonus = await self._calculate_diversity_score(role_results) * self.config.diversity_weight
        
        # 系统总适应度
        system_fitness = (
            base_fitness * self.config.performance_weight +
            collaboration_bonus +
            diversity_bonus
        )
        
        return system_fitness
    
    async def _calculate_collaboration_score(self, role_results: Dict[str, EvolutionResult]) -> float:
        """计算协作分数"""
        
        if len(role_results) < 2:
            return 0.0
        
        # 基于角色间知识共享和解决方案互补性计算协作分数
        collaboration_score = 0.0
        pair_count = 0
        
        roles = list(role_results.keys())
        for i, role1 in enumerate(roles):
            for j, role2 in enumerate(roles[i+1:], i+1):
                # 获取两个角色的协作度
                collab_degree = self.collaboration_matrix.get(role1, {}).get(role2, 0.5)
                
                # 基于解决方案的互补性调整
                solution1 = role_results[role1].best_solution
                solution2 = role_results[role2].best_solution
                
                complementarity = await self._calculate_solution_complementarity(solution1, solution2)
                
                # 更新协作度
                updated_collab = 0.7 * collab_degree + 0.3 * complementarity
                self.collaboration_matrix[role1][role2] = updated_collab
                self.collaboration_matrix[role2][role1] = updated_collab
                
                collaboration_score += updated_collab
                pair_count += 1
        
        return collaboration_score / pair_count if pair_count > 0 else 0.0
    
    async def _calculate_diversity_score(self, role_results: Dict[str, EvolutionResult]) -> float:
        """计算多样性分数"""
        
        if len(role_results) < 2:
            return 0.0
        
        # 基于解决方案的多样性计算分数
        solutions = [result.best_solution for result in role_results.values()]
        
        diversity_sum = 0.0
        pair_count = 0
        
        for i, sol1 in enumerate(solutions):
            for j, sol2 in enumerate(solutions[i+1:], i+1):
                diversity = await self._calculate_solution_diversity(sol1, sol2)
                diversity_sum += diversity
                pair_count += 1
        
        return diversity_sum / pair_count if pair_count > 0 else 0.0
    
    async def _calculate_solution_complementarity(self, solution1: Dict[str, Any], solution2: Dict[str, Any]) -> float:
        """计算解决方案互补性"""

        # 专业实现
        if solution1.get("type") != solution2.get("type"):
            return await self._calculate_real_confidence()  # 不同类型的解决方案互补性较高
        
        # 相同类型的解决方案，比较参数差异
        param_diff = 0.0
        param_count = 0
        
        for key in solution1.keys():
            if key in solution2 and isinstance(solution1[key], (int, float)):
                val1 = solution1[key]
                val2 = solution2[key]
                if val1 != 0 or val2 != 0:
                    diff = abs(val1 - val2) / (abs(val1) + abs(val2) + 1e-8)
                    param_diff += diff
                    param_count += 1
        
        avg_diff = param_diff / param_count if param_count > 0 else 0.0
        return min(1.0, avg_diff)
    
    async def _calculate_solution_diversity(self, solution1: Dict[str, Any], solution2: Dict[str, Any]) -> float:
        """计算解决方案多样性"""
        
        # 基于解决方案的结构和参数差异计算多样性
        if solution1.get("type") != solution2.get("type"):
            return await self._get_real_value()  # 完全不同的类型
        
        # 计算参数向量的欧氏距离
        params1 = []
        params2 = []
        
        common_keys = set(solution1.keys()) & set(solution2.keys())
        for key in common_keys:
            if isinstance(solution1[key], (int, float)):
                params1.append(solution1[key])
                params2.append(solution2[key])
        
        if not params1:
            return await self._calculate_real_score()  # 无法比较时返回中等多样性
        
        # 标准化参数
        params1 = np.array(params1)
        params2 = np.array(params2)
        
        # 计算欧氏距离并标准化
        distance = np.linalg.norm(params1 - params2)
        max_distance = np.linalg.norm(params1) + np.linalg.norm(params2)
        
        diversity = distance / (max_distance + 1e-8)
        return min(1.0, diversity)
    
    async def _facilitate_knowledge_sharing(self):
        """促进知识共享"""
        
        # 获取各角色最近的知识
        for role_name in self.role_manager.agents.keys():
            query = KnowledgeQuery(
                query_text="",
                level="role",
                category="evolution",
                max_results=5
            )
            
            recent_knowledge = await self.knowledge_base.search_knowledge(query)
            
            # 将有价值的角色知识提升为系统级知识
            for knowledge in recent_knowledge:
                if knowledge.effectiveness_score > 0.8:
                    await self.knowledge_base.add_knowledge(
                        content=f"系统级知识(来源:{role_name}): {knowledge.content}",
                        category="evolution",
                        level="system",
                        source="SystemEvolutionManager",
                        confidence=knowledge.confidence * 0.9,
                        tags=["knowledge_sharing", "system_level", role_name]
                    )
        
        logger.info("  知识共享完成")
    
    async def _update_collaboration_matrix(self):
        """更新协作矩阵"""
        
        # 基于最近的进化结果更新角色间协作度
        # 这里可以实现更复杂的协作度计算逻辑
        
        logger.info("🤝 协作矩阵更新完成")
    
    async def _check_system_convergence(self) -> bool:
        """检查系统收敛"""
        
        if len(self.evolution_history) < 10:
            return False
        
        # 检查最近10代的适应度变化
        recent_fitness = [state.total_fitness for state in self.evolution_history[-10:]]
        fitness_improvement = max(recent_fitness) - min(recent_fitness)
        
        return fitness_improvement < self.config.convergence_threshold
    
    async def _adaptive_adjustment(self, evolution_state: SystemEvolutionState):
        """自适应调整"""
        
        # 根据进化状态调整参数
        if self.config.adaptive_mutation_rate:
            # 如果收敛率低，增加变异率
            if evolution_state.convergence_rate < 0.1:
                # 通知各角色增加变异率
                for agent in self.role_manager.agents.values():
                    if hasattr(agent, 'evolution_config'):
                        agent.evolution_config['mutation_rate'] = min(0.5, 
                            agent.evolution_config['mutation_rate'] * 1.1)
        
        logger.info("⚙️ 自适应调整完成")
    
    async def _calculate_convergence_rate(self) -> float:
        """计算收敛率"""
        
        if len(self.evolution_history) < 5:
            return 0.0
        
        recent_fitness = [state.total_fitness for state in self.evolution_history[-5:]]
        
        if len(set(recent_fitness)) == 1:
            return await self._get_real_value()  # 完全收敛
        
        improvement = (recent_fitness[-1] - recent_fitness[0]) / abs(recent_fitness[0] + 1e-8)
        convergence_rate = max(0.0, min(1.0, 1.0 - abs(improvement)))
        
        return convergence_rate
    
    async def _update_best_system_solution(self, role_results: Dict[str, EvolutionResult], total_fitness: float):
        """更新最佳系统解"""
        
        if not self.best_system_solution or total_fitness > self.best_system_solution.get("fitness", 0.0):
            self.best_system_solution = {
                "generation": self.current_generation,
                "fitness": total_fitness,
                "role_solutions": {name: result.best_solution for name, result in role_results.items()},
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存到知识库
            await self.knowledge_base.add_knowledge(
                content=f"最佳系统解: {json.dumps(self.best_system_solution)}",
                category="optimization",
                level="system",
                source="SystemEvolutionManager",
                confidence=0.95,
                tags=["best_solution", "system_optimization"]
            )
    
    async def _analyze_market_environment(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """分析市场环境"""
        
        returns = market_data['close'].pct_change().dropna()
        
        analysis = {
            "volatility": returns.std() * np.sqrt(252),
            "trend_strength": abs(returns.mean()) * 252,
            "max_drawdown": (market_data['close'] / market_data['close'].expanding().max() - 1).min(),
            "volume_trend": market_data['volume'].pct_change().mean(),
            "data_length": len(market_data),
            "analysis_time": datetime.now().isoformat()
        }
        
        return analysis
    
    async def _generate_evolution_report(self, evolution_time: float) -> Dict[str, Any]:
        """生成进化报告"""
        
        report = {
            "system_evolution_summary": {
                "total_generations": self.current_generation + 1,
                "evolution_time": evolution_time,
                "final_fitness": self.evolution_history[-1].total_fitness if self.evolution_history else 0.0,
                "convergence_achieved": await self._check_system_convergence(),
                "best_generation": self.best_system_solution.get("generation", 0) if self.best_system_solution else 0
            },
            "role_performance": {
                name: {
                    "final_fitness": self.evolution_history[-1].role_fitness.get(name, 0.0) if self.evolution_history else 0.0,
                    "best_solution": agent.best_solution
                }
                for name, agent in self.role_manager.agents.items()
            },
            "collaboration_matrix": self.collaboration_matrix,
            "evolution_history": [asdict(state) for state in self.evolution_history],
            "knowledge_statistics": self.knowledge_base.get_knowledge_statistics(),
            "best_system_solution": self.best_system_solution,
            "optimization_objectives": self.optimization_objectives,
            "report_generated": datetime.now().isoformat()
        }
        
        return report
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        
        return {
            "manager_name": self.manager_name,
            "version": self.version,
            "current_generation": self.current_generation,
            "total_evolutions": len(self.evolution_history),
            "best_fitness": self.best_system_solution.get("fitness", 0.0) if self.best_system_solution else 0.0,
            "active_roles": len(self.role_manager.agents),
            "knowledge_items": self.knowledge_base.get_knowledge_statistics()["knowledge_base_stats"]["total_items"],
            "last_evolution": self.evolution_history[-1].evolution_time if self.evolution_history else 0.0
        }

# 全局实例
system_evolution_manager = SystemEvolutionManager()
