#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玉衡星DeepSeek AI配置
精准的交易执行和风险控制专家，专注于最优执行策略的AI角色定义和智能分析系统
"""

from typing import Dict, Any, List
import json

# DeepSeek API配置
DEEPSEEK_CONFIG = {
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com",
    "model": "deepseek-chat",
    "max_tokens": 2000,
    "temperature": 0.2,
    "timeout": 30
}

# 玉衡星角色设定
YUHENG_STAR_ROLE_SETTING = """
你是玉衡星，七星量化交易系统中的交易执行专家。

核心身份：
- 精准的交易执行和风险控制专家，专注于最优执行策略
- 拥有专业的分析能力和决策判断力
- 精通相关领域的理论知识和实践经验
- 具备深度的数据分析和模式识别能力
- 能够与其他星座协作，提供专业建议

专业能力：
- 深度分析和逻辑推理
- 数据驱动的决策制定
- 风险识别和机会发现
- 持续学习和自我优化

工作原则：
- 基于真实数据进行分析
- 提供客观专业的建议
- 注重风险控制和收益平衡
- 与团队协作达成最优结果

请始终以专业的交易执行专家身份，为七星系统提供高质量的专业服务。
"""

# 专业提示词模板
PROFESSIONAL_PROMPTS = {
    "analysis": f"作为玉衡星，请从交易执行专家的角度分析以下内容：",
    "decision": f"作为玉衡星，请基于专业判断提供决策建议：",
    "evaluation": f"作为玉衡星，请评估以下情况并给出专业意见：",
    "collaboration": f"作为玉衡星，请配合其他星座完成以下任务："
}

def get_deepseek_config() -> Dict[str, Any]:
    """获取DeepSeek配置"""
    return DEEPSEEK_CONFIG.copy()

def get_role_setting() -> str:
    """获取玉衡星角色设定"""
    return YUHENG_STAR_ROLE_SETTING

def get_professional_prompt(prompt_type: str, content: str) -> str:
    """获取专业提示词"""
    template = PROFESSIONAL_PROMPTS.get(prompt_type, PROFESSIONAL_PROMPTS["analysis"])
    return f"{template}\n\n{content}"

def create_context_prompt(task_type: str, context_data: Dict[str, Any]) -> str:
    """创建上下文提示词"""
    role_context = f"{get_role_setting()}\n\n"
    
    if task_type == "market_analysis":
        return f"{role_context}请分析市场数据：{json.dumps(context_data, ensure_ascii=False, indent=2)}"
    elif task_type == "risk_assessment":
        return f"{role_context}请评估风险情况：{json.dumps(context_data, ensure_ascii=False, indent=2)}"
    elif task_type == "decision_making":
        return f"{role_context}请提供决策建议：{json.dumps(context_data, ensure_ascii=False, indent=2)}"
    else:
        return f"{role_context}请处理以下任务：{json.dumps(context_data, ensure_ascii=False, indent=2)}"
