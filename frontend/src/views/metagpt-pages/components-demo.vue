<template>
  <div class="metagpt-components-demo">
    <div class="demo-header">
      <h1>MetaGPT组件演示</h1>
      <p>所有MetaGPT组件的完整演示</p>
    </div>
    
    <div class="demo-content">
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span>组件演示</span>
          </div>
        </template>
        <p>这里将展示MetaGPT风格的组件演示</p>
        <p>页面正在开发中...</p>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

onMounted(() => {
  console.log('MetaGPT组件演示页面已加载')
})
</script>

<style lang="scss" scoped>
.metagpt-components-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    h1 {
      color: #2c3e50;
      margin-bottom: 8px;
    }
    
    p {
      color: #7f8c8d;
      margin: 0;
    }
  }
  
  .demo-content {
    .demo-card {
      margin-bottom: 20px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
