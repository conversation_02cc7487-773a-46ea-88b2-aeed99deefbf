#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证测试
验证所有路径问题是否已修复
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_core_modules():
    """测试core模块导入"""
    try:
        logger.info("🔧 测试core模块导入...")
        
        # 添加core路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        core_path = os.path.join(current_dir, "core")
        
        if core_path not in sys.path:
            sys.path.insert(0, core_path)
        
        success_count = 0
        total_count = 4
        
        # 测试因子分配系统
        try:
            from factor_allocation import factor_allocation_system
            logger.info("✅ factor_allocation导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ factor_allocation导入失败: {e}")
        
        # 测试传奇记忆系统
        try:
            from domain.memory.legendary.interface import legendary_memory_interface
            logger.info("✅ legendary_memory_interface导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ legendary_memory_interface导入失败: {e}")
        
        # 测试绩效监控系统
        try:
            from performance.star_performance_monitor import star_performance_monitor
            logger.info("✅ star_performance_monitor导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ star_performance_monitor导入失败: {e}")
        
        # 测试层级权限系统
        try:
            from enhanced_seven_stars_hierarchy import enhanced_seven_stars_hierarchy
            logger.info("✅ enhanced_seven_stars_hierarchy导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ enhanced_seven_stars_hierarchy导入失败: {e}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"📊 core模块导入成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"❌ core模块测试失败: {e}")
        return False

async def test_automation_systems():
    """测试自动化系统导入"""
    try:
        logger.info("🤖 测试自动化系统导入...")
        
        success_count = 0
        total_count = 4
        
        # 测试天枢星自动化系统
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            logger.info("✅ 天枢星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天枢星自动化系统导入失败: {e}")
        
        # 测试天玑星自动化系统
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            logger.info("✅ 天玑星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天玑星自动化系统导入失败: {e}")
        
        # 测试天璇星自动化系统
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            logger.info("✅ 天璇星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天璇星自动化系统导入失败: {e}")
        
        # 测试玉衡星自动化系统
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            logger.info("✅ 玉衡星自动化系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 玉衡星自动化系统导入失败: {e}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"📊 自动化系统导入成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_count >= 3  # 至少3个系统正常
        
    except Exception as e:
        logger.error(f"❌ 自动化系统测试失败: {e}")
        return False

async def test_rd_agent_integration():
    """测试RD-Agent集成"""
    try:
        logger.info("🔬 测试RD-Agent集成...")
        
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        # 测试服务状态
        status = rd_agent_integration_service.get_service_status()
        
        if status.get("use_professional") and status.get("local_rd_agent_available"):
            logger.info("✅ RD-Agent集成正常")
            return True
        else:
            logger.error("❌ RD-Agent集成异常")
            return False
            
    except Exception as e:
        logger.error(f"❌ RD-Agent集成测试失败: {e}")
        return False

async def test_yaoguang_unified_system():
    """测试瑶光星统一系统"""
    try:
        logger.info("🌟 测试瑶光星统一系统...")
        
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 测试系统初始化
        init_result = await unified_yaoguang_system.initialize_system()
        
        if init_result.get("success"):
            logger.info("✅ 瑶光星统一系统初始化成功")
            return True
        else:
            logger.error(f"❌ 瑶光星统一系统初始化失败: {init_result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 瑶光星统一系统测试失败: {e}")
        return False

async def test_backend_services():
    """测试backend服务"""
    try:
        logger.info("🔧 测试backend服务...")
        
        success_count = 0
        total_count = 3
        
        # 测试多源数据管理器
        try:
            from services.data.multi_source_data_manager import multi_source_data_manager
            logger.info("✅ multi_source_data_manager导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ multi_source_data_manager导入失败: {e}")
        
        # 测试deepseek服务
        try:
            from shared.infrastructure import deepseek_service
            logger.info("✅ deepseek_service导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ deepseek_service导入失败: {e}")
        
        # 测试天枢星核心服务
        try:
            from roles.tianshu_star.services.tianshu_core_service import tianshu_core_service
            if hasattr(tianshu_core_service, 'get_stock_basic_info'):
                logger.info("✅ tianshu_core_service.get_stock_basic_info存在")
                success_count += 1
            else:
                logger.error("❌ tianshu_core_service.get_stock_basic_info不存在")
        except Exception as e:
            logger.error(f"❌ tianshu_core_service测试失败: {e}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"📊 backend服务成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_count >= 2  # 至少2个服务正常
        
    except Exception as e:
        logger.error(f"❌ backend服务测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始最终修复验证测试")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 测试1：core模块
    test_results["core_modules"] = await test_core_modules()
    
    # 测试2：自动化系统
    test_results["automation_systems"] = await test_automation_systems()
    
    # 测试3：RD-Agent集成
    test_results["rd_agent_integration"] = await test_rd_agent_integration()
    
    # 测试4：瑶光星统一系统
    test_results["yaoguang_unified_system"] = await test_yaoguang_unified_system()
    
    # 测试5：backend服务
    test_results["backend_services"] = await test_backend_services()
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 最终修复验证结果:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"📈 最终修复成功率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 主要修复完成，系统基本正常！")
        logger.info("💡 建议：现在可以运行完整的学习模式测试")
    elif success_rate >= 60:
        logger.info("⚠️ 部分修复完成，需要进一步优化")
    else:
        logger.info("❌ 修复不完整，需要更多工作")
    
    return success_rate

if __name__ == "__main__":
    asyncio.run(main())
