#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玉衡星交易执行服务
负责统一的交易执行、风险控制和订单管理
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# 增强导入容错机制 - 四大核心系统
import sys
import importlib

class CoreSystemsManager:
    """核心系统管理器 - 提供容错导入和降级功能"""

    def __init__(self):
        self.systems = {}
        self.fallback_systems = {}
        self.import_errors = {}

    def safe_import(self, module_path: str, class_name: str = None, fallback=None):
        """安全导入模块或类"""
        try:
            # 修复core模块路径问题
            if module_path.startswith("core."):
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                # 从 backend/roles/yuheng_star/services/ 到 backend/
                backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
                core_path = os.path.join(backend_dir, "core")

                if core_path not in sys.path:
                    sys.path.insert(0, core_path)

                # 移除core.前缀
                module_path = module_path[5:]  # 移除"core."

            module = importlib.import_module(module_path)
            if class_name:
                return getattr(module, class_name)
            return module
        except ImportError as e:
            self.import_errors[module_path] = str(e)
            logging.warning(f"导入失败 {module_path}: {e}")
            return fallback
        except Exception as e:
            self.import_errors[module_path] = str(e)
            logging.error(f"导入异常 {module_path}: {e}")
            return fallback

# 创建核心系统管理器
core_manager = CoreSystemsManager()

# 1. 传奇记忆系统
legendary_memory_interface = core_manager.safe_import(
    "core.domain.memory.legendary.interface",
    "legendary_memory_interface"
)

MessageType = core_manager.safe_import(
    "core.domain.memory.legendary.models",
    "MessageType"
)

MemoryScope = core_manager.safe_import(
    "core.domain.memory.legendary.models",
    "MemoryScope"
)

MemoryPriority = core_manager.safe_import(
    "core.domain.memory.legendary.models",
    "MemoryPriority"
)

get_memory_config = core_manager.safe_import(
    "roles.yuheng_star.config.deepseek_config",
    "get_memory_config"
)

# 2. 绩效监控系统
StarPerformanceMonitor = core_manager.safe_import(
    "performance.star_performance_monitor",
    "StarPerformanceMonitor"
)

PerformanceMetricType = core_manager.safe_import(
    "performance.star_performance_monitor",
    "PerformanceMetricType"
)

# 3. 层级权限系统
EnhancedSevenStarsHierarchy = core_manager.safe_import(
    "enhanced_seven_stars_hierarchy",
    "EnhancedSevenStarsHierarchy"
)

# 4. DeepSeek配置
get_deepseek_config = core_manager.safe_import(
    "roles.yuheng_star.config.deepseek_config",
    "get_deepseek_config"
)

# 检查核心系统可用性
CORE_SYSTEMS_AVAILABLE = all([
    legendary_memory_interface is not None,
    MessageType is not None,
    StarPerformanceMonitor is not None,
    EnhancedSevenStarsHierarchy is not None
])

# 创建性能监控实例（如果可用）
performance_monitor = None
if StarPerformanceMonitor:
    try:
        performance_monitor = StarPerformanceMonitor()
    except Exception as e:
        logging.warning(f"绩效监控系统初始化失败: {e}")
        performance_monitor = None

# 导入其他服务
from .virtual_trading_engine import VirtualTradingEngine
from .order_management_service import OrderManagementService
from .position_management_service import PositionManagementService
# 移除风险评估服务导入 - 这是天玑星的职责，通过辩论机制获取
# from .risk_assessment_service import RiskAssessmentService
from .advanced_execution_system import AdvancedExecutionSystem
from .intelligent_execution_and_profit_service import IntelligentExecutionAndProfitService

logger = logging.getLogger(__name__)

class TradeAction(Enum):
    """交易动作"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"

class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

@dataclass
class TradeRequest:
    """交易请求"""
    stock_code: str
    stock_name: str
    action: TradeAction
    quantity: int
    price: Optional[float] = None
    order_type: OrderType = OrderType.MARKET
    strategy_id: Optional[str] = None
    risk_params: Optional[Dict[str, Any]] = None
    strategy_signal: Optional[Dict[str, Any]] = None
    execution_constraints: Optional[Dict[str, Any]] = None

@dataclass
class TradeResult:
    """交易结果"""
    success: bool
    order_id: Optional[str] = None
    executed_quantity: int = 0
    executed_price: float = 0.0
    commission: float = 0.0
    error_message: Optional[str] = None
    risk_assessment: Optional[Dict[str, Any]] = None

class TradingExecutionService:
    """玉衡星交易执行服务"""
    
    def __init__(self):
        self.role_name = "玉衡星"
        self.service_name = "交易执行服务"
        
        # 初始化子服务
        self.trading_engine = VirtualTradingEngine()
        self.order_manager = OrderManagementService()
        self.position_manager = PositionManagementService()
        # 移除风险评估服务 - 这是天玑星的职责，通过辩论机制获取
        # self.risk_assessor = RiskAssessmentService()
        self.advanced_executor = AdvancedExecutionSystem()
        self.intelligent_executor = IntelligentExecutionAndProfitService()

        # 辩论系统接口
        self.debate_system = None
        try:
            from roles.tianquan_star.services.enhanced_four_stars_debate import EnhancedFourStarsDebate
            self.debate_system = EnhancedFourStarsDebate()
            logger.info("  辩论系统接口初始化成功")
        except ImportError:
            logger.warning("  辩论系统不可用，使用简化风险评估")
        
        # 服务状态
        self.is_initialized = False
        self.active_orders = {}
        self.execution_stats = {
            "total_orders": 0,
            "successful_orders": 0,
            "failed_orders": 0,
            "total_volume": 0.0,
            "total_commission": 0.0
        }

        # 初始化四大核心系统
        self._init_core_systems()

        logger.info("玉衡星交易执行服务初始化完成")

    def _init_core_systems(self):
        """增强四大核心系统初始化 - 支持容错和降级"""
        system_status = {
            "memory_system": False,
            "deepseek_config": False,
            "performance_monitor": False,
            "hierarchy_system": False
        }

        try:
            # 1. 传奇记忆系统
            if legendary_memory_interface:
                self.memory_system = legendary_memory_interface
                system_status["memory_system"] = True
                logger.info("[SUCCESS] 传奇记忆系统初始化成功")
            else:
                self.memory_system = self._create_fallback_memory_system()
                logger.warning("⚠️ 传奇记忆系统不可用，使用降级模式")

            # 2. DeepSeek人设配置
            if get_memory_config:
                try:
                    self.deepseek_memory_config = get_memory_config()
                    system_status["deepseek_config"] = True
                    logger.info("[SUCCESS] DeepSeek配置加载成功")
                except Exception as e:
                    logger.warning(f"⚠️ DeepSeek配置加载失败: {e}")
                    self.deepseek_memory_config = self._create_fallback_deepseek_config()
            else:
                self.deepseek_memory_config = self._create_fallback_deepseek_config()
                logger.warning("⚠️ DeepSeek配置不可用，使用默认配置")

            # 3. 绩效监控系统
            if performance_monitor:
                self.performance_monitor = performance_monitor
                system_status["performance_monitor"] = True
                logger.info("[SUCCESS] 绩效监控系统初始化成功")
            else:
                self.performance_monitor = self._create_fallback_performance_monitor()
                logger.warning("⚠️ 绩效监控系统不可用，使用降级模式")

            # 4. 层级权限系统
            if EnhancedSevenStarsHierarchy:
                try:
                    self.permission_system = EnhancedSevenStarsHierarchy()
                    system_status["hierarchy_system"] = True
                    logger.info("[SUCCESS] 层级权限系统初始化成功")
                except Exception as e:
                    logger.warning(f"⚠️ 层级权限系统初始化失败: {e}")
                    self.permission_system = self._create_fallback_hierarchy_system()
            else:
                self.permission_system = self._create_fallback_hierarchy_system()
                logger.warning("⚠️ 层级权限系统不可用，使用降级模式")

            # 系统状态报告
            active_systems = sum(system_status.values())
            total_systems = len(system_status)

            if active_systems == total_systems:
                logger.info(f"[COMPLETE] 玉衡星四大核心系统全部初始化完成 ({active_systems}/{total_systems})")
            elif active_systems >= total_systems // 2:
                logger.warning(f"[WARNING] 玉衡星核心系统部分可用 ({active_systems}/{total_systems})，使用混合模式")
            else:
                logger.error(f"[ERROR] 玉衡星核心系统大部分不可用 ({active_systems}/{total_systems})，使用降级模式")

            # 记录导入错误
            if core_manager.import_errors:
                logger.warning(f"导入错误详情: {core_manager.import_errors}")

        except Exception as e:
            logger.error(f"[CRITICAL] 核心系统初始化严重失败: {e}")
            self._init_fallback_systems()

    def _create_fallback_memory_system(self):
        """创建降级记忆系统"""
        class FallbackMemorySystem:
            async def add_yuheng_memory(self, content: str, message_type=None, context=None):
                logger.debug(f"降级记忆记录: {content}")
                return type('Result', (), {'success': True, 'message': 'Fallback mode'})()

            async def search_yuheng_memories(self, query: str, **kwargs):
                return []

            async def get_recent_yuheng_memories(self, limit: int = 10):
                return []

        return FallbackMemorySystem()

    def _create_fallback_deepseek_config(self):
        """创建降级DeepSeek配置"""
        return {
            "persona": "玉衡星-交易执行官",
            "memory_triggers": {
                "execution_completed": "execution_history_memory",
                "cost_anomaly": "order_management_memory",
                "liquidity_issue": "cost_analysis_memory",
                "execution_optimization": "execution_optimization_memory"
            },
            "fallback_mode": True
        }

    def _create_fallback_performance_monitor(self):
        """创建降级绩效监控系统"""
        class FallbackPerformanceMonitor:
            async def record_performance(self, star_name: str, metric_type, value: float, context=None):
                logger.debug(f"降级绩效记录: {star_name} - {metric_type} = {value}")
                return True

            async def get_star_performance(self, star_name: str, period_days: int = 30):
                return None

        return FallbackPerformanceMonitor()

    def _create_fallback_hierarchy_system(self):
        """创建降级层级权限系统"""
        class FallbackHierarchySystem:
            def get_star_authority_level(self, star_name: str) -> int:
                return 5  # 玉衡星默认权限等级

            def check_permission(self, star_name: str, action: str) -> bool:
                return True  # 降级模式下允许所有操作

        return FallbackHierarchySystem()

    def _init_fallback_systems(self):
        """初始化所有降级系统"""
        self.memory_system = self._create_fallback_memory_system()
        self.deepseek_memory_config = self._create_fallback_deepseek_config()
        self.performance_monitor = self._create_fallback_performance_monitor()
        self.permission_system = self._create_fallback_hierarchy_system()
        logger.warning("[FALLBACK] 所有系统已切换到降级模式")

    async def _trigger_deepseek_memory(self, trigger_name: str, content: str, context: Dict[str, Any] = None):
        """根据DeepSeek配置触发记忆"""
        try:
            if not self.memory_system or not self.deepseek_memory_config:
                return

            # 获取触发器对应的记忆类型
            memory_type_mapping = {
                "execution_completed": "execution_history_memory",
                "cost_anomaly": "order_management_memory",
                "liquidity_issue": "cost_analysis_memory",
                "execution_optimization": "execution_optimization_memory"
            }

            memory_type = memory_type_mapping.get(trigger_name)
            if memory_type:
                # 根据记忆类型选择消息类型
                message_type_mapping = {
                    "execution_history_memory": MessageType.TRADING_EXECUTION,
                    "order_management_memory": MessageType.TRADING_EXECUTION,
                    "cost_analysis_memory": MessageType.SYSTEM_NOTIFICATION,
                    "execution_optimization_memory": MessageType.SYSTEM_NOTIFICATION
                }

                message_type = message_type_mapping.get(memory_type, MessageType.GENERAL)

                # 添加到传奇记忆系统
                result = await self.memory_system.add_yuheng_memory(
                    content=content,
                    message_type=message_type
                )

                if result.success:
                    logger.info(f"玉衡星记忆触发成功: {trigger_name} -> {memory_type}")
                else:
                    logger.error(f"玉衡星记忆触发失败: {result.message}")

        except Exception as e:
            logger.error(f"DeepSeek记忆触发失败: {e}")

    async def _record_performance_metric(self, metric_name: str, value: float, context: Dict[str, Any] = None):
        """记录绩效指标"""
        try:
            if self.performance_monitor:
                from core.performance.star_performance_monitor import PerformanceMetricType

                # 映射指标名称到枚举类型
                metric_type_mapping = {
                    "execution_success_rate": PerformanceMetricType.SUCCESS_RATE,
                    "cost_efficiency": PerformanceMetricType.EFFICIENCY,
                    "slippage_control": PerformanceMetricType.QUALITY_SCORE,
                    "order_management": PerformanceMetricType.ACCURACY
                }

                metric_type = metric_type_mapping.get(metric_name, PerformanceMetricType.SUCCESS_RATE)

                await self.performance_monitor.record_performance(
                    star_name="玉衡星",
                    metric_type=metric_type,
                    value=value,
                    context=context or {}
                )
                logger.debug(f"玉衡星绩效记录: {metric_name}={value}")
        except Exception as e:
            logger.error(f"绩效记录失败: {e}")
    
    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化各个子服务
            await self._initialize_sub_services()
            
            self.is_initialized = True
            logger.info("玉衡星交易执行服务初始化成功")
            
        except Exception as e:
            logger.error(f"玉衡星交易执行服务初始化失败: {e}")
            raise
    
    async def _initialize_sub_services(self):
        """初始化子服务"""
        try:
            # 初始化交易引擎
            if hasattr(self.trading_engine, 'initialize'):
                await self.trading_engine.initialize()
            
            # 初始化订单管理
            if hasattr(self.order_manager, 'initialize'):
                await self.order_manager.initialize()
            
            # 初始化持仓管理
            if hasattr(self.position_manager, 'initialize'):
                await self.position_manager.initialize()

            # 风险评估由天玑星通过辩论机制提供，无需初始化

            logger.info("所有子服务初始化完成")
            
        except Exception as e:
            logger.error(f"子服务初始化失败: {e}")
            raise
    
    async def execute_trade(self, trade_request: TradeRequest) -> TradeResult:
        """执行交易"""
        try:
            logger.info(f"玉衡星开始执行交易: {trade_request.action.value} {trade_request.stock_code} {trade_request.quantity}股")
            
            # 1. 通过辩论机制获取风险评估（天玑星负责）
            risk_result = await self._get_risk_assessment_via_debate(trade_request)
            if not risk_result.get("approved", True):  # 默认通过，如果辩论系统不可用
                return TradeResult(
                    success=False,
                    error_message=f"辩论系统风险评估未通过: {risk_result.get('reason', '未知原因')}",
                    risk_assessment=risk_result
                )
            
            # 2. 选择执行策略
            execution_strategy = await self._select_execution_strategy(trade_request)
            
            # 3. 执行交易
            if execution_strategy == "advanced":
                result = await self._execute_with_advanced_system(trade_request)
            elif execution_strategy == "intelligent":
                result = await self._execute_with_intelligent_system(trade_request)
            else:
                result = await self._execute_with_basic_engine(trade_request)
            
            # 4. 更新统计信息
            await self._update_execution_stats(result)
            
            # 5. 记录交易结果和触发记忆
            if result.success:
                logger.info(f"交易执行成功: 订单ID {result.order_id}, 成交价格 {result.executed_price:.2f}")

                # 触发DeepSeek记忆
                await self._trigger_deepseek_memory(
                    "execution_completed",
                    f"交易执行成功: {trade_request.action.value} {trade_request.stock_code} {result.executed_quantity}股 @{result.executed_price:.2f}",
                    {"order_id": result.order_id, "commission": result.commission}
                )

                # 记录绩效指标
                await self._record_performance_metric(
                    "execution_success_rate",
                    1.0,
                    {"stock_code": trade_request.stock_code, "action": trade_request.action.value}
                )
            else:
                logger.warning(f"交易执行失败: {result.error_message}")

                # 记录失败绩效
                await self._record_performance_metric(
                    "execution_success_rate",
                    0.0,
                    {"stock_code": trade_request.stock_code, "error": result.error_message}
                )

            return result
            
        except Exception as e:
            logger.error(f"交易执行异常: {e}")
            return TradeResult(
                success=False,
                error_message=f"交易执行异常: {str(e)}"
            )
    
    async def _get_risk_assessment_via_debate(self, trade_request: TradeRequest) -> Dict[str, Any]:
        """通过辩论机制获取风险评估（天玑星负责）"""
        try:
            if self.debate_system:
                # 构造辩论分析数据
                analysis_data = {
                    "stock_code": trade_request.stock_code,
                    "action": trade_request.action.value,
                    "quantity": trade_request.quantity,
                    "price": trade_request.price or 0.0,
                    "strategy_id": trade_request.strategy_id,
                    "execution_context": {
                        "urgency": "normal",
                        "market_session": "regular",
                        "liquidity_requirement": "standard"
                    }
                }

                # 发起四星辩论
                task_id = f"trade_risk_{trade_request.stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                debate_result = await self.debate_system.conduct_enhanced_debate(
                    task_id=task_id,
                    target_stock=trade_request.stock_code,
                    initial_analysis=analysis_data
                )

                # 从辩论结果中提取风险评估
                if debate_result.get("success", False):
                    final_decision = debate_result.get("final_decision", {})
                    risk_assessment = final_decision.get("risk_assessment", {})

                    # 根据辩论结果决定是否批准交易
                    final_recommendation = final_decision.get("final_recommendation", "hold")
                    # 修复大小写匹配问题
                    approved = final_recommendation.lower() in ["buy", "sell"] and final_recommendation.lower() == trade_request.action.value.lower()

                    return {
                        "approved": approved,
                        "reason": f"四星辩论决策: {final_recommendation}",
                        "risk_level": risk_assessment.get("risk_level", "medium"),
                        "confidence": final_decision.get("final_confidence", 0.5),
                        "debate_consensus": debate_result.get("consensus_level", 0.0),
                        "tianji_assessment": risk_assessment  # 天玑星的专业风险评估
                    }
                else:
                    logger.warning("辩论系统执行失败，使用默认风险评估")
                    return self._get_default_risk_assessment(trade_request)
            else:
                # 辩论系统不可用，使用简化评估
                return self._get_default_risk_assessment(trade_request)

        except Exception as e:
            logger.error(f"辩论系统风险评估失败: {e}")
            return self._get_default_risk_assessment(trade_request)

    def _get_default_risk_assessment(self, trade_request: TradeRequest) -> Dict[str, Any]:
        """获取默认风险评估（辩论系统不可用时的降级方案）"""
        # 简化的风险评估逻辑
        risk_level = "medium"
        approved = True

        # 基于交易量的简单风险评估
        if trade_request.quantity > 100000:
            risk_level = "high"
            approved = False
        elif trade_request.quantity > 50000:
            risk_level = "medium"
        else:
            risk_level = "low"

        return {
            "approved": approved,
            "reason": f"简化风险评估: {risk_level}风险",
            "risk_level": risk_level,
            "confidence": 0.6,
            "debate_consensus": 0.0,
            "fallback_mode": True
        }
    
    async def _select_execution_strategy(self, trade_request: TradeRequest) -> str:
        """选择执行策略"""
        try:
            # 根据交易规模和市场条件选择执行策略
            if trade_request.quantity > 10000:
                return "advanced"  # 大单使用高级执行系统
            elif trade_request.strategy_id and "intelligent" in trade_request.strategy_id:
                return "intelligent"  # 智能策略使用智能执行系统
            else:
                return "basic"  # 默认使用基础交易引擎
                
        except Exception as e:
            logger.warning(f"执行策略选择失败: {e}, 使用基础策略")
            return "basic"
    
    async def _execute_with_advanced_system(self, trade_request: TradeRequest) -> TradeResult:
        """使用高级执行系统"""
        try:
            result = await self.advanced_executor.execute_smart_order(
                stock_code=trade_request.stock_code,
                direction=trade_request.action.value,
                quantity=trade_request.quantity,
                strategy_signal={
                    "signal_type": trade_request.action.value,
                    "confidence": 0.8,
                    "strategy_id": trade_request.strategy_id
                }
            )
            
            if result.get("success", False):
                # 获取执行价格
                executed_price = result.get("average_price", trade_request.price)
                if not executed_price:
                    try:
                        from .real_data_integration_service import real_data_service
                        price_data = await real_data_service.get_real_stock_price(trade_request.stock_code)
                        if price_data.get("success"):
                            executed_price = price_data["data"]["current_price"]
                        else:
                            executed_price = 10.0
                    except:
                        executed_price = 10.0

                return TradeResult(
                    success=True,
                    order_id=result.get("order_id"),
                    executed_quantity=result.get("executed_quantity", trade_request.quantity),
                    executed_price=executed_price,
                    commission=result.get("total_cost", 0.0)
                )
            else:
                return TradeResult(
                    success=False,
                    error_message=result.get("error", "高级执行系统执行失败")
                )
                
        except Exception as e:
            logger.error(f"高级执行系统执行失败: {e}")
            return TradeResult(
                success=False,
                error_message=f"高级执行系统异常: {str(e)}"
            )
    
    async def _execute_with_intelligent_system(self, trade_request: TradeRequest) -> TradeResult:
        """使用智能执行系统"""
        try:
            from .intelligent_execution_and_profit_service import ExecutionAction

            # 转换交易动作
            intelligent_action = ExecutionAction.BUY if trade_request.action == TradeAction.BUY else ExecutionAction.SELL
            
            trade_record = await self.intelligent_executor.execute_trade(
                stock_code=trade_request.stock_code,
                stock_name=trade_request.stock_name,
                action=intelligent_action,
                quantity=trade_request.quantity,
                price=trade_request.price or 0.0,
                strategy_name=trade_request.strategy_id or "default"
            )
            
            if trade_record:
                return TradeResult(
                    success=True,
                    order_id=trade_record.trade_id,
                    executed_quantity=trade_record.quantity,
                    executed_price=trade_record.price,
                    commission=trade_record.commission
                )
            else:
                return TradeResult(
                    success=False,
                    error_message="智能执行系统执行失败"
                )
                
        except Exception as e:
            logger.error(f"智能执行系统执行失败: {e}")
            return TradeResult(
                success=False,
                error_message=f"智能执行系统异常: {str(e)}"
            )
    
    async def _execute_with_basic_engine(self, trade_request: TradeRequest) -> TradeResult:
        """使用基础交易引擎"""
        try:
            # 如果没有提供价格，获取当前市场价格
            execution_price = trade_request.price
            if not execution_price:
                try:
                    from .real_data_integration_service import real_data_service
                    price_data = await real_data_service.get_real_stock_price(trade_request.stock_code)
                    if price_data.get("success"):
                        execution_price = price_data["data"]["current_price"]
                        logger.info(f"获取当前市场价格: {trade_request.stock_code} = ¥{execution_price}")
                    else:
                        execution_price = 10.0  # 默认价格
                        logger.warning(f"无法获取市场价格，使用默认价格: ¥{execution_price}")
                except Exception as e:
                    execution_price = 10.0
                    logger.warning(f"价格获取异常，使用默认价格: {e}")

            result = await self.trading_engine.submit_order(
                stock_code=trade_request.stock_code,
                action=trade_request.action.value,
                quantity=trade_request.quantity,
                price=execution_price,
                order_type=trade_request.order_type.value
            )

            if result.get("status") == "submitted":
                return TradeResult(
                    success=True,
                    order_id=result.get("order_id"),
                    executed_quantity=trade_request.quantity,
                    executed_price=execution_price,
                    commission=result.get("commission", 0.0)
                )
            else:
                return TradeResult(
                    success=False,
                    error_message=result.get("error", "基础交易引擎执行失败")
                )
                
        except Exception as e:
            logger.error(f"基础交易引擎执行失败: {e}")
            return TradeResult(
                success=False,
                error_message=f"基础交易引擎异常: {str(e)}"
            )
    
    async def _update_execution_stats(self, result: TradeResult):
        """更新执行统计"""
        try:
            self.execution_stats["total_orders"] += 1
            
            if result.success:
                self.execution_stats["successful_orders"] += 1
                self.execution_stats["total_volume"] += result.executed_quantity * result.executed_price
                self.execution_stats["total_commission"] += result.commission
            else:
                self.execution_stats["failed_orders"] += 1
                
        except Exception as e:
            logger.warning(f"更新执行统计失败: {e}")
    
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """获取订单状态"""
        try:
            return await self.order_manager.get_order_status(order_id)
        except Exception as e:
            logger.error(f"获取订单状态失败: {e}")
            return None
    
    async def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            return await self.order_manager.cancel_order(order_id)
        except Exception as e:
            logger.error(f"取消订单失败: {e}")
            return False
    
    async def get_positions(self) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            return await self.position_manager.get_positions()
        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return []
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            return await self.trading_engine.get_account_balance()
        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            return {"error": str(e)}
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计"""
        success_rate = 0.0
        if self.execution_stats["total_orders"] > 0:
            success_rate = self.execution_stats["successful_orders"] / self.execution_stats["total_orders"]
        
        return {
            **self.execution_stats,
            "success_rate": success_rate,
            "average_commission": (
                self.execution_stats["total_commission"] / self.execution_stats["successful_orders"]
                if self.execution_stats["successful_orders"] > 0 else 0.0
            )
        }
    
    async def execute_trading_decision(self, decision: Dict[str, Any], risk_assessment: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行交易决策 - 工作流引擎接口"""
        try:
            stock_code = decision.get("stock_code")
            action = decision.get("action")
            quantity = decision.get("quantity", 1000)
            target_price = decision.get("target_price")

            if not stock_code or not action:
                return {
                    "success": False,
                    "error": "交易决策信息不完整",
                    "timestamp": datetime.now().isoformat()
                }

            # 转换为TradeRequest
            trade_action = TradeAction.BUY if action == "buy" else TradeAction.SELL if action == "sell" else TradeAction.HOLD

            if trade_action == TradeAction.HOLD:
                return {
                    "success": True,
                    "action": "hold",
                    "message": "决策为持有，无需执行交易",
                    "stock_code": stock_code,
                    "timestamp": datetime.now().isoformat()
                }

            trade_request = TradeRequest(
                stock_code=stock_code,
                stock_name=stock_code,  # 简化处理
                action=trade_action,
                quantity=quantity,
                price=target_price,
                order_type=OrderType.LIMIT if target_price else OrderType.MARKET,
                strategy_id="workflow_decision",
                risk_params=risk_assessment
            )

            # 执行交易
            result = await self.execute_trade(trade_request)

            # 转换为工作流引擎期望的格式
            return {
                "success": result.success,
                "stock_code": stock_code,
                "execution_result": {
                    "order_id": result.order_id,
                    "action": action,
                    "quantity": result.executed_quantity,
                    "executed_price": result.executed_price,
                    "commission": result.commission,
                    "error_message": result.error_message
                } if result.success else {
                    "error": result.error_message
                },
                "risk_assessment": result.risk_assessment,
                "executor": "玉衡星交易执行服务",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"执行交易决策失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "role_name": self.role_name,
            "service_name": self.service_name,
            "is_initialized": self.is_initialized,
            "active_orders_count": len(self.active_orders),
            "execution_stats": self.get_execution_statistics(),
            "sub_services": {
                "trading_engine": "active",
                "order_manager": "active",
                "position_manager": "active",
                "debate_system": "active" if self.debate_system else "fallback",
                "advanced_executor": "active",
                "intelligent_executor": "active"
            },
            "timestamp": datetime.now().isoformat()
        }

# 全局实例
trading_execution_service = TradingExecutionService()
