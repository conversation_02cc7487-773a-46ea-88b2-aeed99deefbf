#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股年度数据收集器
收集所有A股1年内的完整数据
"""

import requests
import sqlite3
import os
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend/logs/annual_collection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class AnnualAStockCollector:
    """A股年度数据收集器"""
    
    def __init__(self):
        self.service_name = "AnnualAStockCollector"
        self.version = "1.0.0"
        self.db_path = "backend/data/stock_database.db"
        self.collected_stocks = 0
        self.collected_records = 0
        self.failed_stocks = 0
        self.lock = threading.Lock()
        
        # 配置请求会话
        self.session = requests.Session()
        self.session.proxies = {}
        self.session.verify = False
        
        # 确保目录存在
        os.makedirs("backend/logs", exist_ok=True)
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def get_all_a_stocks(self) -> List[Dict[str, Any]]:
        """获取全部A股股票列表"""
        try:
            logger.info("🔍 获取全部A股股票列表...")
            
            all_stocks = []
            page = 1
            page_size = 100  # 东方财富API实际每页返回100只
            
            while True:
                url = 'http://push2.eastmoney.com/api/qt/clist/get'
                params = {
                    'pn': page,
                    'pz': page_size,
                    'po': 1,
                    'np': 1,
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': 2,
                    'invt': 2,
                    'fid': 'f3',
                    'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',  # A股
                    'fields': 'f12,f14,f2,f3,f4,f5,f6,f7,f8,f9,f10,f15,f16,f17,f18'
                }

                response = self.session.get(url, params=params, timeout=15)

                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                        stocks = data['data']['diff']
                        total_stocks_api = data['data'].get('total', 0)

                        for stock in stocks:
                            stock_code = stock.get('f12', '')
                            stock_name = stock.get('f14', '')

                            if stock_code and stock_name:
                                # 确定交易所和secid
                                if stock_code.startswith('6'):
                                    exchange = 'SSE'
                                    secid = f'1.{stock_code}'
                                else:
                                    exchange = 'SZSE'
                                    secid = f'0.{stock_code}'

                                all_stocks.append({
                                    'code': stock_code,
                                    'name': stock_name,
                                    'exchange': exchange,
                                    'secid': secid
                                })

                        logger.info(f"📄 第{page}页: 获取{len(stocks)}只股票，累计{len(all_stocks)}只 (API总数: {total_stocks_api})")

                        # 检查是否已获取所有股票
                        if len(stocks) < page_size:
                            logger.info(f"✅ 已获取所有股票: {len(all_stocks)}/{total_stocks_api}")
                            break

                        # 安全检查：如果已获取的股票数接近总数，也停止
                        if len(all_stocks) >= total_stocks_api * 0.99:
                            logger.info(f"✅ 已获取足够股票: {len(all_stocks)}/{total_stocks_api}")
                            break

                        page += 1
                        time.sleep(0.5)  # 控制请求频率
                    else:
                        logger.warning(f"⚠️ 第{page}页无数据，可能已到最后一页")
                        break
                else:
                    logger.error(f"❌ 第{page}页请求失败: {response.status_code}")
                    break
            
            logger.info(f"✅ 成功获取 {len(all_stocks)} 只A股股票")
            return all_stocks
            
        except Exception as e:
            logger.error(f"❌ 获取股票列表失败: {e}")
            return []
    
    def collect_stock_annual_data(self, stock_info: Dict[str, Any]) -> bool:
        """收集单只股票的年度数据"""
        try:
            stock_code = stock_info['code']
            stock_name = stock_info['name']
            secid = stock_info['secid']
            
            # 使用固定的时间范围（与第一次成功收集相同）
            url = 'http://push2his.eastmoney.com/api/qt/stock/kline/get'
            params = {
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K
                'fqt': '1',    # 前复权
                'beg': '20240619',  # 固定开始日期
                'end': '20250619'   # 固定结束日期
            }
            
            # 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    logger.debug(f"🔄 {stock_code} 尝试第{attempt+1}次请求...")
                    response = self.session.get(url, params=params, timeout=20)
                    logger.debug(f"📡 {stock_code} API响应: {response.status_code}")

                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data and data['data'] and 'klines' in data['data']:
                            klines = data['data']['klines']

                            if klines:
                                # 解析和保存数据
                                records_saved = self.save_stock_data(stock_info, klines)

                                with self.lock:
                                    self.collected_stocks += 1
                                    self.collected_records += records_saved

                                logger.debug(f"✅ {stock_code} {stock_name}: {records_saved} 条记录")
                                return True
                            else:
                                logger.debug(f"⚠️ {stock_code} 无历史数据（可能是新股）")
                                with self.lock:
                                    self.failed_stocks += 1
                                return False
                        else:
                            logger.debug(f"⚠️ {stock_code} API返回格式异常")
                            with self.lock:
                                self.failed_stocks += 1
                            return False

                    elif response.status_code in [502, 503, 429, 500]:
                        # API限制，需要重试
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 5
                            logger.debug(f"🔄 {stock_code} API限制({response.status_code})，等待{wait_time}秒重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            logger.debug(f"⚠️ {stock_code} API请求失败: {response.status_code}")
                            with self.lock:
                                self.failed_stocks += 1
                            return False

                    else:
                        logger.debug(f"⚠️ {stock_code} API请求失败: {response.status_code}")
                        with self.lock:
                            self.failed_stocks += 1
                        return False

                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        logger.debug(f"🔄 {stock_code} 超时，重试...")
                        time.sleep(2)
                        continue
                    else:
                        logger.debug(f"⚠️ {stock_code} 请求超时")
                        with self.lock:
                            self.failed_stocks += 1
                        return False

                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.debug(f"🔄 {stock_code} 异常，重试: {e}")
                        time.sleep(1)
                        continue
                    else:
                        logger.debug(f"⚠️ {stock_code} 请求异常: {e}")
                        with self.lock:
                            self.failed_stocks += 1
                        return False
                
        except Exception as e:
            logger.debug(f"⚠️ 收集 {stock_code} 数据异常: {e}")
            with self.lock:
                self.failed_stocks += 1
            return False
    
    def save_stock_data(self, stock_info: Dict[str, Any], klines: List[str]) -> int:
        """保存股票数据到数据库"""
        try:
            stock_code = stock_info['code']
            stock_name = stock_info['name']
            exchange = stock_info['exchange']
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            
            # 更新股票基本信息
            cursor.execute("""
                INSERT OR REPLACE INTO stock_info 
                (stock_code, stock_name, exchange, updated_at)
                VALUES (?, ?, ?, ?)
            """, (stock_code, stock_name, exchange, current_time))
            
            # 插入历史数据
            records_inserted = 0
            for kline in klines:
                parts = kline.split(',')
                if len(parts) >= 11:
                    try:
                        cursor.execute("""
                            INSERT OR REPLACE INTO daily_data 
                            (stock_code, trade_date, open_price, close_price, high_price, low_price,
                             volume, amount, change_percent, turnover_rate, data_source, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            stock_code,
                            parts[0],  # 日期
                            float(parts[1]),  # 开盘价
                            float(parts[2]),  # 收盘价
                            float(parts[3]),  # 最高价
                            float(parts[4]),  # 最低价
                            int(parts[5]),    # 成交量
                            float(parts[6]),  # 成交额
                            float(parts[8]),  # 涨跌幅
                            float(parts[10]) if parts[10] != '-' else 0.0,  # 换手率
                            'eastmoney_annual',
                            current_time,
                            current_time
                        ))
                        records_inserted += 1
                    except (ValueError, IndexError) as e:
                        logger.debug(f"解析数据行失败 {stock_code}: {e}")
                        continue
            
            conn.commit()
            conn.close()
            
            return records_inserted
            
        except Exception as e:
            logger.error(f"❌ 保存数据失败 {stock_code}: {e}")
            return 0
    
    def run_annual_collection(self, max_workers: int = 10, batch_size: int = 100):
        """运行年度数据收集"""
        try:
            start_time = datetime.now()
            logger.info("🚀 开始A股年度数据收集...")
            
            # 1. 获取全部股票列表
            stock_list = self.get_all_a_stocks()
            if not stock_list:
                logger.error("❌ 无法获取股票列表，退出")
                return False
            
            logger.info(f"📊 开始收集 {len(stock_list)} 只股票的年度数据")
            
            # 2. 多线程收集数据
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 分批处理
                for i in range(0, len(stock_list), batch_size):
                    batch = stock_list[i:i + batch_size]
                    
                    logger.info(f"📦 处理第 {i//batch_size + 1} 批，股票 {i+1}-{min(i+batch_size, len(stock_list))}")
                    
                    # 提交任务
                    futures = [
                        executor.submit(self.collect_stock_annual_data, stock)
                        for stock in batch
                    ]
                    
                    # 等待批次完成
                    for future in as_completed(futures):
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"任务执行失败: {e}")
                    
                    # 显示进度
                    progress = min(i + batch_size, len(stock_list))
                    logger.info(f"📈 进度: {progress}/{len(stock_list)} ({progress/len(stock_list)*100:.1f}%)")
                    logger.info(f"📊 统计: 成功{self.collected_stocks}, 失败{self.failed_stocks}, 记录{self.collected_records}")
                    
                    # 批次间休息，避免API限制
                    if i + batch_size < len(stock_list):
                        time.sleep(5)  # 恢复第一次成功的休息时间
            
            # 3. 生成收集报告
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.generate_annual_report(len(stock_list), duration)
            
            logger.info("🎉 A股年度数据收集完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 年度数据收集失败: {e}")
            return False
    
    def generate_annual_report(self, total_stocks: int, duration: float):
        """生成年度收集报告"""
        try:
            # 验证数据库中的数据
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计年度数据
            cursor.execute("SELECT COUNT(*) FROM daily_data WHERE data_source = 'eastmoney_annual'")
            annual_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM daily_data WHERE data_source = 'eastmoney_annual'")
            annual_stocks = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM daily_data WHERE data_source = 'eastmoney_annual'")
            date_range = cursor.fetchone()
            
            # 统计总数据
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            total_stock_info = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM daily_data")
            total_daily_data = cursor.fetchone()[0]
            
            conn.close()
            
            report = f"""
📊 A股年度数据收集报告
==========================================
🕐 收集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
⏱️ 收集耗时: {duration/3600:.1f} 小时
📈 目标股票: {total_stocks} 只
✅ 成功收集: {self.collected_stocks} 只
❌ 失败数量: {self.failed_stocks} 只
📊 年度数据: {annual_records} 条
📋 有数据股票: {annual_stocks} 只
📅 数据时间范围: {date_range[0]} 至 {date_range[1]}
💾 数据库总股票: {total_stock_info} 只
💾 数据库总记录: {total_daily_data} 条
🎯 成功率: {(self.collected_stocks / total_stocks * 100):.1f}%
📈 平均每股记录: {annual_records / annual_stocks if annual_stocks > 0 else 0:.1f} 条
==========================================
"""
            
            # 保存报告
            report_path = f"backend/data/annual_collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(report)
            logger.info(f"📄 报告已保存: {report_path}")
            
        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")

def main():
    """主函数"""
    collector = AnnualAStockCollector()
    
    # 开始年度数据收集（恢复第一次成功的配置）
    success = collector.run_annual_collection(max_workers=8, batch_size=50)
    
    if success:
        print("🎉 A股年度数据收集成功！")
    else:
        print("❌ A股年度数据收集失败")

if __name__ == "__main__":
    main()
