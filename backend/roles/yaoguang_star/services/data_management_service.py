#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理服务
瑶光星的数据管理核心服务 - 仅使用本地股票库数据
"""

import asyncio
import sqlite3
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

# 导入统一数据源管理器
try:
    from backend.shared.data_sources.unified_data_source_manager import unified_data_source_manager
    UNIFIED_DATA_SOURCE_AVAILABLE = True
except ImportError:
    UNIFIED_DATA_SOURCE_AVAILABLE = False
    unified_data_source_manager = None

logger = logging.getLogger(__name__)

class DataManagementService:
    """数据管理服务 - 仅基于本地股票库"""

    def __init__(self):
        self.local_db_path = Path(__file__).parent.parent.parent.parent / "data" / "stock_database.db"
        self.data_sources = {
            "local_stock_db": str(self.local_db_path),
            "unified_data_source": "统一数据源管理器" if UNIFIED_DATA_SOURCE_AVAILABLE else "不可用"
        }
        self.cache_manager = None
        self.quality_controller = None

        logger.info("瑶光星数据管理服务初始化完成")
        logger.info(f"本地数据库: {self.local_db_path}")
        logger.info(f"统一数据源: {'可用' if UNIFIED_DATA_SOURCE_AVAILABLE else '不可用'}")

    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # 检查本地数据库状态
        db_status = await self.check_local_database_status()

        return {
            "status": "active",
            "data_sources": list(self.data_sources.keys()),
            "cache_status": "healthy",
            "data_quality": "good",
            "local_db_status": db_status,
            "unified_data_source_available": UNIFIED_DATA_SOURCE_AVAILABLE,
            "learning_mode": "hybrid" if UNIFIED_DATA_SOURCE_AVAILABLE else "local_only",
            "last_update": datetime.now().isoformat()
        }

    async def check_local_database_status(self) -> Dict[str, Any]:
        """检查本地数据库状态"""
        try:
            if not self.local_db_path.exists():
                return {
                    "status": "error",
                    "message": "本地股票数据库不存在"
                }

            conn = sqlite3.connect(self.local_db_path)

            # 检查数据量
            stock_count = pd.read_sql_query("SELECT COUNT(*) as count FROM stock_info", conn).iloc[0]['count']
            daily_count = pd.read_sql_query("SELECT COUNT(*) as count FROM daily_data", conn).iloc[0]['count']

            # 检查数据时间范围
            date_range = pd.read_sql_query("""
                SELECT MIN(trade_date) as start_date, MAX(trade_date) as end_date
                FROM daily_data WHERE trade_date != ''
            """, conn)

            conn.close()

            return {
                "status": "healthy",
                "stock_count": stock_count,
                "daily_records": daily_count,
                "date_range": {
                    "start": date_range.iloc[0]['start_date'] if not date_range.empty else None,
                    "end": date_range.iloc[0]['end_date'] if not date_range.empty else None
                },
                "database_size_mb": round(self.local_db_path.stat().st_size / (1024*1024), 2)
            }

        except Exception as e:
            logger.error(f"检查本地数据库状态失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    async def get_local_stock_data(self, stock_code: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """从本地数据库获取股票数据"""
        try:
            if not self.local_db_path.exists():
                return {
                    "success": False,
                    "error": "本地股票数据库不存在"
                }

            conn = sqlite3.connect(self.local_db_path)

            # 构建查询条件
            where_conditions = ["stock_code = ?"]
            params = [stock_code]

            if start_date:
                where_conditions.append("trade_date >= ?")
                params.append(start_date)

            if end_date:
                where_conditions.append("trade_date <= ?")
                params.append(end_date)

            where_clause = " AND ".join(where_conditions)

            # 查询日线数据
            query = f"""
                SELECT * FROM daily_data
                WHERE {where_clause}
                ORDER BY trade_date ASC
            """

            daily_data = pd.read_sql_query(query, conn, params=params)

            # 查询股票基本信息
            stock_info = pd.read_sql_query(
                "SELECT * FROM stock_info WHERE stock_code = ?",
                conn, params=[stock_code]
            )

            conn.close()

            return {
                "success": True,
                "stock_code": stock_code,
                "stock_info": stock_info.to_dict('records')[0] if not stock_info.empty else {},
                "daily_data": daily_data.to_dict('records'),
                "record_count": len(daily_data),
                "data_source": "local_database"
            }

        except Exception as e:
            logger.error(f"获取本地股票数据失败 {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_historical_data(self, stock_code: str, start_date: str, end_date: str, data_type: str = "daily") -> Optional[pd.DataFrame]:
        """获取历史数据 - 为真实学习流程引擎提供数据"""
        try:
            logger.info(f"🔍 获取历史数据: {stock_code}, {start_date} 至 {end_date}")

            # 首先尝试从本地数据库获取
            local_result = await self.get_local_stock_data(stock_code, start_date, end_date)

            if local_result.get("success") and local_result.get("daily_data"):
                daily_data = local_result["daily_data"]

                # 转换为DataFrame格式
                df = pd.DataFrame(daily_data)

                # 确保包含必要的列
                required_columns = ['open', 'high', 'low', 'close', 'volume']

                # 列名映射
                column_mapping = {
                    'open_price': 'open',
                    'high_price': 'high',
                    'low_price': 'low',
                    'close_price': 'close',
                    'trade_volume': 'volume',
                    'trade_amount': 'amount'
                }

                # 重命名列
                df = df.rename(columns=column_mapping)

                # 如果缺少amount列，用volume * close估算
                if 'amount' not in df.columns and 'volume' in df.columns and 'close' in df.columns:
                    df['amount'] = df['volume'] * df['close']

                # 确保数据类型正确
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                # 按日期排序
                if 'trade_date' in df.columns:
                    df = df.sort_values('trade_date')

                logger.info(f"✅ 从本地数据库获取历史数据成功: {len(df)} 条记录")
                return df

            # 如果本地数据不足，尝试使用统一数据源
            if UNIFIED_DATA_SOURCE_AVAILABLE:
                try:
                    async with unified_data_source_manager as data_source:
                        result = await data_source.get_stock_data(
                            stock_code,
                            "historical",
                            start_date=start_date,
                            end_date=end_date
                        )

                    if result.get("success") and result.get("data"):
                        data = result["data"]
                        df = pd.DataFrame(data)
                        logger.info(f"✅ 从统一数据源获取历史数据成功: {len(df)} 条记录")
                        return df

                except Exception as e:
                    logger.warning(f"统一数据源获取失败: {e}")

            # 如果都失败，返回None
            logger.warning(f"无法获取股票 {stock_code} 的历史数据")
            return None

        except Exception as e:
            logger.error(f"获取历史数据失败 {stock_code}: {e}")
            return None

    async def get_available_stocks(self) -> Dict[str, Any]:
        """获取可用的股票列表"""
        try:
            if not self.local_db_path.exists():
                return {
                    "success": False,
                    "error": "本地股票数据库不存在"
                }

            conn = sqlite3.connect(self.local_db_path)

            # 获取有日线数据的股票
            stocks_with_data = pd.read_sql_query("""
                SELECT si.stock_code, si.stock_name, si.industry,
                       COUNT(dd.trade_date) as data_count,
                       MIN(dd.trade_date) as start_date,
                       MAX(dd.trade_date) as end_date
                FROM stock_info si
                LEFT JOIN daily_data dd ON si.stock_code = dd.stock_code
                WHERE dd.trade_date IS NOT NULL
                GROUP BY si.stock_code, si.stock_name, si.industry
                HAVING COUNT(dd.trade_date) > 0
                ORDER BY data_count DESC
            """, conn)

            conn.close()

            return {
                "success": True,
                "total_stocks": len(stocks_with_data),
                "stocks": stocks_with_data.to_dict('records'),
                "data_source": "local_database"
            }

        except Exception as e:
            logger.error(f"获取可用股票列表失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_realtime_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """获取实时股票数据（瑶光星增强版 - 集成东方财富API）"""
        try:
            # 优先使用东方财富API获取真实数据
            try:
                from backend.services.data.eastmoney_api import EastmoneyAPI
                eastmoney_api = EastmoneyAPI()

                # 获取实时数据
                result = await eastmoney_api.get_realtime_data(stock_code)

                if result.get("success"):
                    data = result.get("data", {})
                    return {
                        "success": True,
                        "stock_code": stock_code,
                        "realtime_data": {
                            "name": data.get("name", ""),
                            "current_price": data.get("current", 0),
                            "open_price": data.get("open", 0),
                            "high_price": data.get("high", 0),
                            "low_price": data.get("low", 0),
                            "volume": data.get("volume", 0),
                            "amount": data.get("amount", 0),
                            "change_pct": data.get("change_pct", 0),
                            "timestamp": data.get("timestamp", datetime.now().isoformat())
                        },
                        "data_source": "东方财富API",
                        "source_priority": 1,
                        "collection_time": datetime.now().isoformat(),
                        "data_quality": "real"
                    }
                else:
                    logger.warning(f"东方财富API获取失败: {result.get('error')}")
            except Exception as e:
                logger.warning(f"东方财富API调用异常: {e}")

            # 回退到统一数据源
            if not UNIFIED_DATA_SOURCE_AVAILABLE:
                return {
                    "success": False,
                    "error": "统一数据源不可用，瑶光星无法获取实时数据",
                    "fallback": "请使用本地历史数据"
                }

            # 使用统一数据源管理器获取实时数据
            async with unified_data_source_manager as data_source:
                result = await data_source.get_stock_data(stock_code, "realtime")

            if result.get("success"):
                data = result.get("data", {})
                return {
                    "success": True,
                    "stock_code": stock_code,
                    "realtime_data": {
                        "name": data.get("name", ""),
                        "current_price": data.get("current", 0),
                        "open_price": data.get("open", 0),
                        "high_price": data.get("high", 0),
                        "low_price": data.get("low", 0),
                        "volume": data.get("volume", 0),
                        "amount": data.get("amount", 0),
                        "change_pct": data.get("change_pct", 0),
                        "timestamp": data.get("timestamp", datetime.now().isoformat())
                    },
                    "data_source": result.get("data_source", "统一数据源"),
                    "source_priority": result.get("source_priority", 0),
                    "collection_time": datetime.now().isoformat(),
                    "data_quality": "fallback"
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "实时数据获取失败"),
                    "attempted_sources": result.get("attempted_sources", [])
                }

        except Exception as e:
            logger.error(f"瑶光星获取实时股票数据失败 {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_multiple_realtime_data(self, stock_codes: List[str]) -> Dict[str, Any]:
        """批量获取多只股票的实时数据"""
        try:
            if not UNIFIED_DATA_SOURCE_AVAILABLE:
                return {
                    "success": False,
                    "error": "统一数据源不可用",
                    "stock_codes": stock_codes
                }

            results = {}
            successful_count = 0
            failed_count = 0

            for stock_code in stock_codes:
                try:
                    result = await self.get_realtime_stock_data(stock_code)
                    results[stock_code] = result

                    if result.get("success"):
                        successful_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    results[stock_code] = {
                        "success": False,
                        "error": str(e)
                    }
                    failed_count += 1

            return {
                "success": True,
                "total_requested": len(stock_codes),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "results": results,
                "collection_time": datetime.now().isoformat(),
                "data_source": "瑶光星-统一数据源"
            }

        except Exception as e:
            logger.error(f"瑶光星批量获取实时数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_codes": stock_codes
            }

    async def collect_historical_data_eastmoney(self, stock_codes: List[str] = None, years: int = 10,
                                              collect_all: bool = False) -> Dict[str, Any]:
        """使用东方财富API收集10年历史数据（瑶光星优化版）"""
        try:
            logger.info(f"🚀 瑶光星开始使用东方财富API收集{years}年历史数据...")

            # 如果没有指定股票代码，获取股票列表
            if stock_codes is None:
                if collect_all:
                    # 获取全部A股股票列表
                    stock_codes = await self._get_all_a_stocks()
                    logger.info(f"📊 获取到全部A股: {len(stock_codes)} 只股票")
                else:
                    # 获取数据库中未收集的股票
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT stock_code FROM stock_info
                        WHERE stock_code NOT IN (
                            SELECT DISTINCT stock_code FROM daily_data
                            WHERE data_source = 'eastmoney_annual'
                        )
                        ORDER BY stock_code
                        LIMIT 1000
                    """)
                    stock_codes = [row[0] for row in cursor.fetchall()]
                    conn.close()
                    logger.info(f"📊 获取到未收集股票: {len(stock_codes)} 只")

            if not stock_codes:
                return {
                    "success": False,
                    "error": "无可用股票代码",
                    "collected_count": 0
                }

            # 使用优化的收集方法
            collected_count = 0
            failed_count = 0
            total_records = 0

            # 分批处理，避免内存问题
            batch_size = 50
            total_batches = (len(stock_codes) + batch_size - 1) // batch_size

            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, len(stock_codes))
                batch_stocks = stock_codes[start_idx:end_idx]

                logger.info(f"📦 处理第 {batch_num + 1}/{total_batches} 批，股票 {start_idx + 1}-{end_idx}")

                # 处理当前批次
                for i, stock_code in enumerate(batch_stocks):
                    try:
                        # 收集单只股票数据
                        success, records, data_range, error_msg = await self._collect_single_stock_data(stock_code, years)

                        if success:
                            collected_count += 1
                            total_records += records
                        else:
                            failed_count += 1

                        # 每10只股票显示一次进度
                        if (i + 1) % 10 == 0 or i == len(batch_stocks) - 1:
                            current_total = start_idx + i + 1
                            progress = current_total / len(stock_codes) * 100
                            logger.info(f"📈 进度: {current_total}/{len(stock_codes)} ({progress:.1f}%)")
                            logger.info(f"📊 统计: 成功{collected_count}, 失败{failed_count}, 记录{total_records}")

                        # 控制请求频率
                        await asyncio.sleep(0.3)

                    except Exception as e:
                        logger.error(f"❌ 收集 {stock_code} 失败: {e}")
                        failed_count += 1
                        continue

                # 批次间休息
                if batch_num + 1 < total_batches:
                    logger.info("⏳ 批次间休息10秒...")
                    await asyncio.sleep(10)

            # 生成收集报告
            result = {
                "success": True,
                "collection_method": "eastmoney_api",
                "total_stocks": len(stock_codes),
                "collected_count": collected_count,
                "failed_count": failed_count,
                "total_records": total_records,
                "success_rate": collected_count / len(stock_codes) if stock_codes else 0,
                "collection_time": datetime.now().isoformat(),
                "data_source": "东方财富API",
                "data_quality": "real"
            }

            logger.info(f"✅ 瑶光星历史数据收集完成: {collected_count}/{len(stock_codes)} 只股票成功")
            return result

        except Exception as e:
            logger.error(f"❌ 瑶光星历史数据收集失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "collection_method": "eastmoney_api"
            }

    async def calculate_technical_indicators_batch(self, stock_codes: List[str] = None, limit: int = 50) -> Dict[str, Any]:
        """批量计算技术指标（瑶光星新增功能）"""
        try:
            logger.info(f"🔧 瑶光星开始批量计算技术指标...")

            # 导入技术指标计算器
            from backend.services.technical.real_technical_calculator import real_technical_calculator

            # 如果没有指定股票代码，获取有历史数据的股票
            if stock_codes is None:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT DISTINCT stock_code
                    FROM daily_data
                    WHERE data_source LIKE 'eastmoney%'
                    ORDER BY stock_code
                    LIMIT ?
                """, (limit,))
                stock_codes = [row[0] for row in cursor.fetchall()]
                conn.close()

            if not stock_codes:
                return {
                    "success": False,
                    "error": "无可用股票数据",
                    "calculated_count": 0
                }

            # 批量计算技术指标
            result = await asyncio.get_event_loop().run_in_executor(
                None, real_technical_calculator.batch_calculate_indicators, stock_codes, len(stock_codes)
            )

            # 添加瑶光星标识
            result["calculation_source"] = "瑶光星技术指标服务"
            result["data_quality"] = "real"

            logger.info(f"✅ 瑶光星技术指标计算完成: {result.get('success_count', 0)} 只股票成功")
            return result

        except Exception as e:
            logger.error(f"❌ 瑶光星技术指标计算失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "calculation_source": "瑶光星技术指标服务"
            }

    async def sync_daily_data(self) -> Dict[str, Any]:
        """同步每日数据"""
        try:
            logger.info("🔄 开始同步每日数据...")

            # 检查本地数据库状态
            db_status = await self.check_local_database_status()

            if db_status["status"] != "healthy":
                return {
                    "success": False,
                    "error": "本地数据库状态异常",
                    "db_status": db_status
                }

            # 模拟数据同步过程
            sync_result = {
                "success": True,
                "sync_method": "local_database_check",
                "records_checked": db_status.get("daily_records", 0),
                "stocks_checked": db_status.get("stock_count", 0),
                "sync_time": datetime.now().isoformat(),
                "data_quality": "good"
            }

            logger.info(f"✅ 每日数据同步完成: 检查{sync_result['records_checked']}条记录")
            return sync_result

        except Exception as e:
            logger.error(f"❌ 每日数据同步失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_hybrid_stock_data(self, stock_code: str, include_realtime: bool = True,
                                   start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取混合股票数据（本地历史数据 + 实时数据）"""
        try:
            result = {
                "success": True,
                "stock_code": stock_code,
                "data_sources": [],
                "collection_time": datetime.now().isoformat()
            }

            # 1. 获取本地历史数据
            local_data = await self.get_local_stock_data(stock_code, start_date, end_date)
            if local_data.get("success"):
                result["historical_data"] = local_data
                result["data_sources"].append("local_database")
            else:
                result["historical_data"] = {"error": local_data.get("error")}

            # 2. 获取实时数据（如果需要且可用）
            if include_realtime and UNIFIED_DATA_SOURCE_AVAILABLE:
                realtime_data = await self.get_realtime_stock_data(stock_code)
                if realtime_data.get("success"):
                    result["realtime_data"] = realtime_data
                    result["data_sources"].append("unified_data_source")
                else:
                    result["realtime_data"] = {"error": realtime_data.get("error")}
            elif include_realtime:
                result["realtime_data"] = {"error": "统一数据源不可用"}

            # 3. 数据质量评估
            result["data_quality"] = {
                "historical_available": local_data.get("success", False),
                "realtime_available": include_realtime and UNIFIED_DATA_SOURCE_AVAILABLE,
                "record_count": local_data.get("record_count", 0) if local_data.get("success") else 0,
                "completeness": "high" if local_data.get("success") and (not include_realtime or UNIFIED_DATA_SOURCE_AVAILABLE) else "partial"
            }

            return result

        except Exception as e:
            logger.error(f"瑶光星获取混合股票数据失败 {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code
            }

    async def _get_all_a_stocks(self) -> List[str]:
        """获取全部A股股票代码"""
        try:
            import requests
            import time

            session = requests.Session()
            session.proxies = {}
            session.verify = False

            all_stocks = []
            page = 1
            page_size = 100

            logger.info("🔍 获取全部A股股票列表...")

            while True:
                url = 'http://push2.eastmoney.com/api/qt/clist/get'
                params = {
                    'pn': page,
                    'pz': page_size,
                    'po': 1,
                    'np': 1,
                    'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                    'fltt': 2,
                    'invt': 2,
                    'fid': 'f3',
                    'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',  # A股
                    'fields': 'f12,f14'
                }

                response = session.get(url, params=params, timeout=15)

                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and 'diff' in data['data'] and data['data']['diff']:
                        stocks = data['data']['diff']
                        total_stocks_api = data['data'].get('total', 0)

                        for stock in stocks:
                            stock_code = stock.get('f12', '')
                            if stock_code:
                                all_stocks.append(stock_code)

                        logger.debug(f"📄 第{page}页: 获取{len(stocks)}只股票，累计{len(all_stocks)}只")

                        # 检查是否已获取所有股票
                        if len(stocks) < page_size:
                            logger.info(f"✅ 已获取所有股票: {len(all_stocks)}/{total_stocks_api}")
                            break

                        page += 1
                        time.sleep(0.5)  # 控制请求频率
                    else:
                        logger.warning(f"⚠️ 第{page}页无数据")
                        break
                else:
                    logger.error(f"❌ 第{page}页请求失败: {response.status_code}")
                    break

            return all_stocks

        except Exception as e:
            logger.error(f"获取A股列表失败: {e}")
            return []

    async def _collect_single_stock_data(self, stock_code: str, years: int = 10, max_retries: int = 3) -> tuple:
        """收集单只股票10年历史数据（优化版）"""
        try:
            import requests
            import time

            session = requests.Session()
            session.proxies = {}
            session.verify = False

            # 处理股票代码格式 (支持 000001.XSHE 和 000001 两种格式)
            clean_code = stock_code.split('.')[0] if '.' in stock_code else stock_code

            # 构造股票信息
            secid = f'1.{clean_code}' if clean_code.startswith('6') else f'0.{clean_code}'

            # 计算日期范围
            from datetime import datetime, timedelta
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years * 365)

            beg_date = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            data_range = f"{start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')}"

            for attempt in range(max_retries):
                try:
                    # 生成时间戳和回调函数名
                    import random
                    import time
                    timestamp = int(time.time() * 1000)
                    callback = f'jQuery{random.randint(100000000000000000000, 999999999999999999999)}_{timestamp}'

                    url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get'
                    params = {
                        'cb': callback,
                        'secid': secid,
                        'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                        'fields1': 'f1,f2,f3,f4,f5,f6',
                        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                        'klt': '101',  # 日K
                        'fqt': '1',    # 前复权
                        'beg': beg_date,    # 动态计算开始日期
                        'end': end_date_str, # 动态计算结束日期
                        'fltt': '1',
                        'invt': '2',
                        'dect': '1',
                        'wbp2u': '|0|0|0|web',
                        '_': timestamp
                    }

                    response = session.get(url, params=params, timeout=20)

                    if response.status_code == 200:
                        # 处理JSONP响应
                        text_content = response.text

                        # 如果返回的是HTML，说明被反爬虫了
                        if '<html' in text_content.lower() or '<!doctype' in text_content.lower():
                            logger.debug(f"⚠️ {stock_code} API被反爬虫拦截")
                            return False, 0, data_range, "API被反爬虫拦截"

                        # 处理JSONP响应（去掉callback包装）
                        if text_content.startswith(callback + '(') and text_content.rstrip().endswith(');'):
                            json_str = text_content[len(callback) + 1:-2].rstrip()
                        elif text_content.startswith(callback + '(') and text_content.rstrip().endswith(')'):
                            json_str = text_content[len(callback) + 1:-1].rstrip()
                        else:
                            json_str = text_content

                        # 解析JSON
                        import json
                        data = json.loads(json_str)

                        if 'data' in data and data['data'] and 'klines' in data['data']:
                            klines = data['data']['klines']

                            if klines and len(klines) > 0:
                                # 保存数据到数据库
                                saved_count = await self._save_stock_data(stock_code, klines)

                                if saved_count > 0:
                                    logger.debug(f"✅ {stock_code} 成功保存 {saved_count} 条数据")
                                    return True, saved_count, data_range, "success"
                                else:
                                    logger.warning(f"⚠️ {stock_code} 数据保存失败")
                                    return False, 0, data_range, "数据保存失败"
                            else:
                                logger.debug(f"⚠️ {stock_code} 无K线数据（可能是新股或停牌）")
                                return False, 0, data_range, "无K线数据"
                        else:
                            logger.debug(f"⚠️ {stock_code} API返回数据格式异常")
                            return False, 0, data_range, "API返回数据格式异常"

                    elif response.status_code in [502, 503, 429, 500]:
                        # API限制，需要重试
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 5
                            logger.debug(f"🔄 {stock_code} API限制({response.status_code})，等待{wait_time}秒重试...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            logger.debug(f"⚠️ {stock_code} API请求失败: {response.status_code}")
                            return False, 0, data_range, f"API请求失败: {response.status_code}"

                    else:
                        logger.debug(f"⚠️ {stock_code} API请求失败: {response.status_code}")
                        return False, 0, data_range, f"API请求失败: {response.status_code}"

                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        logger.debug(f"🔄 {stock_code} 超时，重试...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        logger.debug(f"⚠️ {stock_code} 请求超时")
                        return False, 0, data_range, "请求超时"

                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.debug(f"🔄 {stock_code} 异常，重试: {e}")
                        await asyncio.sleep(1)
                        continue
                    else:
                        logger.debug(f"⚠️ {stock_code} 请求异常: {e}")
                        return False, 0, data_range, f"请求异常: {e}"

            return False, 0, data_range, "所有重试都失败"

        except Exception as e:
            logger.error(f"收集 {stock_code} 数据失败: {e}")
            return False, 0, "", f"收集数据失败: {e}"

    async def _save_stock_data(self, stock_code: str, klines: List[str]) -> int:
        """保存股票数据到数据库"""
        try:
            # 使用正确的数据库路径
            db_path = "backend/data/stock_database.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            saved_count = 0

            for kline in klines:
                try:
                    # 解析K线数据
                    parts = kline.split(',')
                    if len(parts) >= 11:
                        trade_date = parts[0]
                        open_price = float(parts[1]) if parts[1] else 0
                        close_price = float(parts[2]) if parts[2] else 0
                        high_price = float(parts[3]) if parts[3] else 0
                        low_price = float(parts[4]) if parts[4] else 0
                        volume = int(parts[5]) if parts[5] else 0
                        amount = float(parts[6]) if parts[6] else 0
                        change_percent = float(parts[8]) if parts[8] else 0
                        turnover_rate = float(parts[10]) if parts[10] else 0

                        # 使用原始股票代码格式保存
                        cursor.execute("""
                            INSERT OR REPLACE INTO daily_data
                            (stock_code, trade_date, open_price, close_price, high_price, low_price,
                             volume, amount, change_percent, turnover_rate, data_source, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            stock_code, trade_date, open_price, close_price, high_price, low_price,
                            volume, amount, change_percent, turnover_rate, 'eastmoney_annual',
                            datetime.now().isoformat()
                        ))

                        saved_count += 1

                except Exception as e:
                    logger.debug(f"解析K线数据失败 {stock_code}: {e}")
                    continue

            conn.commit()
            conn.close()

            return saved_count

        except Exception as e:
            logger.error(f"保存股票数据失败 {stock_code}: {e}")
            return 0

    async def get_stock_basic_info(self, stock_code: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        try:
            if not self.local_db_path.exists():
                return {
                    "success": False,
                    "error": "本地股票数据库不存在",
                    "data": None
                }

            conn = sqlite3.connect(self.local_db_path)
            cursor = conn.cursor()

            # 获取股票基本信息
            cursor.execute("SELECT * FROM stock_info WHERE stock_code = ?", (stock_code,))
            stock_info = cursor.fetchone()

            if not stock_info:
                conn.close()
                return {
                    "success": False,
                    "error": f"股票 {stock_code} 不存在",
                    "data": None
                }

            conn.close()

            # 构建股票信息字典
            stock_data = {
                "stock_code": stock_info[0],
                "stock_name": stock_info[1] if len(stock_info) > 1 else "未知",
                "display_name": stock_info[2] if len(stock_info) > 2 else stock_info[1],
                "industry": stock_info[3] if len(stock_info) > 3 else "未分类",
                "market": stock_info[4] if len(stock_info) > 4 else "未知",
                "status": stock_info[7] if len(stock_info) > 7 else "active",
                "type": stock_info[8] if len(stock_info) > 8 else "stock"
            }

            return {
                "success": True,
                "data": stock_data
            }

        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": None
            }

# 全局数据管理服务实例
data_management_service = DataManagementService()
