#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星学习模式API接口
提供学习模式的启动、停止、状态查询等功能
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import asyncio
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/yaoguang-star/learning", tags=["瑶光星学习模式"])

class LearningSessionConfig(BaseModel):
    """学习会话配置"""
    stocks_per_session: int = 1
    data_years: int = 1
    strategy_testing_enabled: bool = True
    learning_mode: str = "comprehensive"
    session_duration_minutes: int = 10
    target_stocks: Optional[List[str]] = ["000001.XSHE"]
    enable_real_trading: bool = False
    enable_factor_generation: bool = True
    enable_multi_role_collaboration: bool = True

@router.post("/start")
async def start_learning_mode(config: LearningSessionConfig):
    """启动学习模式"""
    try:
        logger.info("🚀 启动瑶光星学习模式")
        
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 初始化系统
        init_result = await unified_yaoguang_system.initialize_system()
        if not init_result.get("success"):
            raise HTTPException(status_code=500, detail=f"系统初始化失败: {init_result.get('error')}")
        
        # 启动学习会话
        session_config = config.dict()
        session_result = await unified_yaoguang_system.start_learning_session(session_config)
        
        if not session_result.get("success"):
            raise HTTPException(status_code=500, detail=f"学习会话启动失败: {session_result.get('error')}")
        
        return {
            "success": True,
            "message": "学习模式启动成功",
            "data": {
                "session_id": session_result["session_id"],
                "session_active": True,
                "start_time": datetime.now().isoformat(),
                "config": session_config
            }
        }
        
    except Exception as e:
        logger.error(f"启动学习模式失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动学习模式失败: {str(e)}")

@router.post("/stop")
async def stop_learning_mode():
    """停止学习模式"""
    try:
        logger.info("🛑 停止瑶光星学习模式")
        
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 停止当前会话
        stop_result = await unified_yaoguang_system.stop_current_session()
        
        if not stop_result.get("success"):
            raise HTTPException(status_code=500, detail=f"停止学习会话失败: {stop_result.get('error')}")
        
        return {
            "success": True,
            "message": "学习模式已停止",
            "data": {
                "session_id": stop_result.get("session_id"),
                "session_active": False,
                "stop_time": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"停止学习模式失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止学习模式失败: {str(e)}")

@router.get("/status")
async def get_learning_status():
    """获取学习状态"""
    try:
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 获取学习状态
        status = await unified_yaoguang_system.get_learning_status()
        
        return {
            "success": True,
            "message": "获取学习状态成功",
            "data": status
        }
        
    except Exception as e:
        logger.error(f"获取学习状态失败: {e}")
        return {
            "success": False,
            "error": f"获取学习状态失败: {str(e)}",
            "data": {
                "session_active": False,
                "session_id": None,
                "processed_stocks": 0,
                "learning_progress": 0,
                "current_step": None,
                "start_time": None
            }
        }

@router.get("/summary/{session_id}")
async def get_learning_session_summary(session_id: str):
    """获取学习会话总结"""
    try:
        logger.info(f"📋 获取学习会话总结: {session_id}")
        
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 获取会话总结
        summary = await unified_yaoguang_system.get_session_summary(session_id)
        
        return {
            "success": True,
            "message": "获取学习总结成功",
            "data": summary
        }
        
    except Exception as e:
        logger.error(f"获取学习总结失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习总结失败: {str(e)}")

@router.get("/sessions")
async def get_learning_sessions(limit: int = 10, offset: int = 0):
    """获取学习会话列表"""
    try:
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 获取会话历史
        sessions = []
        if hasattr(unified_yaoguang_system, 'session_history'):
            sessions = unified_yaoguang_system.session_history[offset:offset+limit]
        
        return {
            "success": True,
            "message": "获取学习会话列表成功",
            "data": {
                "sessions": sessions,
                "total": len(unified_yaoguang_system.session_history) if hasattr(unified_yaoguang_system, 'session_history') else 0,
                "limit": limit,
                "offset": offset
            }
        }
        
    except Exception as e:
        logger.error(f"获取学习会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习会话列表失败: {str(e)}")

@router.post("/test")
async def test_learning_system():
    """测试学习系统"""
    try:
        logger.info("🧪 测试瑶光星学习系统")
        
        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 初始化系统
        init_result = await unified_yaoguang_system.initialize_system()
        
        # 获取系统状态
        system_status = await unified_yaoguang_system.get_system_status()
        
        return {
            "success": True,
            "message": "学习系统测试完成",
            "data": {
                "initialization": init_result,
                "system_status": system_status,
                "test_time": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"测试学习系统失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试学习系统失败: {str(e)}")

@router.get("/factors")
async def get_generated_factors(session_id: Optional[str] = None):
    """获取生成的因子"""
    try:
        # 导入RD-Agent集成服务
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        # 获取因子状态
        status = rd_agent_integration_service.get_service_status()
        
        return {
            "success": True,
            "message": "获取因子信息成功",
            "data": {
                "allocated_factors": status.get("allocated_factors", 0),
                "use_professional": status.get("use_professional", False),
                "local_rd_agent_available": status.get("local_rd_agent_available", False),
                "session_id": session_id
            }
        }
        
    except Exception as e:
        logger.error(f"获取因子信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子信息失败: {str(e)}")

@router.get("/report/{session_id}")
async def get_learning_report(session_id: str):
    """获取学习报告"""
    try:
        logger.info(f"📋 获取学习报告: {session_id}")

        # 导入瑶光星统一系统
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system

        # 从历史记录中查找会话
        session_data = None
        for session in unified_yaoguang_system.session_history:
            if session.get("session_id") == session_id:
                session_data = session
                break

        if not session_data:
            raise HTTPException(status_code=404, detail=f"未找到会话: {session_id}")

        # 检查是否已有学习报告
        if "learning_report" in session_data:
            return {
                "success": True,
                "message": "获取学习报告成功",
                "data": session_data["learning_report"]
            }

        # 如果没有报告，生成一个
        try:
            from roles.yaoguang_star.services.learning_report_generator import learning_report_generator
            report_result = await learning_report_generator.generate_comprehensive_report(session_data)

            if report_result.get("success"):
                return {
                    "success": True,
                    "message": "学习报告生成成功",
                    "data": report_result["report"]
                }
            else:
                raise HTTPException(status_code=500, detail=f"生成学习报告失败: {report_result.get('error')}")

        except ImportError:
            raise HTTPException(status_code=500, detail="学习报告生成器不可用")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学习报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习报告失败: {str(e)}")

@router.post("/scheduler/start")
async def start_intelligent_scheduler():
    """启动智能自动化调度器"""
    try:
        logger.info("🚀 启动智能自动化调度器")

        # 导入智能调度系统
        from roles.yaoguang_star.core.intelligent_automation_scheduler import intelligent_automation_scheduler

        # 启动调度器
        result = await intelligent_automation_scheduler.start_scheduler()

        if result.get("success"):
            return {
                "success": True,
                "message": "智能调度器启动成功",
                "data": result
            }
        else:
            raise HTTPException(status_code=500, detail=f"启动调度器失败: {result.get('error')}")

    except Exception as e:
        logger.error(f"启动智能调度器失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动智能调度器失败: {str(e)}")

@router.post("/scheduler/stop")
async def stop_intelligent_scheduler():
    """停止智能自动化调度器"""
    try:
        logger.info("🛑 停止智能自动化调度器")

        # 导入智能调度系统
        from roles.yaoguang_star.core.intelligent_automation_scheduler import intelligent_automation_scheduler

        # 停止调度器
        result = await intelligent_automation_scheduler.stop_scheduler()

        if result.get("success"):
            return {
                "success": True,
                "message": "智能调度器停止成功",
                "data": result
            }
        else:
            raise HTTPException(status_code=500, detail=f"停止调度器失败: {result.get('error')}")

    except Exception as e:
        logger.error(f"停止智能调度器失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止智能调度器失败: {str(e)}")

@router.get("/scheduler/status")
async def get_scheduler_status():
    """获取智能调度器状态"""
    try:
        # 导入智能调度系统
        from roles.yaoguang_star.core.intelligent_automation_scheduler import intelligent_automation_scheduler

        # 获取状态
        status = await intelligent_automation_scheduler.get_scheduler_status()

        return {
            "success": True,
            "message": "获取调度器状态成功",
            "data": status
        }

    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取调度器状态失败: {str(e)}")

@router.get("/daily-report/{report_date}")
async def get_daily_trading_report(report_date: str):
    """获取每日实盘交易报告"""
    try:
        logger.info(f"📋 获取每日实盘交易报告: {report_date}")

        # 导入每日报告生成器
        from roles.yaoguang_star.services.daily_trading_report_generator import daily_trading_report_generator

        # 生成报告
        report_result = await daily_trading_report_generator.generate_daily_report(report_date)

        if report_result.get("success"):
            return {
                "success": True,
                "message": "获取每日报告成功",
                "data": report_result["report"]
            }
        else:
            raise HTTPException(status_code=500, detail=f"生成每日报告失败: {report_result.get('error')}")

    except Exception as e:
        logger.error(f"获取每日报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取每日报告失败: {str(e)}")

# 导出路由器
__all__ = ["router"]
