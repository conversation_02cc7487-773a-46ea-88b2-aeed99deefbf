<template>
  <div class="seven-stars-dialog">
    <!-- 对话触发按钮 -->
    <el-button 
      type="primary" 
      size="large" 
      class="dialog-trigger"
      @click="openDialog"
      :loading="loading"
    >
      <el-icon><ChatDotRound /></el-icon>
      七星对话
    </el-button>

    <!-- 主对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="七星量化交易系统 - 智能对话"
      width="85%"
      :before-close="handleClose"
      class="seven-stars-dialog-modal"
    >
      <div class="dialog-content">
        <!-- 对话模式选择 -->
        <div class="dialog-header">
          <div class="mode-selector">
            <el-radio-group v-model="dialogMode" @change="onModeChange">
              <el-radio-button label="single">单独对话</el-radio-button>
              <el-radio-button label="multi">群聊模式</el-radio-button>
              <el-radio-button label="debate">辩论模式</el-radio-button>
            </el-radio-group>
          </div>
          
          <div class="session-info">
            <el-tag type="info">会话ID: {{ currentSessionId }}</el-tag>
            <el-button size="small" @click="createNewSession">
              <el-icon><Plus /></el-icon>
              新会话
            </el-button>
          </div>
        </div>

        <!-- 角色选择区域 -->
        <div class="role-selector">
          <div class="selector-title">
            <span v-if="dialogMode === 'single'">选择对话角色：</span>
            <span v-else>选择参与角色：</span>
          </div>
          
          <div class="roles-grid">
            <div
              v-for="role in availableRoles"
              :key="role.id"
              class="role-card"
              :class="{ 
                selected: isRoleSelected(role.id),
                disabled: dialogMode === 'single' && selectedRoles.length > 0 && !isRoleSelected(role.id)
              }"
              @click="toggleRole(role.id)"
            >
              <div class="role-avatar">
                <span class="role-icon">{{ role.icon }}</span>
                <div v-if="isRoleSelected(role.id)" class="selected-indicator">
                  <el-icon><Check /></el-icon>
                </div>
              </div>
              <div class="role-info">
                <div class="role-name">{{ role.name }}</div>
                <div class="role-title">{{ role.title }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 消息区域 -->
        <div class="messages-area">
          <div 
            ref="messagesContainer"
            class="messages-container"
          >
            <!-- 欢迎消息 -->
            <div v-if="messages.length === 0" class="welcome-message">
              <div class="welcome-content">
                <el-icon size="48"><ChatDotRound /></el-icon>
                <h3>欢迎使用七星智能对话系统</h3>
                <p>选择角色开始对话，体验AI智能体的专业能力</p>
              </div>
            </div>
            
            <!-- 消息列表 -->
            <div v-else class="messages-list">
              <div
                v-for="message in messages"
                :key="message.id"
                class="message-item"
                :class="message.role"
              >
                <div class="message-avatar">
                  <el-icon v-if="message.role === 'user'" size="24"><User /></el-icon>
                  <span v-else class="role-icon">{{ getRoleIcon(message.metadata?.source_role) }}</span>
                </div>
                
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">
                      {{ message.role === 'user' ? '用户' : message.metadata?.role_name || '系统' }}
                    </span>
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                  </div>
                  <div class="message-text" v-html="formatMessageContent(message.content)"></div>
                </div>
              </div>
              
              <!-- 打字指示器 -->
              <div v-if="isProcessing" class="typing-indicator">
                <div class="typing-avatar">
                  <span class="role-icon">🤖</span>
                </div>
                <div class="typing-content">
                  <div class="typing-text">{{ processingText }}</div>
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入您的消息..."
              :disabled="isProcessing || selectedRoles.length === 0"
              @keydown.ctrl.enter="sendMessage"
              class="message-input"
            />
            
            <div class="input-actions">
              <div class="input-info">
                <span v-if="selectedRoles.length === 0" class="warning-text">
                  请先选择对话角色
                </span>
                <span v-else class="selected-info">
                  已选择: {{ getSelectedRoleNames().join(', ') }}
                </span>
              </div>
              
              <el-button
                type="primary"
                @click="sendMessage"
                :disabled="!inputMessage.trim() || isProcessing || selectedRoles.length === 0"
                :loading="isProcessing"
              >
                <el-icon><Promotion /></el-icon>
                发送 (Ctrl+Enter)
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  ChatDotRound, 
  Plus, 
  Check, 
  User, 
  Promotion 
} from '@element-plus/icons-vue'
import { sevenStarsDialogApi } from '@/api/seven-stars-dialog'

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const dialogMode = ref('single') // single, multi, debate
const selectedRoles = ref<string[]>([])
const messages = ref<any[]>([])
const inputMessage = ref('')
const isProcessing = ref(false)
const processingText = ref('')
const currentSessionId = ref('')
const messagesContainer = ref<HTMLElement>()

// 可用角色列表
const availableRoles = ref([
  { id: 'tianquan', name: '天权星', title: '指挥官', icon: '👑', specialties: ['战略决策', '投资指挥'] },
  { id: 'tianji', name: '天玑星', title: '风险管理专家', icon: '🛡️', specialties: ['风险识别', '风险评估'] },
  { id: 'tianxuan', name: '天璇星', title: '技术分析师', icon: '📈', specialties: ['技术分析', '量化研究'] },
  { id: 'tianshu', name: '天枢星', title: '智能情报官', icon: '🔍', specialties: ['新闻收集', '情感分析'] },
  { id: 'yuheng', name: '玉衡星', title: '交易执行官', icon: '⚡', specialties: ['交易执行', '订单管理'] },
  { id: 'kaiyang', name: '开阳星', title: '股票检测员', icon: '🎯', specialties: ['股票筛选', '机会发现'] },
  { id: 'yaoguang', name: '瑶光星', title: '学习训练专家', icon: '🧠', specialties: ['数据管理', '模型训练'] }
])

// 计算属性
const getSelectedRoleNames = () => {
  return selectedRoles.value.map(roleId => {
    const role = availableRoles.value.find(r => r.id === roleId)
    return role ? role.name : roleId
  })
}

// 方法
const openDialog = async () => {
  dialogVisible.value = true
  loading.value = true
  
  try {
    // 加载可用角色
    const response = await sevenStarsDialogApi.getAvailableRoles()
    if (response.data?.roles) {
      availableRoles.value = response.data.roles
    }
    
    // 创建新会话
    createNewSession()
    
  } catch (error) {
    ElMessage.error('打开对话失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  // 清理状态
  selectedRoles.value = []
  messages.value = []
  inputMessage.value = ''
  isProcessing.value = false
}

const onModeChange = (mode: string) => {
  if (mode === 'single') {
    // 单独对话模式只能选择一个角色
    if (selectedRoles.value.length > 1) {
      selectedRoles.value = selectedRoles.value.slice(0, 1)
    }
  }
}

const isRoleSelected = (roleId: string) => {
  return selectedRoles.value.includes(roleId)
}

const toggleRole = (roleId: string) => {
  if (dialogMode.value === 'single') {
    // 单独对话模式
    if (isRoleSelected(roleId)) {
      selectedRoles.value = []
    } else {
      selectedRoles.value = [roleId]
    }
  } else {
    // 多角色模式
    if (isRoleSelected(roleId)) {
      selectedRoles.value = selectedRoles.value.filter(id => id !== roleId)
    } else {
      selectedRoles.value.push(roleId)
    }
  }
}

const createNewSession = () => {
  currentSessionId.value = `session_${Date.now()}`
  messages.value = []
}

const sendMessage = async () => {
  if (!inputMessage.value.trim() || isProcessing.value || selectedRoles.value.length === 0) {
    return
  }

  const message = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息
  const userMessage = {
    id: `msg_${Date.now()}`,
    role: 'user',
    content: message,
    timestamp: new Date().toISOString()
  }
  
  messages.value.push(userMessage)
  await scrollToBottom()

  // 发送到后端
  isProcessing.value = true
  
  try {
    if (dialogMode.value === 'single') {
      // 单角色对话
      processingText.value = `${getSelectedRoleNames()[0]}正在思考...`
      
      const response = await sevenStarsDialogApi.singleRoleDialog({
        target_role: selectedRoles.value[0],
        message: message,
        session_id: currentSessionId.value
      })
      
      if (response.data?.response_message) {
        messages.value.push(response.data.response_message)
      }
    } else {
      // 多角色对话
      processingText.value = `${selectedRoles.value.length}个角色正在协作回复...`
      
      const response = await sevenStarsDialogApi.multiRoleDialog({
        target_roles: selectedRoles.value,
        message: message,
        session_id: currentSessionId.value,
        mode: dialogMode.value === 'debate' ? 'parallel' : 'sequential'
      })
      
      if (response.data?.responses) {
        for (const responseMsg of response.data.responses) {
          messages.value.push(responseMsg)
          await scrollToBottom()
          // 添加延迟让用户看到逐个回复的效果
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }
    }
    
    await scrollToBottom()
    
  } catch (error) {
    ElMessage.error('发送消息失败')
    console.error('Dialog error:', error)
  } finally {
    isProcessing.value = false
  }
}

const getRoleIcon = (roleId: string) => {
  const role = availableRoles.value.find(r => r.id === roleId)
  return role ? role.icon : '🤖'
}

const formatTime = (timestamp: string) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const formatMessageContent = (content: string) => {
  // 简单的markdown格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 生命周期
onMounted(() => {
  // 初始化
})

// 暴露方法给父组件
defineExpose({
  openDialog
})
</script>

<style scoped>
.seven-stars-dialog {
  display: inline-block;
}

.dialog-trigger {
  font-size: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.dialog-trigger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.seven-stars-dialog-modal {
  --el-dialog-border-radius: 12px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  height: 70vh;
  gap: 16px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
}

.mode-selector .el-radio-group {
  background: white;
  border-radius: 6px;
  padding: 4px;
}

.session-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-selector {
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
}

.selector-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.role-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.role-card:hover {
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.role-card.selected {
  border-color: #409eff;
  background: #ecf5ff;
}

.role-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.role-avatar {
  position: relative;
  margin-bottom: 8px;
}

.role-icon {
  font-size: 24px;
  display: block;
}

.selected-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.role-info {
  text-align: center;
}

.role-name {
  font-weight: 600;
  font-size: 12px;
  color: #303133;
}

.role-title {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.messages-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
}

.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.welcome-content {
  text-align: center;
  color: #909399;
}

.welcome-content h3 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.message-item.user .message-avatar {
  background: #67c23a;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-item.user .message-content {
  text-align: right;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #909399;
}

.message-item.user .message-header {
  flex-direction: row-reverse;
}

.sender-name {
  font-weight: 600;
}

.message-text {
  background: white;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  line-height: 1.5;
}

.message-item.user .message-text {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.typing-indicator {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.typing-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.typing-content {
  background: white;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.typing-text {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #409eff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-area {
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-input {
  resize: none;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-info {
  font-size: 12px;
}

.warning-text {
  color: #f56c6c;
}

.selected-info {
  color: #409eff;
}
</style>
