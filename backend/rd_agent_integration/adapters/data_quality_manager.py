#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RD-Agent数据质量管理器
确保数据质量符合RD-Agent的要求
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class DataQualityLevel(str, Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"  # 优秀 (>95%)
    GOOD = "good"           # 良好 (85-95%)
    FAIR = "fair"           # 一般 (70-85%)
    POOR = "poor"           # 较差 (<70%)

class DataIssueType(str, Enum):
    """数据问题类型"""
    MISSING_VALUES = "missing_values"
    OUTLIERS = "outliers"
    DUPLICATES = "duplicates"
    INCONSISTENT_FORMAT = "inconsistent_format"
    INVALID_RANGE = "invalid_range"
    TEMPORAL_GAPS = "temporal_gaps"
    FEATURE_CORRELATION = "feature_correlation"

@dataclass
class DataQualityIssue:
    """数据质量问题"""
    issue_type: DataIssueType
    severity: str  # "low", "medium", "high", "critical"
    description: str
    affected_columns: List[str]
    affected_rows: List[int]
    suggested_action: str
    auto_fixable: bool = False

@dataclass
class DataQualityReport:
    """数据质量报告"""
    dataset_id: str
    quality_level: DataQualityLevel
    overall_score: float
    total_rows: int
    total_columns: int
    issues: List[DataQualityIssue]
    statistics: Dict[str, Any]
    recommendations: List[str]
    generated_at: datetime = None
    
    def __post_init__(self):
        if self.generated_at is None:
            self.generated_at = datetime.now()

class RDAgentDataQualityManager:
    """RD-Agent数据质量管理器"""
    
    def __init__(self):
        self.service_name = "RDAgentDataQualityManager"
        self.version = "0.3.0"
        
        # 质量标准配置
        self.quality_standards = {
            "missing_value_threshold": 0.05,  # 缺失值阈值5%
            "outlier_std_threshold": 3.0,     # 异常值标准差阈值
            "duplicate_threshold": 0.01,      # 重复值阈值1%
            "correlation_threshold": 0.95,    # 特征相关性阈值
            "temporal_gap_threshold": 2       # 时间间隔阈值（天）
        }
        
        # 数据类型映射
        self.expected_dtypes = {
            "price": float,
            "volume": float,
            "ratio": float,
            "return": float,
            "date": "datetime64[ns]",
            "symbol": str
        }
        
        # 合理范围定义
        self.value_ranges = {
            "price": (0, float('inf')),
            "volume": (0, float('inf')),
            "return": (-1.0, 1.0),  # 日收益率通常在-100%到100%之间
            "ratio": (0, 100),      # 比率通常在0到100之间
            "volatility": (0, 1.0)  # 波动率通常在0到100%之间
        }
        
        # 统计信息
        self.stats = {
            "total_assessments": 0,
            "datasets_processed": 0,
            "issues_detected": 0,
            "auto_fixes_applied": 0,
            "avg_quality_score": 0.0
        }
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    async def assess_data_quality(self, 
                                data: pd.DataFrame,
                                dataset_id: str = None,
                                feature_types: Dict[str, str] = None) -> DataQualityReport:
        """评估数据质量"""
        
        if dataset_id is None:
            dataset_id = f"dataset_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            logger.info(f"开始数据质量评估: {dataset_id}")
            
            # 基础统计
            total_rows, total_columns = data.shape
            
            # 检测各类问题
            issues = []
            
            # 1. 检测缺失值
            missing_issues = await self._detect_missing_values(data)
            issues.extend(missing_issues)
            
            # 2. 检测异常值
            outlier_issues = await self._detect_outliers(data, feature_types)
            issues.extend(outlier_issues)
            
            # 3. 检测重复值
            duplicate_issues = await self._detect_duplicates(data)
            issues.extend(duplicate_issues)
            
            # 4. 检测数据格式问题
            format_issues = await self._detect_format_issues(data, feature_types)
            issues.extend(format_issues)
            
            # 5. 检测数值范围问题
            range_issues = await self._detect_range_issues(data, feature_types)
            issues.extend(range_issues)
            
            # 6. 检测时间序列问题
            temporal_issues = await self._detect_temporal_issues(data)
            issues.extend(temporal_issues)
            
            # 7. 检测特征相关性问题
            correlation_issues = await self._detect_correlation_issues(data)
            issues.extend(correlation_issues)
            
            # 计算质量评分
            quality_score = await self._calculate_quality_score(data, issues)
            quality_level = self._determine_quality_level(quality_score)
            
            # 生成统计信息
            statistics = await self._generate_statistics(data, issues)
            
            # 生成建议
            recommendations = await self._generate_recommendations(issues)
            
            # 创建质量报告
            report = DataQualityReport(
                dataset_id=dataset_id,
                quality_level=quality_level,
                overall_score=quality_score,
                total_rows=total_rows,
                total_columns=total_columns,
                issues=issues,
                statistics=statistics,
                recommendations=recommendations
            )
            
            # 更新统计
            self.stats["total_assessments"] += 1
            self.stats["datasets_processed"] += 1
            self.stats["issues_detected"] += len(issues)
            self.stats["avg_quality_score"] = (
                self.stats["avg_quality_score"] * (self.stats["total_assessments"] - 1) + quality_score
            ) / self.stats["total_assessments"]
            
            logger.info(f"  数据质量评估完成: {dataset_id}, 评分: {quality_score:.2f}, 问题: {len(issues)}个")
            return report
            
        except Exception as e:
            logger.error(f"  数据质量评估失败: {e}")
            raise
    
    async def _detect_missing_values(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检测缺失值"""
        
        issues = []
        
        for column in data.columns:
            missing_count = data[column].isnull().sum()
            missing_ratio = missing_count / len(data)
            
            if missing_ratio > self.quality_standards["missing_value_threshold"]:
                severity = "critical" if missing_ratio > 0.2 else "high" if missing_ratio > 0.1 else "medium"
                
                issue = DataQualityIssue(
                    issue_type=DataIssueType.MISSING_VALUES,
                    severity=severity,
                    description=f"列 '{column}' 有 {missing_count} 个缺失值 ({missing_ratio:.2%})",
                    affected_columns=[column],
                    affected_rows=data[data[column].isnull()].index.tolist(),
                    suggested_action="使用插值、前向填充或删除缺失行",
                    auto_fixable=True
                )
                issues.append(issue)
        
        return issues
    
    async def _detect_outliers(self, data: pd.DataFrame, feature_types: Dict[str, str] = None) -> List[DataQualityIssue]:
        """检测异常值"""
        
        issues = []
        
        # 只检测数值列
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            if data[column].isnull().all():
                continue
            
            # 使用Z-score方法检测异常值
            z_scores = np.abs((data[column] - data[column].mean()) / data[column].std())
            outlier_mask = z_scores > self.quality_standards["outlier_std_threshold"]
            outlier_count = outlier_mask.sum()
            
            if outlier_count > 0:
                outlier_ratio = outlier_count / len(data)
                severity = "high" if outlier_ratio > 0.05 else "medium" if outlier_ratio > 0.02 else "low"
                
                issue = DataQualityIssue(
                    issue_type=DataIssueType.OUTLIERS,
                    severity=severity,
                    description=f"列 '{column}' 有 {outlier_count} 个异常值 ({outlier_ratio:.2%})",
                    affected_columns=[column],
                    affected_rows=data[outlier_mask].index.tolist(),
                    suggested_action="检查数据来源，考虑使用Winsorization或删除异常值",
                    auto_fixable=True
                )
                issues.append(issue)
        
        return issues
    
    async def _detect_duplicates(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检测重复值"""
        
        issues = []
        
        # 检测完全重复的行
        duplicate_mask = data.duplicated()
        duplicate_count = duplicate_mask.sum()
        
        if duplicate_count > 0:
            duplicate_ratio = duplicate_count / len(data)
            
            if duplicate_ratio > self.quality_standards["duplicate_threshold"]:
                severity = "high" if duplicate_ratio > 0.1 else "medium"
                
                issue = DataQualityIssue(
                    issue_type=DataIssueType.DUPLICATES,
                    severity=severity,
                    description=f"发现 {duplicate_count} 行重复数据 ({duplicate_ratio:.2%})",
                    affected_columns=data.columns.tolist(),
                    affected_rows=data[duplicate_mask].index.tolist(),
                    suggested_action="删除重复行或检查数据收集过程",
                    auto_fixable=True
                )
                issues.append(issue)
        
        return issues
    
    async def _detect_format_issues(self, data: pd.DataFrame, feature_types: Dict[str, str] = None) -> List[DataQualityIssue]:
        """检测格式问题"""
        
        issues = []
        
        if feature_types is None:
            return issues
        
        for column, expected_type in feature_types.items():
            if column not in data.columns:
                continue
            
            current_dtype = str(data[column].dtype)
            expected_dtype = self.expected_dtypes.get(expected_type, expected_type)
            
            # 检查数据类型是否匹配
            if expected_dtype != current_dtype and not self._is_compatible_dtype(current_dtype, expected_dtype):
                issue = DataQualityIssue(
                    issue_type=DataIssueType.INCONSISTENT_FORMAT,
                    severity="medium",
                    description=f"列 '{column}' 数据类型不匹配: 期望 {expected_dtype}, 实际 {current_dtype}",
                    affected_columns=[column],
                    affected_rows=[],
                    suggested_action=f"将列 '{column}' 转换为 {expected_dtype} 类型",
                    auto_fixable=True
                )
                issues.append(issue)
        
        return issues
    
    async def _detect_range_issues(self, data: pd.DataFrame, feature_types: Dict[str, str] = None) -> List[DataQualityIssue]:
        """检测数值范围问题"""
        
        issues = []
        
        if feature_types is None:
            return issues
        
        for column, feature_type in feature_types.items():
            if column not in data.columns or feature_type not in self.value_ranges:
                continue
            
            min_val, max_val = self.value_ranges[feature_type]
            
            # 检查超出范围的值
            out_of_range_mask = (data[column] < min_val) | (data[column] > max_val)
            out_of_range_count = out_of_range_mask.sum()
            
            if out_of_range_count > 0:
                severity = "high" if out_of_range_count / len(data) > 0.05 else "medium"
                
                issue = DataQualityIssue(
                    issue_type=DataIssueType.INVALID_RANGE,
                    severity=severity,
                    description=f"列 '{column}' 有 {out_of_range_count} 个值超出合理范围 [{min_val}, {max_val}]",
                    affected_columns=[column],
                    affected_rows=data[out_of_range_mask].index.tolist(),
                    suggested_action="检查数据来源，考虑数据清洗或范围调整",
                    auto_fixable=False
                )
                issues.append(issue)
        
        return issues
    
    async def _detect_temporal_issues(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检测时间序列问题"""
        
        issues = []
        
        # 寻找日期列
        date_columns = []
        for column in data.columns:
            if 'date' in column.lower() or 'time' in column.lower():
                date_columns.append(column)
        
        for date_column in date_columns:
            try:
                # 确保是日期类型
                if not pd.api.types.is_datetime64_any_dtype(data[date_column]):
                    data[date_column] = pd.to_datetime(data[date_column])
                
                # 检查时间间隔
                date_series = data[date_column].dropna().sort_values()
                if len(date_series) > 1:
                    gaps = date_series.diff().dt.days
                    large_gaps = gaps > self.quality_standards["temporal_gap_threshold"]
                    
                    if large_gaps.any():
                        gap_count = large_gaps.sum()
                        max_gap = gaps.max()
                        
                        issue = DataQualityIssue(
                            issue_type=DataIssueType.TEMPORAL_GAPS,
                            severity="medium",
                            description=f"时间序列 '{date_column}' 存在 {gap_count} 个大间隔，最大间隔 {max_gap} 天",
                            affected_columns=[date_column],
                            affected_rows=[],
                            suggested_action="检查数据收集过程，考虑填补时间间隔",
                            auto_fixable=False
                        )
                        issues.append(issue)
                        
            except Exception as e:
                logger.warning(f"时间序列检测失败: {date_column} - {e}")
        
        return issues
    
    async def _detect_correlation_issues(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检测特征相关性问题"""
        
        issues = []
        
        # 只检查数值列
        numeric_data = data.select_dtypes(include=[np.number])
        
        if numeric_data.shape[1] < 2:
            return issues
        
        try:
            # 计算相关性矩阵
            correlation_matrix = numeric_data.corr().abs()
            
            # 找出高相关性的特征对
            high_corr_pairs = []
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_value = correlation_matrix.iloc[i, j]
                    if corr_value > self.quality_standards["correlation_threshold"]:
                        col1 = correlation_matrix.columns[i]
                        col2 = correlation_matrix.columns[j]
                        high_corr_pairs.append((col1, col2, corr_value))
            
            if high_corr_pairs:
                for col1, col2, corr_value in high_corr_pairs:
                    issue = DataQualityIssue(
                        issue_type=DataIssueType.FEATURE_CORRELATION,
                        severity="medium",
                        description=f"特征 '{col1}' 和 '{col2}' 高度相关 (r={corr_value:.3f})",
                        affected_columns=[col1, col2],
                        affected_rows=[],
                        suggested_action="考虑删除其中一个特征或使用主成分分析",
                        auto_fixable=False
                    )
                    issues.append(issue)
                    
        except Exception as e:
            logger.warning(f"相关性检测失败: {e}")

        return issues

    def _is_compatible_dtype(self, current_dtype: str, expected_dtype: str) -> bool:
        """检查数据类型是否兼容"""

        # 数值类型兼容性
        numeric_types = ['int64', 'float64', 'int32', 'float32']
        if expected_dtype == float and current_dtype in numeric_types:
            return True

        # 字符串类型兼容性
        string_types = ['object', 'string']
        if expected_dtype == str and current_dtype in string_types:
            return True

        return False

    async def _calculate_quality_score(self, data: pd.DataFrame, issues: List[DataQualityIssue]) -> float:
        """计算数据质量评分"""

        base_score = 100.0

        # 根据问题严重程度扣分
        severity_weights = {
            "low": 1,
            "medium": 3,
            "high": 7,
            "critical": 15
        }

        for issue in issues:
            weight = severity_weights.get(issue.severity, 1)
            base_score -= weight

        # 确保评分在0-100之间
        return max(0.0, min(100.0, base_score))

    def _determine_quality_level(self, score: float) -> DataQualityLevel:
        """确定数据质量等级"""

        if score >= 95:
            return DataQualityLevel.EXCELLENT
        elif score >= 85:
            return DataQualityLevel.GOOD
        elif score >= 70:
            return DataQualityLevel.FAIR
        else:
            return DataQualityLevel.POOR

    async def _generate_statistics(self, data: pd.DataFrame, issues: List[DataQualityIssue]) -> Dict[str, Any]:
        """生成统计信息"""

        stats = {
            "basic_info": {
                "rows": len(data),
                "columns": len(data.columns),
                "memory_usage_mb": data.memory_usage(deep=True).sum() / 1024 / 1024,
                "dtypes": data.dtypes.value_counts().to_dict()
            },
            "missing_values": {
                "total_missing": data.isnull().sum().sum(),
                "missing_by_column": data.isnull().sum().to_dict(),
                "missing_percentage": (data.isnull().sum() / len(data) * 100).to_dict()
            },
            "numeric_summary": {},
            "issue_summary": {
                "total_issues": len(issues),
                "issues_by_type": {},
                "issues_by_severity": {}
            }
        }

        # 数值列统计
        numeric_data = data.select_dtypes(include=[np.number])
        if not numeric_data.empty:
            stats["numeric_summary"] = {
                "mean": numeric_data.mean().to_dict(),
                "std": numeric_data.std().to_dict(),
                "min": numeric_data.min().to_dict(),
                "max": numeric_data.max().to_dict(),
                "median": numeric_data.median().to_dict()
            }

        # 问题统计
        for issue in issues:
            issue_type = issue.issue_type.value
            severity = issue.severity

            stats["issue_summary"]["issues_by_type"][issue_type] = \
                stats["issue_summary"]["issues_by_type"].get(issue_type, 0) + 1

            stats["issue_summary"]["issues_by_severity"][severity] = \
                stats["issue_summary"]["issues_by_severity"].get(severity, 0) + 1

        return stats

    async def _generate_recommendations(self, issues: List[DataQualityIssue]) -> List[str]:
        """生成改进建议"""

        recommendations = []

        # 按问题类型分组
        issues_by_type = {}
        for issue in issues:
            issue_type = issue.issue_type
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)

        # 生成针对性建议
        if DataIssueType.MISSING_VALUES in issues_by_type:
            recommendations.append("处理缺失值：考虑使用插值、前向填充或删除策略")

        if DataIssueType.OUTLIERS in issues_by_type:
            recommendations.append("处理异常值：使用Winsorization、Z-score过滤或业务规则验证")

        if DataIssueType.DUPLICATES in issues_by_type:
            recommendations.append("删除重复数据：检查数据收集流程，确保数据唯一性")

        if DataIssueType.FEATURE_CORRELATION in issues_by_type:
            recommendations.append("处理特征相关性：考虑特征选择、主成分分析或正则化方法")

        if DataIssueType.TEMPORAL_GAPS in issues_by_type:
            recommendations.append("处理时间间隔：检查数据收集频率，考虑插值或重采样")

        # 通用建议
        critical_issues = [i for i in issues if i.severity == "critical"]
        if critical_issues:
            recommendations.insert(0, "优先处理严重问题：数据质量存在关键缺陷，建议立即修复")

        auto_fixable_issues = [i for i in issues if i.auto_fixable]
        if auto_fixable_issues:
            recommendations.append(f"自动修复：{len(auto_fixable_issues)}个问题可以自动修复")

        return recommendations

    async def auto_fix_issues(self, data: pd.DataFrame, issues: List[DataQualityIssue]) -> Tuple[pd.DataFrame, List[str]]:
        """自动修复可修复的问题"""

        fixed_data = data.copy()
        fix_log = []

        for issue in issues:
            if not issue.auto_fixable:
                continue

            try:
                if issue.issue_type == DataIssueType.MISSING_VALUES:
                    # 处理缺失值
                    for column in issue.affected_columns:
                        if column in fixed_data.columns:
                            if fixed_data[column].dtype in ['float64', 'int64']:
                                # 数值列使用前向填充
                                fixed_data[column].fillna(method='ffill', inplace=True)
                                fixed_data[column].fillna(fixed_data[column].mean(), inplace=True)
                            else:
                                # 非数值列使用前向填充
                                fixed_data[column].fillna(method='ffill', inplace=True)

                            fix_log.append(f"修复缺失值: {column}")

                elif issue.issue_type == DataIssueType.DUPLICATES:
                    # 删除重复行
                    before_count = len(fixed_data)
                    fixed_data.drop_duplicates(inplace=True)
                    after_count = len(fixed_data)
                    fix_log.append(f"删除重复行: {before_count - after_count}行")

                elif issue.issue_type == DataIssueType.OUTLIERS:
                    # 使用Winsorization处理异常值
                    for column in issue.affected_columns:
                        if column in fixed_data.columns and fixed_data[column].dtype in ['float64', 'int64']:
                            q1 = fixed_data[column].quantile(0.01)
                            q99 = fixed_data[column].quantile(0.99)
                            fixed_data[column] = fixed_data[column].clip(lower=q1, upper=q99)
                            fix_log.append(f"修复异常值: {column} (Winsorization)")

                self.stats["auto_fixes_applied"] += 1

            except Exception as e:
                logger.warning(f"自动修复失败: {issue.issue_type} - {e}")

        return fixed_data, fix_log

    def get_quality_standards(self) -> Dict[str, Any]:
        """获取质量标准配置"""
        return self.quality_standards.copy()

    def update_quality_standards(self, new_standards: Dict[str, Any]):
        """更新质量标准"""
        self.quality_standards.update(new_standards)
        logger.info("质量标准已更新")

    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""

        return {
            "service_name": self.service_name,
            "version": self.version,
            "quality_standards": self.quality_standards,
            "performance_stats": self.stats,
            "supported_issue_types": [t.value for t in DataIssueType],
            "quality_levels": [l.value for l in DataQualityLevel],
            "timestamp": datetime.now().isoformat()
        }
