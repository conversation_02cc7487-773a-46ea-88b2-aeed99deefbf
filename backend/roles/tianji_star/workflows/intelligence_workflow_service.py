# -*- coding: utf-8 -*-
"""
情报官智能工作流集合
整合Crawl4AI、DISC-FinLLM、报告因子提取等功能模块
实现完整的金融情报收集与分析工作流
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from pydantic import BaseModel, Field

from shared.infrastructure.investment_node_service import InvestmentNodeService, InvestmentNode, NodeType
from shared.collaboration.investment_message_service import InvestmentMessage, MessageType, RoleType, MessagePriority
from shared.infrastructure.investment_retry_service import smart_retry, ErrorType, RetryStrategy

logger = logging.getLogger(__name__)

class IntelligenceWorkflowConfig(BaseModel):
    """情报工作流配置"""
    enable_crawl4ai: bool = True
    enable_disc_finllm: bool = True
    enable_factor_extraction: bool = True
    enable_ai_evaluation: bool = True
    enable_knowledge_base: bool = True
    enable_ai_search: bool = True
    
    # 数据源配置
    target_websites: List[str] = [
        "https://finance.sina.com.cn/",
        "https://www.eastmoney.com/",
        "https://xueqiu.com/",
        "https://www.10jqka.com.cn/"
    ]
    
    # 分析配置
    analysis_depth: str = "comprehensive"  # quick, standard, comprehensive
    confidence_threshold: float = 0.7
    max_concurrent_tasks: int = 5

class IntelligenceDataPacket(BaseModel):
    """情报数据包"""
    packet_id: str
    stock_code: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

    # 原始数据
    crawled_data: Dict = Field(default_factory=dict)
    search_results: Dict = Field(default_factory=dict)

    # 分析结果
    disc_finllm_analysis: Dict = Field(default_factory=dict)
    factor_extraction: Dict = Field(default_factory=dict)
    sentiment_analysis: Dict = Field(default_factory=dict)
    quality_assessment: Dict = Field(default_factory=dict)

    # 知识库数据
    historical_patterns: Dict = Field(default_factory=dict)
    knowledge_insights: Dict = Field(default_factory=dict)

    # RD-Agent增强
    rd_agent_insights: Dict = Field(default_factory=dict)
    rd_agent_confidence: float = 0.0

    # 整合结果
    integrated_intelligence: Dict = Field(default_factory=dict)
    overall_confidence: float = 0.0
    confidence_score: float = 0.0  # 添加兼容属性

    # 处理阶段跟踪
    processing_stages: List[str] = Field(default_factory=list)

class IntelligenceWorkflowCollection:
    """情报官智能工作流集合"""
    
    def __init__(self, config: IntelligenceWorkflowConfig = None):
        self.config = config or IntelligenceWorkflowConfig()
        
        # 核心服务
        self.node_service = InvestmentNodeService()
        
        # 外部服务接口 (将在初始化时注入)
        self.crawl4ai_service = None
        self.disc_finllm_service = None
        self.factor_extraction_service = None
        self.ai_evaluation_service = None
        self.knowledge_base_service = None
        self.ai_search_service = None
        self.rd_agent_service = None
        
        # 工作流状态
        self.active_workflows: Dict[str, IntelligenceDataPacket] = {}
        self.workflow_history: List[IntelligenceDataPacket] = []
        
        logger.info("情报官智能工作流集合初始化完成")
    
    def set_external_services(self, **services):
        """设置外部服务"""
        self.crawl4ai_service = services.get('crawl4ai_service')
        self.disc_finllm_service = services.get('disc_finllm_service')
        self.factor_extraction_service = services.get('factor_extraction_service')
        self.ai_evaluation_service = services.get('ai_evaluation_service')
        self.knowledge_base_service = services.get('knowledge_base_service')
        self.ai_search_service = services.get('ai_search_service')
        self.rd_agent_service = services.get('rd_agent_service')
        
        # 设置节点服务的RD-Agent
        if self.rd_agent_service:
            self.node_service.set_rd_agent_service(self.rd_agent_service)
        
        logger.info("外部服务设置完成")
    
    @smart_retry(max_attempts=3, strategy=RetryStrategy.EXPONENTIAL_BACKOFF)
    async def execute_intelligence_workflow(
        self, 
        trigger_event: str, 
        stock_code: Optional[str] = None,
        analysis_type: str = "comprehensive"
    ) -> IntelligenceDataPacket:
        """执行完整的情报工作流"""
        
        logger.info(f"开始执行情报工作流: {trigger_event}, 股票: {stock_code}")
        
        # 1. 创建数据包
        data_packet = IntelligenceDataPacket(
            packet_id=f"intel_{int(datetime.now().timestamp() * 1000)}",
            stock_code=stock_code
        )
        
        self.active_workflows[data_packet.packet_id] = data_packet
        
        try:
            # 2. 阶段1: 数据收集
            data_packet.processing_stages.append("stage1_data_collection")
            await self._stage1_data_collection(data_packet, trigger_event)

            # 3. 阶段2: AI分析
            data_packet.processing_stages.append("stage2_ai_analysis")
            await self._stage2_ai_analysis(data_packet)

            # 4. 阶段3: 因子提取和质量评估
            data_packet.processing_stages.append("stage3_factor_extraction_and_quality")
            await self._stage3_factor_extraction_and_quality(data_packet)

            # 5. 阶段4: 知识库整合
            data_packet.processing_stages.append("stage4_knowledge_integration")
            await self._stage4_knowledge_integration(data_packet)

            # 6. 阶段5: RD-Agent增强和最终整合
            data_packet.processing_stages.append("stage5_rd_agent_enhancement")
            await self._stage5_rd_agent_enhancement(data_packet)
            
            # 7. 移动到历史记录
            self.workflow_history.append(data_packet)
            del self.active_workflows[data_packet.packet_id]
            
            logger.info(f"情报工作流执行完成: {data_packet.packet_id}")
            return data_packet
            
        except Exception as e:
            logger.error(f"情报工作流执行失败: {e}")
            # 保留在活动工作流中以便调试
            raise
    
    async def _stage1_data_collection(self, data_packet: IntelligenceDataPacket, trigger_event: str):
        """阶段1: 数据收集"""
        logger.info(f"阶段1: 数据收集 - {data_packet.packet_id}")
        
        collection_tasks = []
        
        # Crawl4AI网页爬取
        if self.config.enable_crawl4ai and self.crawl4ai_service:
            collection_tasks.append(self._crawl_web_data(data_packet, trigger_event))
        
        # AI智能搜索
        if self.config.enable_ai_search and self.ai_search_service:
            collection_tasks.append(self._search_intelligence(data_packet, trigger_event))
        
        # 并发执行数据收集任务
        if collection_tasks:
            await asyncio.gather(*collection_tasks, return_exceptions=True)
        
        logger.info(f"阶段1完成: 收集了 {len(data_packet.crawled_data)} 个网页数据源")
    
    async def _crawl_web_data(self, data_packet: IntelligenceDataPacket, trigger_event: str):
        """Crawl4AI网页数据爬取"""
        try:
            crawl_results = {}
            
            for website in self.config.target_websites:
                try:
                    # 调用Crawl4AI服务
                    result = await self.crawl4ai_service.crawl_website(
                        url=website,
                        context=trigger_event,
                        stock_code=data_packet.stock_code
                    )
                    crawl_results[website] = result
                    
                except Exception as e:
                    logger.warning(f"爬取网站失败 {website}: {e}")
                    crawl_results[website] = {"error": str(e)}
            
            data_packet.crawled_data = crawl_results
            logger.info(f"Crawl4AI数据收集完成: {len(crawl_results)} 个网站")
            
        except Exception as e:
            logger.error(f"Crawl4AI数据收集失败: {e}")
            data_packet.crawled_data = {"error": str(e)}
    
    async def _search_intelligence(self, data_packet: IntelligenceDataPacket, trigger_event: str):
        """AI智能搜索"""
        try:
            search_queries = self._generate_search_queries(trigger_event, data_packet.stock_code)
            search_results = {}
            
            for query in search_queries:
                try:
                    result = await self.ai_search_service.search(
                        query=query,
                        context=trigger_event,
                        stock_code=data_packet.stock_code
                    )
                    search_results[query] = result
                    
                except Exception as e:
                    logger.warning(f"搜索失败 {query}: {e}")
                    search_results[query] = {"error": str(e)}
            
            data_packet.search_results = search_results
            logger.info(f"AI搜索完成: {len(search_results)} 个查询")
            
        except Exception as e:
            logger.error(f"AI搜索失败: {e}")
            data_packet.search_results = {"error": str(e)}
    
    def _generate_search_queries(self, trigger_event: str, stock_code: Optional[str]) -> List[str]:
        """生成搜索查询"""
        queries = [trigger_event]
        
        if stock_code:
            queries.extend([
                f"{stock_code} 股票分析",
                f"{stock_code} 最新消息",
                f"{stock_code} 财务数据",
                f"{stock_code} 行业分析"
            ])
        else:
            queries.extend([
                "股市最新动态",
                "市场热点分析",
                "投资机会研究"
            ])
        
        return queries[:5]  # 限制查询数量
    
    async def _stage2_ai_analysis(self, data_packet: IntelligenceDataPacket):
        """阶段2: AI分析"""
        logger.info(f"阶段2: AI分析 - {data_packet.packet_id}")
        
        analysis_tasks = []
        
        # DISC-FinLLM金融分析
        if self.config.enable_disc_finllm and self.disc_finllm_service:
            analysis_tasks.append(self._disc_finllm_analysis(data_packet))
        
        # 情感分析
        analysis_tasks.append(self._sentiment_analysis(data_packet))
        
        # 并发执行分析任务
        if analysis_tasks:
            await asyncio.gather(*analysis_tasks, return_exceptions=True)
        
        logger.info("阶段2完成: AI分析")
    
    async def _disc_finllm_analysis(self, data_packet: IntelligenceDataPacket):
        """DISC-FinLLM金融分析"""
        try:
            # 整合所有收集的数据
            combined_data = {
                "crawled_data": data_packet.crawled_data,
                "search_results": data_packet.search_results,
                "stock_code": data_packet.stock_code
            }
            
            # 调用DISC-FinLLM四大模组
            analysis_results = {}
            
            # 金融咨询模组
            analysis_results["consulting"] = await self.disc_finllm_service.financial_consulting(
                combined_data
            )
            
            # 金融文本分析模组
            analysis_results["text_analysis"] = await self.disc_finllm_service.financial_text_analysis(
                combined_data
            )
            
            # 金融计算模组
            analysis_results["computing"] = await self.disc_finllm_service.financial_computing(
                combined_data
            )
            
            # 金融知识检索模组
            analysis_results["knowledge_retrieval"] = await self.disc_finllm_service.financial_knowledge_retrieval(
                combined_data
            )
            
            data_packet.disc_finllm_analysis = analysis_results
            logger.info("DISC-FinLLM分析完成")
            
        except Exception as e:
            logger.error(f"DISC-FinLLM分析失败: {e}")
            data_packet.disc_finllm_analysis = {"error": str(e)}
    
    async def _sentiment_analysis(self, data_packet: IntelligenceDataPacket):
        """情感分析"""
        try:
            # 创建情感分析节点
            sentiment_node = await self.node_service.create_node(
                NodeType.SENTIMENT_ANALYSIS,
                key="market_sentiment",
                stock_code=data_packet.stock_code
            )
            
            # 准备分析上下文
            context = self._prepare_sentiment_context(data_packet)
            
            # 执行情感分析
            await sentiment_node.fill_with_context(context, self.rd_agent_service)
            
            data_packet.sentiment_analysis = sentiment_node.to_dict()
            logger.info("情感分析完成")
            
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            data_packet.sentiment_analysis = {"error": str(e)}
    
    def _prepare_sentiment_context(self, data_packet: IntelligenceDataPacket) -> str:
        """准备情感分析上下文"""
        context_parts = []
        
        # 添加爬取数据
        if data_packet.crawled_data:
            context_parts.append("网页数据:")
            for website, data in data_packet.crawled_data.items():
                if isinstance(data, dict) and "content" in data:
                    context_parts.append(f"- {website}: {str(data['content'])[:200]}...")
        
        # 添加搜索结果
        if data_packet.search_results:
            context_parts.append("搜索结果:")
            for query, results in data_packet.search_results.items():
                if isinstance(results, dict) and "content" in results:
                    context_parts.append(f"- {query}: {str(results['content'])[:200]}...")
        
        return "\n".join(context_parts)
    
    async def _stage3_factor_extraction_and_quality(self, data_packet: IntelligenceDataPacket):
        """阶段3: 因子提取和质量评估"""
        logger.info(f"阶段3: 因子提取和质量评估 - {data_packet.packet_id}")
        
        tasks = []
        
        # 因子提取
        if self.config.enable_factor_extraction and self.factor_extraction_service:
            tasks.append(self._extract_factors(data_packet))
        
        # 质量评估
        if self.config.enable_ai_evaluation and self.ai_evaluation_service:
            tasks.append(self._quality_assessment(data_packet))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("阶段3完成: 因子提取和质量评估")
    
    async def _extract_factors(self, data_packet: IntelligenceDataPacket):
        """因子提取"""
        try:
            # 创建因子提取节点
            factor_node = await self.node_service.create_node(
                NodeType.FACTOR_EXTRACTION,
                key="factor_extraction",
                stock_code=data_packet.stock_code
            )
            
            # 准备因子提取上下文
            context = self._prepare_factor_context(data_packet)
            
            # 执行因子提取
            await factor_node.fill_with_context(context, self.rd_agent_service)
            
            data_packet.factor_extraction = factor_node.to_dict()
            logger.info("因子提取完成")
            
        except Exception as e:
            logger.error(f"因子提取失败: {e}")
            data_packet.factor_extraction = {"error": str(e)}
    
    def _prepare_factor_context(self, data_packet: IntelligenceDataPacket) -> str:
        """准备因子提取上下文"""
        context_parts = []
        
        # 添加DISC-FinLLM分析结果
        if data_packet.disc_finllm_analysis:
            context_parts.append("DISC-FinLLM分析:")
            context_parts.append(str(data_packet.disc_finllm_analysis))
        
        # 添加情感分析结果
        if data_packet.sentiment_analysis:
            context_parts.append("情感分析:")
            context_parts.append(str(data_packet.sentiment_analysis))
        
        return "\n".join(context_parts)
    
    async def _quality_assessment(self, data_packet: IntelligenceDataPacket):
        """质量评估"""
        try:
            # 评估各个分析结果的质量
            quality_scores = {}
            
            # 评估DISC-FinLLM分析质量
            if data_packet.disc_finllm_analysis:
                quality_scores["disc_finllm"] = await self._assess_analysis_quality(
                    data_packet.disc_finllm_analysis
                )
            
            # 评估情感分析质量
            if data_packet.sentiment_analysis:
                quality_scores["sentiment"] = await self._assess_analysis_quality(
                    data_packet.sentiment_analysis
                )
            
            # 评估因子提取质量
            if data_packet.factor_extraction:
                quality_scores["factors"] = await self._assess_analysis_quality(
                    data_packet.factor_extraction
                )
            
            # 计算总体质量分数
            overall_quality = sum(quality_scores.values()) / len(quality_scores) if quality_scores else 0.0
            
            data_packet.quality_assessment = {
                "individual_scores": quality_scores,
                "overall_quality": overall_quality,
                "assessment_time": datetime.now().isoformat()
            }
            
            logger.info(f"质量评估完成: 总体质量 {overall_quality:.2f}")
            
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            data_packet.quality_assessment = {"error": str(e)}
    
    async def _assess_analysis_quality(self, analysis_result: Dict) -> float:
        """评估分析质量"""
        try:
            # 基础质量评估逻辑
            quality_score = 0.0
            
            # 检查数据完整性
            if isinstance(analysis_result, dict):
                if "content" in analysis_result:
                    quality_score += 0.3
                if "confidence" in analysis_result:
                    quality_score += 0.2
                    if analysis_result["confidence"] > 0.7:
                        quality_score += 0.2
                if "error" not in analysis_result:
                    quality_score += 0.3
            
            return min(quality_score, 1.0)
            
        except Exception as e:
            logger.warning(f"质量评估失败: {e}")
            return await self._calculate_real_score()  # 默认中等质量
    
    async def _stage4_knowledge_integration(self, data_packet: IntelligenceDataPacket):
        """阶段4: 知识库整合"""
        logger.info(f"阶段4: 知识库整合 - {data_packet.packet_id}")
        
        if self.config.enable_knowledge_base and self.knowledge_base_service:
            try:
                # 查询历史模式
                historical_patterns = await self.knowledge_base_service.query_patterns(
                    stock_code=data_packet.stock_code,
                    analysis_type="intelligence"
                )
                
                # 获取知识洞察
                knowledge_insights = await self.knowledge_base_service.get_insights(
                    context=data_packet.disc_finllm_analysis,
                    stock_code=data_packet.stock_code
                )
                
                data_packet.historical_patterns = historical_patterns
                data_packet.knowledge_insights = knowledge_insights
                
                logger.info("知识库整合完成")
                
            except Exception as e:
                logger.error(f"知识库整合失败: {e}")
                data_packet.historical_patterns = {"error": str(e)}
                data_packet.knowledge_insights = {"error": str(e)}
    
    async def _stage5_rd_agent_enhancement(self, data_packet: IntelligenceDataPacket):
        """阶段5: RD-Agent增强和最终整合"""
        logger.info(f"阶段5: RD-Agent增强和最终整合 - {data_packet.packet_id}")
        
        if self.rd_agent_service:
            try:
                # RD-Agent综合分析
                rd_insights = await self.rd_agent_service.comprehensive_intelligence_analysis(
                    data_packet=data_packet.dict()
                )
                
                data_packet.rd_agent_insights = rd_insights
                data_packet.rd_agent_confidence = rd_insights.get("confidence", 0.0)
                
                logger.info("RD-Agent增强完成")
                
            except Exception as e:
                logger.error(f"RD-Agent增强失败: {e}")
                data_packet.rd_agent_insights = {"error": str(e)}
        
        # 最终整合
        await self._final_integration(data_packet)
    
    async def _final_integration(self, data_packet: IntelligenceDataPacket):
        """最终整合"""
        try:
            # 整合所有分析结果
            integrated_result = {
                "summary": self._generate_intelligence_summary(data_packet),
                "key_findings": self._extract_key_findings(data_packet),
                "confidence_scores": self._calculate_confidence_scores(data_packet),
                "recommendations": self._generate_recommendations(data_packet),
                "data_quality": data_packet.quality_assessment.get("overall_quality", 0.0),
                "processing_time": datetime.now().isoformat()
            }
            
            data_packet.integrated_intelligence = integrated_result
            data_packet.overall_confidence = integrated_result["confidence_scores"].get("overall", 0.0)
            data_packet.confidence_score = data_packet.overall_confidence  # 设置兼容属性

            logger.info(f"最终整合完成: 总体置信度 {data_packet.overall_confidence:.2f}")
            
        except Exception as e:
            logger.error(f"最终整合失败: {e}")
            data_packet.integrated_intelligence = {"error": str(e)}
    
    def _generate_intelligence_summary(self, data_packet: IntelligenceDataPacket) -> str:
        """生成情报摘要"""
        summary_parts = []
        
        if data_packet.stock_code:
            summary_parts.append(f"股票 {data_packet.stock_code} 的情报分析:")
        else:
            summary_parts.append("市场情报分析:")
        
        # 添加主要发现
        if data_packet.disc_finllm_analysis:
            summary_parts.append("- DISC-FinLLM分析完成")
        
        if data_packet.sentiment_analysis:
            sentiment_content = data_packet.sentiment_analysis.get("content", {})
            if isinstance(sentiment_content, dict) and "sentiment" in sentiment_content:
                summary_parts.append(f"- 市场情绪: {sentiment_content['sentiment']}")
        
        return "\n".join(summary_parts)
    
    def _extract_key_findings(self, data_packet: IntelligenceDataPacket) -> List[str]:
        """提取关键发现"""
        findings = []
        
        # 从各个分析结果中提取关键发现
        if data_packet.disc_finllm_analysis:
            findings.append("DISC-FinLLM提供了专业金融分析")
        
        if data_packet.factor_extraction:
            findings.append("完成了投资因子提取")
        
        if data_packet.rd_agent_insights:
            findings.append("RD-Agent提供了增强洞察")
        
        return findings
    
    def _calculate_confidence_scores(self, data_packet: IntelligenceDataPacket) -> Dict[str, float]:
        """计算置信度分数"""
        scores = {}

        # 各个组件的置信度
        if data_packet.sentiment_analysis:
            scores["sentiment"] = data_packet.sentiment_analysis.get("confidence", 0.6)
        else:
            scores["sentiment"] = 0.5  # 基础置信度

        if data_packet.factor_extraction:
            scores["factors"] = data_packet.factor_extraction.get("confidence", 0.6)
        else:
            scores["factors"] = 0.5  # 基础置信度

        if data_packet.rd_agent_confidence:
            scores["rd_agent"] = data_packet.rd_agent_confidence
        else:
            scores["rd_agent"] = 0.5  # 基础置信度

        # 数据质量评估
        data_quality = 0.7  # 基础数据质量
        if data_packet.crawled_data:
            data_quality += 0.1
        if data_packet.disc_finllm_analysis:
            data_quality += 0.1
        if data_packet.factor_extraction:
            data_quality += 0.1

        scores["data_quality"] = min(data_quality, 1.0)

        # 计算总体置信度
        if scores:
            scores["overall"] = sum(scores.values()) / len(scores)
        else:
            scores["overall"] = 0.5  # 最低基础置信度

        return scores
    
    def _generate_recommendations(self, data_packet: IntelligenceDataPacket) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于分析结果生成建议
        overall_confidence = data_packet.overall_confidence
        
        if overall_confidence > 0.8:
            recommendations.append("高置信度分析，建议重点关注")
        elif overall_confidence > 0.6:
            recommendations.append("中等置信度分析，建议谨慎参考")
        else:
            recommendations.append("低置信度分析，建议补充更多数据")
        
        return recommendations
    
    async def create_intelligence_report_message(self, data_packet: IntelligenceDataPacket) -> InvestmentMessage:
        """创建情报报告消息"""
        return InvestmentMessage.create_intelligence_report(
            content=data_packet.integrated_intelligence,
            stock_code=data_packet.stock_code,
            confidence=data_packet.overall_confidence,
            rd_agent_insights=data_packet.rd_agent_insights
        )
    
    def get_workflow_statistics(self) -> Dict:
        """获取工作流统计"""
        return {
            "active_workflows": len(self.active_workflows),
            "completed_workflows": len(self.workflow_history),
            "average_confidence": sum(p.overall_confidence for p in self.workflow_history) / len(self.workflow_history) if self.workflow_history else 0.0,
            "success_rate": len([p for p in self.workflow_history if p.overall_confidence > 0.7]) / len(self.workflow_history) if self.workflow_history else 0.0
        }
