#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天璇星清理验证测试
验证模拟API和重复代码清理效果
"""

import asyncio
import sys
import os
sys.path.append(os.getcwd())

import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_tianxuan_star_cleanup_verification():
    print('🧹 天璇星清理验证测试...')
    print('=' * 80)
    
    test_results = {
        "total_tests": 0,
        "passed_tests": 0,
        "failed_tests": 0,
        "test_details": [],
        "cleanup_verification": []
    }
    
    # 1. 验证模拟API已删除
    print('\n🗑️ 验证模拟API已删除...')
    await verify_mock_apis_removed(test_results)
    
    # 2. 验证重复服务已删除
    print('\n🔄 验证重复服务已删除...')
    await verify_duplicate_services_removed(test_results)
    
    # 3. 验证核心服务正常工作
    print('\n✅ 验证核心服务正常工作...')
    await verify_core_services_working(test_results)
    
    # 4. 验证无模拟数据生成
    print('\n🚫 验证无模拟数据生成...')
    await verify_no_mock_data_generation(test_results)
    
    # 生成测试报告
    print('\n' + '=' * 80)
    print('📊 天璇星清理验证报告')
    print('=' * 80)
    
    success_rate = (test_results["passed_tests"] / test_results["total_tests"]) * 100 if test_results["total_tests"] > 0 else 0
    
    print(f'总测试数: {test_results["total_tests"]}')
    print(f'通过测试: {test_results["passed_tests"]}')
    print(f'失败测试: {test_results["failed_tests"]}')
    print(f'成功率: {success_rate:.1f}%')
    
    print('\n📋 详细测试结果:')
    for detail in test_results["test_details"]:
        status = "✅" if detail["passed"] else "❌"
        print(f'   {status} {detail["test_name"]}: {detail["message"]}')
    
    print('\n🧹 清理验证结果:')
    for verification in test_results["cleanup_verification"]:
        print(f'   🗑️ {verification}')
    
    # 评级
    if success_rate >= 95:
        grade = "🏆 完美清理"
    elif success_rate >= 90:
        grade = "✅ 清理良好"
    elif success_rate >= 80:
        grade = "⚠️ 需要进一步清理"
    else:
        grade = "❌ 清理不彻底"
    
    print(f'\n🎖️ 天璇星清理评级: {grade} ({success_rate:.1f}/100)')
    
    return test_results

async def verify_mock_apis_removed(test_results):
    """验证模拟API已删除"""
    mock_files_to_check = [
        "backend/api/data/market_data.py",
        "backend/roles/tianxuan_star/services/real_backtest_analysis_service.py",
        "backend/roles/tianxuan_star/services/real_model_development_service.py",
        "backend/roles/tianxuan_star/services/real_model_evaluation_service.py",
        "backend/roles/tianxuan_star/services/real_factor_research_service.py",
        "backend/roles/tianxuan_star/services/advanced_factor_research_service.py"
    ]
    
    removed_count = 0
    for file_path in mock_files_to_check:
        if not os.path.exists(file_path):
            removed_count += 1
            test_results["cleanup_verification"].append(f"已删除模拟文件: {file_path}")
    
    test_results["total_tests"] += 1
    if removed_count == len(mock_files_to_check):
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "模拟API删除验证",
            "passed": True,
            "message": f"成功删除{removed_count}个模拟API文件"
        })
    else:
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "模拟API删除验证",
            "passed": False,
            "message": f"仅删除{removed_count}/{len(mock_files_to_check)}个模拟API文件"
        })

async def verify_duplicate_services_removed(test_results):
    """验证重复服务已删除"""
    # 检查是否还有重复的服务类
    duplicate_patterns = [
        "MockRealMarketDataService",
        "_generate_test_data",
        "_generate_high_quality_market_data",
        "np.random",
        "random.uniform"
    ]
    
    files_to_check = [
        "backend/roles/tianxuan_star/services/rd_agent_strategy_service.py",
        "backend/roles/tianxuan_star/services/parameter_optimization_service.py",
        "backend/roles/tianxuan_star/services/factor_research_service.py",
        "backend/api/roles/tianxuan_star_api.py"
    ]
    
    clean_files = 0
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_duplicates = any(pattern in content for pattern in duplicate_patterns)
                if not has_duplicates:
                    clean_files += 1
                    test_results["cleanup_verification"].append(f"文件已清理: {file_path}")
                else:
                    test_results["cleanup_verification"].append(f"文件仍有问题: {file_path}")
            except Exception as e:
                print(f'   ⚠️ 无法检查文件 {file_path}: {e}')
    
    test_results["total_tests"] += 1
    if clean_files == len(files_to_check):
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "重复代码清理验证",
            "passed": True,
            "message": f"成功清理{clean_files}个文件的重复代码"
        })
    else:
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "重复代码清理验证",
            "passed": False,
            "message": f"仅清理{clean_files}/{len(files_to_check)}个文件"
        })

async def verify_core_services_working(test_results):
    """验证核心服务正常工作"""
    try:
        # 测试技术分析服务
        from backend.roles.tianxuan_star.services.technical_analysis_service import TechnicalAnalysisService
        tech_service = TechnicalAnalysisService()

        # 测试策略生成服务
        from backend.roles.tianxuan_star.services.strategy_generation_service import StrategyGenerationService
        strategy_service = StrategyGenerationService()
        
        test_results["total_tests"] += 1
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "核心服务导入验证",
            "passed": True,
            "message": "核心服务导入成功"
        })
        
    except Exception as e:
        test_results["total_tests"] += 1
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "核心服务导入验证",
            "passed": False,
            "message": f"核心服务导入失败: {str(e)}"
        })

async def verify_no_mock_data_generation(test_results):
    """验证无模拟数据生成"""
    # 检查剩余文件中是否还有模拟数据生成
    mock_patterns = [
        "np.random.normal",
        "np.random.uniform",
        "random.choice",
        "_generate_mock_",
        "_generate_test_",
        "模拟数据"
    ]
    
    remaining_files = [
        "backend/roles/tianxuan_star/services/technical_analysis_service.py",
        "backend/roles/tianxuan_star/services/disc_finllm_strategy_service.py",
        "backend/roles/tianxuan_star/services/strategy_generation_service.py"
    ]
    
    clean_count = 0
    for file_path in remaining_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_mock_data = any(pattern in content for pattern in mock_patterns)
                if not has_mock_data:
                    clean_count += 1
            except Exception as e:
                print(f'   ⚠️ 无法检查文件 {file_path}: {e}')
    
    test_results["total_tests"] += 1
    if clean_count == len(remaining_files):
        test_results["passed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "模拟数据清理验证",
            "passed": True,
            "message": f"成功清理{clean_count}个文件的模拟数据"
        })
        test_results["cleanup_verification"].append("所有核心文件已清理模拟数据")
    else:
        test_results["failed_tests"] += 1
        test_results["test_details"].append({
            "test_name": "模拟数据清理验证",
            "passed": False,
            "message": f"仅清理{clean_count}/{len(remaining_files)}个文件的模拟数据"
        })

if __name__ == "__main__":
    asyncio.run(test_tianxuan_star_cleanup_verification())
