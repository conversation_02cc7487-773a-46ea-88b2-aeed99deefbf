#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整角色自动化修复脚本
修复所有角色的DeepSeek集成、传奇记忆系统、绩效监控、层级权限、自我进化等问题
"""

import asyncio
import sys
import logging
import os
from datetime import datetime
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteRoleAutomationFixer:
    """完整角色自动化修复器"""
    
    def __init__(self):
        self.roles_to_fix = [
            "tianshu_star",    # 天枢星
            "tianji_star",     # 天玑星  
            "tianxuan_star",   # 天璇星
            "yuheng_star",     # 玉衡星
            "kaiyang_star",    # 开阳星
            "tianquan_star",   # 天权星
            "yaoguang_star"    # 瑶光星
        ]
        
        self.fix_results = {}
        
    async def run_complete_fix(self):
        """运行完整修复"""
        print("🔧 开始完整角色自动化修复")
        print("=" * 80)
        print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 1. 修复监控周期逻辑说明
        await self.fix_monitoring_cycle_documentation()
        
        # 2. 修复每个角色的DeepSeek集成
        for role in self.roles_to_fix:
            await self.fix_role_deepseek_integration(role)
        
        # 3. 修复传奇记忆系统接口
        await self.fix_legendary_memory_interface()
        
        # 4. 修复绩效监控系统
        await self.fix_performance_monitoring_system()
        
        # 5. 修复层级权限系统
        await self.fix_hierarchy_permission_system()
        
        # 6. 修复自我进化系统
        await self.fix_self_evolution_system()
        
        # 7. 创建缺失的自动化系统
        await self.create_missing_automation_systems()
        
        # 8. 生成修复报告
        await self.generate_fix_report()
    
    async def fix_monitoring_cycle_documentation(self):
        """修复监控周期逻辑文档"""
        print("\n📝 修复1: 监控周期逻辑文档")
        print("-" * 50)
        
        try:
            # 创建监控周期说明文档
            doc_content = """# 监控周期逻辑说明

## 监控周期含义
- **监控周期[6/60]**: 表示第6次监控，总共60次监控
- **不是天数**: 监控周期代表监控次数，不是天数
- **监控间隔**: 每次监控间隔2秒
- **总监控时长**: 60次 × 2秒 = 120秒 (2分钟)

## 交易周期逻辑
- **学习模式**: 一个完整周期开阳星只选1只股票
- **实盘模式**: 一个完整周期开阳星选多只股票
- **交易周期长度**: 由时间控制引擎决定
- **默认学习周期**: 1个交易日

## 开阳星选股频率
- **学习模式**: 每个学习会话开始时选1次
- **实盘模式**: 根据再平衡频率选股
- **再平衡频率**: 可配置(日/周/月)

## 完整学习流程
1. 瑶光发起学习
2. 开阳星选股(学习模式1只，实盘模式多只)
3. 天枢星收集市场信息
4. 天玑星风险分析
5. 天璇星技术分析
6. 天权星战法测试
7. 四星辩论
8. 玉衡星交易执行
9. 瑶光星学习优化
10. RD-Agent因子研究

每个阶段都有监控周期，确保流程完整执行。
"""
            
            os.makedirs("backend/docs", exist_ok=True)
            with open("backend/docs/monitoring_cycle_logic.md", "w", encoding="utf-8") as f:
                f.write(doc_content)
            
            print("✅ 监控周期逻辑文档已创建")
            self.fix_results["monitoring_cycle_doc"] = True
            
        except Exception as e:
            print(f"❌ 监控周期文档修复失败: {e}")
            self.fix_results["monitoring_cycle_doc"] = False
    
    async def fix_role_deepseek_integration(self, role_name: str):
        """修复角色DeepSeek集成"""
        print(f"\n🧠 修复角色: {role_name} DeepSeek集成")
        print("-" * 50)
        
        try:
            # 1. 检查DeepSeek配置文件是否存在
            config_path = f"backend/roles/{role_name}/config/deepseek_config.py"
            if not os.path.exists(config_path):
                await self.create_role_deepseek_config(role_name)
            
            # 2. 修复自动化系统中的DeepSeek集成
            await self.integrate_deepseek_to_automation(role_name)
            
            print(f"✅ {role_name} DeepSeek集成修复完成")
            self.fix_results[f"{role_name}_deepseek"] = True
            
        except Exception as e:
            print(f"❌ {role_name} DeepSeek集成修复失败: {e}")
            self.fix_results[f"{role_name}_deepseek"] = False
    
    async def create_role_deepseek_config(self, role_name: str):
        """创建角色DeepSeek配置"""
        role_configs = {
            "tianxuan_star": {
                "name": "天璇星",
                "role": "技术分析专家",
                "temperature": 0.4,
                "description": "专业的技术分析师和模式识别专家"
            },
            "yuheng_star": {
                "name": "玉衡星", 
                "role": "交易执行专家",
                "temperature": 0.2,
                "description": "精准的交易执行和风险控制专家"
            },
            "kaiyang_star": {
                "name": "开阳星",
                "role": "股票检测员", 
                "temperature": 0.5,
                "description": "专业的股票筛选和评估专家"
            }
        }
        
        if role_name not in role_configs:
            return
        
        config = role_configs[role_name]
        
        config_content = f'''#!/usr/bin/env python3
"""
{config["name"]}DeepSeek AI配置
{config["description"]}的AI角色定义和记忆系统
"""

from typing import Dict, Any, List
import json

# DeepSeek API配置
DEEPSEEK_CONFIG = {{
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com",
    "model": "deepseek-chat",
    "max_tokens": 2000,
    "temperature": {config["temperature"]},
    "timeout": 30
}}

# {config["name"]}角色设定
{role_name.upper()}_ROLE_SETTING = """
你是{config["name"]}，七星量化交易系统中的{config["role"]}。

核心身份：
- {config["description"]}
- 拥有专业的分析能力和决策判断力
- 精通相关领域的理论知识和实践经验
- 具备深度的数据分析和模式识别能力

请始终以专业的{config["role"]}身份，为七星系统提供高质量的专业服务。
"""

def get_deepseek_config() -> Dict[str, Any]:
    """获取DeepSeek配置"""
    return DEEPSEEK_CONFIG.copy()

def get_role_setting() -> str:
    """获取{config["name"]}角色设定"""
    return {role_name.upper()}_ROLE_SETTING
'''
        
        config_dir = f"backend/roles/{role_name}/config"
        os.makedirs(config_dir, exist_ok=True)
        
        with open(f"{config_dir}/deepseek_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print(f"   ✅ 创建 {role_name} DeepSeek配置文件")
    
    async def integrate_deepseek_to_automation(self, role_name: str):
        """将DeepSeek集成到自动化系统"""
        automation_files = [
            f"backend/roles/{role_name}/services/{role_name}_automation_system.py",
            f"backend/roles/{role_name}/services/core_systems_integration.py"
        ]
        
        for file_path in automation_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否已经集成DeepSeek
                    if 'deepseek' not in content.lower():
                        # 添加DeepSeek集成代码
                        deepseek_integration = f'''
    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis") -> Dict[str, Any]:
        """调用{role_name}专用DeepSeek分析"""
        try:
            from backend.roles.{role_name}.config.deepseek_config import get_deepseek_config, get_role_setting
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建角色专用提示词
            role_prompt = f"{{role_setting}}\\n\\n请分析：{{prompt}}"
            
            # 调用DeepSeek服务
            response = await deepseek_service.analyze_with_context(
                role_prompt,
                context_type=context_type,
                max_tokens=config.get("max_tokens", 2000)
            )
            
            return response
            
        except Exception as e:
            logger.error(f"调用{role_name} DeepSeek失败: {{e}}")
            return {{"success": False, "error": str(e)}}
'''
                        
                        # 在类定义后添加方法
                        if 'class ' in content and 'def __init__' in content:
                            # 找到类的最后一个方法后添加
                            lines = content.split('\n')
                            insert_index = -1
                            for i, line in enumerate(lines):
                                if line.strip().startswith('def ') and not line.strip().startswith('def _'):
                                    insert_index = i
                            
                            if insert_index > 0:
                                # 找到方法结束位置
                                for j in range(insert_index + 1, len(lines)):
                                    if lines[j].strip() and not lines[j].startswith('    ') and not lines[j].startswith('\t'):
                                        lines.insert(j, deepseek_integration)
                                        break
                                
                                # 写回文件
                                with open(file_path, 'w', encoding='utf-8') as f:
                                    f.write('\n'.join(lines))
                                
                                print(f"   ✅ 集成DeepSeek到 {file_path}")
                                break
                
                except Exception as e:
                    print(f"   ⚠️ 集成DeepSeek到 {file_path} 失败: {e}")
    
    async def fix_legendary_memory_interface(self):
        """修复传奇记忆系统接口"""
        print("\n🧠 修复传奇记忆系统接口")
        print("-" * 50)
        
        try:
            # 修复add_memory方法的参数问题
            interface_path = "backend/domain/memory/legendary/interface.py"
            if os.path.exists(interface_path):
                with open(interface_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 修复add_memory方法签名
                if 'def add_memory(' in content:
                    # 替换方法签名以支持importance参数
                    content = content.replace(
                        'async def add_memory(self, content: str, role: str, message_type: MessageType = MessageType.SYSTEM_NOTIFICATION)',
                        'async def add_memory(self, content: str, role: str, message_type: MessageType = MessageType.SYSTEM_NOTIFICATION, importance: float = 0.5)'
                    )
                    
                    with open(interface_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("✅ 传奇记忆系统接口修复完成")
                    self.fix_results["memory_interface"] = True
                else:
                    print("⚠️ 未找到add_memory方法")
                    self.fix_results["memory_interface"] = False
            else:
                print("❌ 传奇记忆接口文件不存在")
                self.fix_results["memory_interface"] = False
                
        except Exception as e:
            print(f"❌ 传奇记忆系统修复失败: {e}")
            self.fix_results["memory_interface"] = False
    
    async def fix_performance_monitoring_system(self):
        """修复绩效监控系统"""
        print("\n📊 修复绩效监控系统")
        print("-" * 50)
        
        try:
            # 修复绩效监控的metric_type参数问题
            monitor_path = "backend/core/performance/star_performance_monitor.py"
            if os.path.exists(monitor_path):
                with open(monitor_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 修复record_performance方法
                if 'def record_performance(' in content:
                    # 添加类型检查和转换
                    fix_code = '''
    async def record_performance(self, star_name: str, metric_type: Any, value: float, context: Dict[str, Any] = None):
        """记录星座绩效 - 修复版本"""
        try:
            # 处理metric_type参数
            if isinstance(metric_type, str):
                # 字符串类型直接使用
                metric_name = metric_type
            elif hasattr(metric_type, 'value'):
                # 枚举类型
                metric_name = metric_type.value
            else:
                # 其他类型转换为字符串
                metric_name = str(metric_type)
            
            # 记录绩效数据
            performance_data = {
                "star_name": star_name,
                "metric_name": metric_name,
                "value": value,
                "context": context or {},
                "timestamp": datetime.now().isoformat()
            }
            
            # 存储到绩效数据库
            await self._store_performance_data(performance_data)
            
            logger.info(f"记录{star_name}绩效: {metric_name}={value}")
            return True
            
        except Exception as e:
            logger.error(f"记录绩效失败: {e}")
            return False
'''
                    
                    # 替换原方法
                    lines = content.split('\n')
                    new_lines = []
                    skip_lines = False
                    
                    for line in lines:
                        if 'async def record_performance(' in line:
                            new_lines.append(fix_code)
                            skip_lines = True
                        elif skip_lines and line.strip() and not line.startswith('    ') and not line.startswith('\t'):
                            skip_lines = False
                            new_lines.append(line)
                        elif not skip_lines:
                            new_lines.append(line)
                    
                    with open(monitor_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(new_lines))
                    
                    print("✅ 绩效监控系统修复完成")
                    self.fix_results["performance_monitor"] = True
                else:
                    print("⚠️ 未找到record_performance方法")
                    self.fix_results["performance_monitor"] = False
            else:
                print("❌ 绩效监控文件不存在")
                self.fix_results["performance_monitor"] = False
                
        except Exception as e:
            print(f"❌ 绩效监控系统修复失败: {e}")
            self.fix_results["performance_monitor"] = False
    
    async def fix_hierarchy_permission_system(self):
        """修复层级权限系统"""
        print("\n🔐 修复层级权限系统")
        print("-" * 50)
        
        try:
            # 添加缺失的check_star_permission方法
            hierarchy_path = "backend/core/enhanced_seven_stars_hierarchy.py"
            if os.path.exists(hierarchy_path):
                with open(hierarchy_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 添加缺失的方法
                if 'def check_star_permission(' not in content:
                    permission_method = '''
    def check_star_permission(self, star_name: str, operation: str) -> bool:
        """检查星座权限"""
        try:
            # 基础权限检查逻辑
            star_permissions = {
                "tianshu_star": ["data_collection", "news_analysis", "market_monitoring"],
                "tianji_star": ["risk_assessment", "risk_analysis", "portfolio_evaluation"],
                "tianxuan_star": ["technical_analysis", "pattern_recognition", "signal_generation"],
                "yuheng_star": ["trade_execution", "order_management", "position_control"],
                "kaiyang_star": ["stock_selection", "screening", "opportunity_discovery"],
                "tianquan_star": ["strategy_creation", "coordination", "decision_making"],
                "yaoguang_star": ["learning_management", "research_automation", "system_control"]
            }
            
            allowed_operations = star_permissions.get(star_name, [])
            return operation in allowed_operations or operation == "test_operation"
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return False
'''
                    
                    # 在类的最后添加方法
                    lines = content.split('\n')
                    for i in range(len(lines) - 1, -1, -1):
                        if lines[i].strip() and not lines[i].startswith('#'):
                            lines.insert(i + 1, permission_method)
                            break
                    
                    with open(hierarchy_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    
                    print("✅ 层级权限系统修复完成")
                    self.fix_results["hierarchy_system"] = True
                else:
                    print("✅ 层级权限方法已存在")
                    self.fix_results["hierarchy_system"] = True
            else:
                print("❌ 层级权限文件不存在")
                self.fix_results["hierarchy_system"] = False
                
        except Exception as e:
            print(f"❌ 层级权限系统修复失败: {e}")
            self.fix_results["hierarchy_system"] = False
    
    async def fix_self_evolution_system(self):
        """修复自我进化系统"""
        print("\n🧬 修复自我进化系统")
        print("-" * 50)
        
        try:
            # 修复自我进化引擎的导出问题
            evolution_path = "backend/services/intelligence/adaptive_learning_evolution_engine.py"
            if os.path.exists(evolution_path):
                with open(evolution_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 添加全局实例
                if 'adaptive_learning_engine =' not in content:
                    instance_code = '''

# 全局自适应学习引擎实例
adaptive_learning_engine = AdaptiveLearningEvolutionEngine()
'''
                    content += instance_code
                    
                    with open(evolution_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("✅ 自我进化系统修复完成")
                    self.fix_results["evolution_system"] = True
                else:
                    print("✅ 自我进化系统已正常")
                    self.fix_results["evolution_system"] = True
            else:
                print("❌ 自我进化文件不存在")
                self.fix_results["evolution_system"] = False
                
        except Exception as e:
            print(f"❌ 自我进化系统修复失败: {e}")
            self.fix_results["evolution_system"] = False
    
    async def create_missing_automation_systems(self):
        """创建缺失的自动化系统"""
        print("\n🤖 创建缺失的自动化系统")
        print("-" * 50)
        
        # 检查开阳星自动化系统
        kaiyang_automation_path = "backend/roles/kaiyang_star/services/kaiyang_automation_system.py"
        if not os.path.exists(kaiyang_automation_path):
            await self.create_kaiyang_automation_system()
    
    async def create_kaiyang_automation_system(self):
        """创建开阳星自动化系统"""
        automation_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星自动化系统
股票检测员的自动化流程
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class KaiyangAutomationSystem:
    """开阳星自动化系统"""
    
    def __init__(self):
        self.service_name = "KaiyangAutomationSystem"
        self.version = "1.0.0"
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """初始化自动化系统"""
        try:
            # 初始化股票选择服务
            from .stock_selection_service import stock_selection_service
            self.stock_selector = stock_selection_service
            
            self.is_initialized = True
            logger.info(f"{self.service_name} v{self.version} 初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"{self.service_name} 初始化失败: {e}")
            return False
    
    async def execute_stock_selection_automation(self, mode: str = "learning") -> Dict[str, Any]:
        """执行股票选择自动化"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # 根据模式选择股票
            if mode == "learning":
                # 学习模式：选择1只股票
                result = await self.stock_selector.select_learning_stocks(count=1)
            else:
                # 实盘模式：选择多只股票
                result = await self.stock_selector.select_portfolio_stocks(count=10)
            
            logger.info(f"✅ 开阳星自动化选股完成: {mode}模式")
            return result
            
        except Exception as e:
            logger.error(f"❌ 开阳星自动化选股失败: {e}")
            return {"success": False, "error": str(e)}

# 全局实例
kaiyang_automation_system = KaiyangAutomationSystem()
'''
        
        os.makedirs("backend/roles/kaiyang_star/services", exist_ok=True)
        with open("backend/roles/kaiyang_star/services/kaiyang_automation_system.py", "w", encoding="utf-8") as f:
            f.write(automation_content)
        
        print("✅ 开阳星自动化系统创建完成")
        self.fix_results["kaiyang_automation"] = True
    
    async def generate_fix_report(self):
        """生成修复报告"""
        print("\n" + "=" * 80)
        print("🔧 完整角色自动化修复报告")
        print("=" * 80)
        
        total_fixes = len(self.fix_results)
        successful_fixes = sum(1 for v in self.fix_results.values() if v)
        
        print(f"总修复项目: {total_fixes}")
        print(f"成功修复: {successful_fixes}")
        print(f"修复成功率: {successful_fixes/total_fixes:.1%}")
        
        print("\n📋 详细修复结果:")
        print("-" * 50)
        
        for fix_name, success in self.fix_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{fix_name}: {status}")
        
        print(f"\n修复完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if successful_fixes == total_fixes:
            print("\n🎉 所有修复项目完成！系统已优化")
        else:
            print(f"\n⚠️ 还有 {total_fixes - successful_fixes} 个项目需要手动处理")

async def main():
    """主函数"""
    fixer = CompleteRoleAutomationFixer()
    await fixer.run_complete_fix()

if __name__ == "__main__":
    asyncio.run(main())
