<template>
  <div class="yaoguang-star-main">
    <!-- 瑶光星学习系统主页面 -->
    <div class="page-header">
      <div class="header-content">
        <div class="star-info">
          <div class="star-icon">🌟</div>
          <div class="star-details">
            <h1>瑶光星学习系统</h1>
            <p>智能量化研究与自动化学习平台</p>
          </div>
        </div>
        <div class="system-status" :class="systemStatus.class">
          <span class="status-indicator"></span>
          <span>{{ systemStatus.text }}</span>
        </div>
      </div>
    </div>

    <!-- 智能调度控制面板 -->
    <div class="scheduler-control-panel">
      <div class="panel-header">
        <h3>🤖 智能自动化调度系统</h3>
        <div class="scheduler-status-badge" :class="schedulerStatus.is_running ? 'running' : 'stopped'">
          {{ schedulerStatus.is_running ? '运行中' : '已停止' }}
        </div>
      </div>

      <div class="scheduler-controls">
        <button
          class="btn btn-primary"
          @click="startScheduler"
          :disabled="schedulerStatus.is_running || schedulerLoading"
        >
          <i class="fas fa-play"></i>
          启动智能调度器
        </button>
        <button
          class="btn btn-danger"
          @click="stopScheduler"
          :disabled="!schedulerStatus.is_running || schedulerLoading"
        >
          <i class="fas fa-stop"></i>
          停止智能调度器
        </button>
        <button
          class="btn btn-info"
          @click="refreshSchedulerStatus"
          :disabled="schedulerLoading"
        >
          <i class="fas fa-sync"></i>
          刷新状态
        </button>
      </div>

      <div class="scheduler-status-grid">
        <div class="status-item">
          <span class="status-label">当前模式:</span>
          <span class="status-value">{{ getModeName(schedulerStatus.current_mode) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">市场状态:</span>
          <span class="status-value">{{ getMarketStatusName(schedulerStatus.market_status) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">是否交易日:</span>
          <span class="status-value">{{ schedulerStatus.is_trading_day ? '是' : '否' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">当前时间:</span>
          <span class="status-value">{{ getCurrentTime() }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">应运行模式:</span>
          <span class="status-value">{{ schedulerStatus.should_run_live_trading ? '实盘交易' : '学习模式' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">今日统计:</span>
          <span class="status-value">学习{{ schedulerStatus.daily_stats?.learning_sessions || 0 }}次 交易{{ schedulerStatus.daily_stats?.live_trading_sessions || 0 }}次</span>
        </div>
      </div>

      <div class="scheduler-alert">
        <div v-if="schedulerStatus.should_run_live_trading" class="alert alert-success">
          <i class="fas fa-chart-line"></i>
          当前应运行实盘交易模式
        </div>
        <div v-else class="alert alert-info">
          <i class="fas fa-graduation-cap"></i>
          当前应运行学习模式
        </div>
      </div>
    </div>

    <!-- 学习模式控制面板 -->
    <div class="learning-control-panel">
      <div class="panel-header">
        <h3>🎓 瑶光星学习模式</h3>
        <div class="learning-status-badge" :class="learningStatus.session_active ? 'active' : 'inactive'">
          {{ learningStatus.session_active ? '学习中' : '空闲' }}
        </div>
      </div>

      <div class="learning-controls">
        <button
          class="btn btn-success"
          @click="startLearning"
          :disabled="learningStatus.session_active || learningLoading"
        >
          <i class="fas fa-play"></i>
          开始学习
        </button>
        <button
          class="btn btn-warning"
          @click="stopLearning"
          :disabled="!learningStatus.session_active || learningLoading"
        >
          <i class="fas fa-stop"></i>
          停止学习
        </button>
        <button
          class="btn btn-info"
          @click="refreshLearningStatus"
          :disabled="learningLoading"
        >
          <i class="fas fa-sync"></i>
          刷新状态
        </button>
        <button
          class="btn btn-secondary"
          @click="viewLearningReport"
          :disabled="!learningStatus.session_id"
        >
          <i class="fas fa-chart-bar"></i>
          查看报告
        </button>
      </div>

      <div class="learning-progress" v-if="learningStatus.session_active">
        <div class="progress-header">
          <span>学习进度</span>
          <span>{{ learningStatus.learning_progress }}%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: learningStatus.learning_progress + '%' }"></div>
        </div>
        <div class="current-step" v-if="learningStatus.current_step">
          当前步骤: {{ learningStatus.current_step }}
        </div>
        <div class="current-stock" v-if="learningStatus.current_stock">
          当前股票: {{ learningStatus.current_stock }}
        </div>
      </div>

      <div class="learning-stats-grid">
        <div class="stat-item">
          <span class="stat-label">会话ID:</span>
          <span class="stat-value">{{ learningStatus.session_id || '无' }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">处理股票:</span>
          <span class="stat-value">{{ learningStatus.processed_stocks || 0 }}只</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">开始时间:</span>
          <span class="stat-value">{{ formatTime(learningStatus.start_time) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">运行时长:</span>
          <span class="stat-value">{{ calculateDuration(learningStatus.start_time) }}</span>
        </div>
      </div>
    </div>

    <!-- 实时日志面板 -->
    <div class="logs-panel">
      <div class="panel-header">
        <h3>📋 实时日志</h3>
        <div class="log-controls">
          <button class="btn btn-sm btn-secondary" @click="clearLogs">
            <i class="fas fa-trash"></i>
            清空日志
          </button>
          <button class="btn btn-sm btn-info" @click="autoScroll = !autoScroll">
            <i class="fas fa-arrow-down"></i>
            {{ autoScroll ? '停止滚动' : '自动滚动' }}
          </button>
        </div>
      </div>

      <div class="logs-container" ref="logsContainer">
        <div
          v-for="log in realTimeLogs"
          :key="log.id"
          class="log-entry"
          :class="'log-' + log.level"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="realTimeLogs.length === 0" class="no-logs">
          暂无日志信息
        </div>
      </div>
    </div>

    <!-- 学习报告弹窗 -->
    <div v-if="showReportDialog" class="report-dialog-overlay" @click="closeReportDialog">
      <div class="report-dialog" @click.stop>
        <div class="dialog-header">
          <h3>📊 学习报告</h3>
          <button class="close-btn" @click="closeReportDialog">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="dialog-content">
          <div v-if="currentReport" class="report-content">
            <div class="report-summary">
              <div class="summary-item">
                <span class="label">总体评分:</span>
                <span class="value score">{{ currentReport.overall_score }}/100</span>
              </div>
              <div class="summary-item">
                <span class="label">交易盈亏:</span>
                <span class="value" :class="currentReport.trading_summary?.total_pnl >= 0 ? 'profit' : 'loss'">
                  ¥{{ currentReport.trading_summary?.total_pnl?.toFixed(2) || '0.00' }}
                </span>
              </div>
              <div class="summary-item">
                <span class="label">收益率:</span>
                <span class="value" :class="currentReport.trading_summary?.return_rate >= 0 ? 'profit' : 'loss'">
                  {{ currentReport.trading_summary?.return_rate?.toFixed(4) || '0.0000' }}%
                </span>
              </div>
              <div class="summary-item">
                <span class="label">生成因子:</span>
                <span class="value">{{ currentReport.factor_analysis?.generated_factors || 0 }}个</span>
              </div>
            </div>

            <div class="report-details">
              <h4>各星表现</h4>
              <div v-if="currentReport.star_performance" class="star-performance">
                <div v-for="(performance, star) in currentReport.star_performance" :key="star" class="star-item">
                  <span class="star-name">{{ getStarName(star) }}:</span>
                  <span class="star-score">{{ performance.score }}/100</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="loading-report">
            <i class="fas fa-spinner fa-spin"></i>
            加载报告中...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import axios from 'axios'

console.log('🌟 瑶光星学习系统页面加载')

// 系统状态
const systemStatus = ref({
  class: 'status-running',
  text: '系统正常运行'
})

// 调度器状态
const schedulerStatus = ref({
  is_running: false,
  current_mode: 'idle',
  market_status: 'closed',
  is_trading_day: false,
  should_run_live_trading: false,
  should_run_learning: false,
  current_time: null,
  daily_stats: {
    learning_sessions: 0,
    live_trading_sessions: 0,
    total_trades: 0,
    total_pnl: 0.0
  },
  tasks_status: {
    learning_task: 'stopped',
    live_trading_task: 'stopped',
    scheduler_task: 'stopped'
  }
})

// 加载状态
const schedulerLoading = ref(false)
const learningLoading = ref(false)

// 学习状态
const learningStatus = ref({
  session_active: false,
  session_id: null,
  processed_stocks: 0,
  learning_progress: 0,
  current_step: null,
  start_time: null,
  current_stock: null
})

// 实时日志
const realTimeLogs = ref([])
const logsContainer = ref(null)
const autoScroll = ref(true)

// 学习报告
const showReportDialog = ref(false)
const currentReport = ref(null)

// 智能调度相关方法
const loadSchedulerStatus = async () => {
  try {
    const response = await axios.get('/api/yaoguang-star/scheduler/status')
    if (response.data.success) {
      Object.assign(schedulerStatus.value, response.data.data)
    }
  } catch (error) {
    console.error('加载调度器状态失败:', error)
  }
}

const startScheduler = async () => {
  schedulerLoading.value = true
  try {
    const response = await axios.post('/api/yaoguang-star/scheduler/start')
    if (response.data.success) {
      await loadSchedulerStatus()
      console.log('✅ 智能调度器启动成功')
    }
  } catch (error) {
    console.error('启动调度器失败:', error)
  } finally {
    schedulerLoading.value = false
  }
}

const stopScheduler = async () => {
  schedulerLoading.value = true
  try {
    const response = await axios.post('/api/yaoguang-star/scheduler/stop')
    if (response.data.success) {
      await loadSchedulerStatus()
      console.log('✅ 智能调度器停止成功')
    }
  } catch (error) {
    console.error('停止调度器失败:', error)
  } finally {
    schedulerLoading.value = false
  }
}

const refreshSchedulerStatus = async () => {
  schedulerLoading.value = true
  try {
    await loadSchedulerStatus()
    console.log('🔄 调度器状态已刷新')
  } catch (error) {
    console.error('刷新调度器状态失败:', error)
  } finally {
    schedulerLoading.value = false
  }
}

const getModeName = (mode: string) => {
  const modeNames = {
    'learning': '学习模式',
    'live_trading': '实盘交易',
    'idle': '空闲'
  }
  return modeNames[mode] || mode
}

const getMarketStatusName = (status: string) => {
  const statusNames = {
    'pre_market': '盘前',
    'trading': '交易中',
    'post_market': '盘后',
    'closed': '休市'
  }
  return statusNames[status] || status
}

const getCurrentTime = () => {
  return new Date().toLocaleString('zh-CN')
}

// 学习模式相关方法
const loadLearningStatus = async () => {
  try {
    const response = await axios.get('/api/yaoguang-star/learning/status')
    if (response.data.success) {
      Object.assign(learningStatus.value, response.data.data)
    }
  } catch (error) {
    console.error('加载学习状态失败:', error)
  }
}

const startLearning = async () => {
  learningLoading.value = true
  try {
    const response = await axios.post('/api/yaoguang-star/learning/start')
    if (response.data.success) {
      await loadLearningStatus()
      addLog('info', '🎓 学习模式启动成功')
    }
  } catch (error) {
    console.error('启动学习失败:', error)
    addLog('error', '❌ 启动学习失败: ' + error.message)
  } finally {
    learningLoading.value = false
  }
}

const stopLearning = async () => {
  learningLoading.value = true
  try {
    const response = await axios.post('/api/yaoguang-star/learning/stop')
    if (response.data.success) {
      await loadLearningStatus()
      addLog('info', '🛑 学习模式停止成功')
    }
  } catch (error) {
    console.error('停止学习失败:', error)
    addLog('error', '❌ 停止学习失败: ' + error.message)
  } finally {
    learningLoading.value = false
  }
}

const refreshLearningStatus = async () => {
  learningLoading.value = true
  try {
    await loadLearningStatus()
    addLog('info', '🔄 学习状态已刷新')
  } catch (error) {
    console.error('刷新学习状态失败:', error)
    addLog('error', '❌ 刷新学习状态失败: ' + error.message)
  } finally {
    learningLoading.value = false
  }
}

const viewLearningReport = async () => {
  if (!learningStatus.value.session_id) return

  try {
    const response = await axios.get(`/api/yaoguang-star/report/${learningStatus.value.session_id}`)
    if (response.data.success) {
      currentReport.value = response.data.data
      showReportDialog.value = true
    }
  } catch (error) {
    console.error('获取学习报告失败:', error)
    addLog('error', '❌ 获取学习报告失败: ' + error.message)
  }
}

const closeReportDialog = () => {
  showReportDialog.value = false
  currentReport.value = null
}

// 日志相关方法
const addLog = (level, message) => {
  const log = {
    id: Date.now() + Math.random(),
    timestamp: new Date().toISOString(),
    level: level,
    message: message
  }
  realTimeLogs.value.push(log)

  // 保持最多100条日志
  if (realTimeLogs.value.length > 100) {
    realTimeLogs.value.shift()
  }

  // 自动滚动到底部
  if (autoScroll.value) {
    setTimeout(() => {
      if (logsContainer.value) {
        logsContainer.value.scrollTop = logsContainer.value.scrollHeight
      }
    }, 100)
  }
}

const clearLogs = () => {
  realTimeLogs.value = []
  addLog('info', '📝 日志已清空')
}

// 工具方法
const formatTime = (timeStr) => {
  if (!timeStr) return '无'
  try {
    return new Date(timeStr).toLocaleString('zh-CN')
  } catch {
    return timeStr
  }
}

const calculateDuration = (startTime) => {
  if (!startTime) return '0分钟'
  try {
    const start = new Date(startTime)
    const now = new Date()
    const diff = Math.floor((now - start) / 1000 / 60)
    return `${diff}分钟`
  } catch {
    return '0分钟'
  }
}

const getStarName = (starKey) => {
  const starNames = {
    'kaiyang': '开阳星',
    'tianshu': '天枢星',
    'tianxuan': '天璇星',
    'tianji': '天玑星',
    'tianquan': '天权星',
    'yuheng': '玉衡星',
    'yaoguang': '瑶光星'
  }
  return starNames[starKey] || starKey
}

onMounted(async () => {
  console.log('🌟 瑶光星学习系统页面挂载完成')

  // 加载调度器状态和学习状态
  await loadSchedulerStatus()
  await loadLearningStatus()

  // 定时刷新状态
  setInterval(loadSchedulerStatus, 10000) // 10秒刷新调度器状态
  setInterval(loadLearningStatus, 5000)   // 5秒刷新学习状态

  // 添加初始日志
  addLog('info', '🌟 瑶光星学习系统已启动')
})

onUnmounted(() => {
  console.log('🌟 瑶光星学习系统页面卸载')
})


</script>

<style lang="scss" scoped>
.yaoguang-star-main {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  box-sizing: border-box;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.star-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.star-icon {
  font-size: 3rem;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
}

.star-details h1 {
  margin: 0;
  font-size: 2.5rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.star-details p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 1.1rem;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
}

.status-running {
  background: #d4edda;
  color: #155724;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 智能调度控制面板 */
.scheduler-control-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-left: 4px solid #9b59b6;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.scheduler-status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.scheduler-status-badge.running {
  background: #d4edda;
  color: #155724;
}

.scheduler-status-badge.stopped {
  background: #f8d7da;
  color: #721c24;
}

.scheduler-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-danger {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.btn-info {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
}

.btn-info:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
}

.scheduler-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.status-item {
  background: rgba(102, 126, 234, 0.1);
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.status-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.status-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.scheduler-alert {
  margin-top: 20px;
}

.alert {
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.alert i {
  font-size: 1.2rem;
}

/* 学习模式控制面板 */
.learning-control-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-left: 4px solid #27ae60;
}

.learning-status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.learning-status-badge.active {
  background: #d4edda;
  color: #155724;
}

.learning-status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.learning-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.btn-success {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
  color: white;
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

.btn-warning {
  background: linear-gradient(45deg, #f39c12, #e67e22);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.4);
}

.btn-secondary {
  background: linear-gradient(45deg, #95a5a6, #7f8c8d);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(149, 165, 166, 0.4);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.learning-progress {
  background: rgba(39, 174, 96, 0.1);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(45deg, #27ae60, #2ecc71);
  transition: width 0.3s ease;
}

.current-step, .current-stock {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.learning-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-item {
  background: rgba(39, 174, 96, 0.1);
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #27ae60;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

/* 实时日志面板 */
.logs-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-left: 4px solid #3498db;
}

.log-controls {
  display: flex;
  gap: 8px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  font-family: 'Consolas', 'Monaco', monospace;
}

.log-entry {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.log-time {
  color: #666;
  font-size: 0.8rem;
  min-width: 80px;
}

.log-level {
  font-weight: 600;
  min-width: 50px;
  font-size: 0.8rem;
}

.log-info .log-level {
  color: #3498db;
}

.log-success .log-level {
  color: #27ae60;
}

.log-warning .log-level {
  color: #f39c12;
}

.log-error .log-level {
  color: #e74c3c;
}

.log-message {
  flex: 1;
  font-size: 0.9rem;
}

.no-logs {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* 学习报告弹窗 */
.report-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.report-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #eee;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.dialog-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dialog-content {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.summary-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.summary-item .label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.summary-item .value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
}

.value.score {
  color: #3498db;
}

.value.profit {
  color: #27ae60;
}

.value.loss {
  color: #e74c3c;
}

.report-details h4 {
  margin-bottom: 15px;
  color: #2c3e50;
}

.star-performance {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.star-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.star-name {
  font-weight: 500;
}

.star-score {
  font-weight: 600;
  color: #3498db;
}

.loading-report {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading-report i {
  font-size: 2rem;
  margin-bottom: 10px;
}
</style>
