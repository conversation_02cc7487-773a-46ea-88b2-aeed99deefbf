#!/usr/bin/env python3
"""
玉衡星-交易执行 - 基础数据模型
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

@dataclass
class YuhengStarRequest:
    """请求模型"""
    request_id: str
    timestamp: datetime
    parameters: Dict[str, Any]
    user_id: Optional[str] = None

@dataclass 
class YuhengStarResponse:
    """响应模型"""
    success: bool
    data: Dict[str, Any]
    message: str
    timestamp: datetime
    request_id: str

class YuhengStarStatus(Enum):
    """状态枚举"""
    IDLE = "idle"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"

__all__ = [
    'YuhengStarRequest',
    'YuhengStarResponse', 
    'YuhengStarStatus'
]
