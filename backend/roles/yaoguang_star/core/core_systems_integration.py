#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星四大核心系统集成
按照玉衡星模式集成：传奇记忆系统、绩效监控系统、七星层级系统、DeepSeek人设
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
import asyncio

logger = logging.getLogger(__name__)

class YaoguangCoreSystemsIntegration:
    """瑶光星四大核心系统集成管理器"""
    
    def __init__(self):
        self.memory_system = None
        self.performance_monitor = None
        self.hierarchy_system = None
        self.deepseek_config = None
        self.is_initialized = False
        
        # 初始化四大核心系统
        self._initialize_core_systems()
    
    def _initialize_core_systems(self):
        """初始化四大核心系统（按照玉衡星模式）"""
        try:
            # 修复导入路径
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
            core_path = os.path.join(backend_dir, "core")

            if core_path not in sys.path:
                sys.path.insert(0, core_path)

            # 导入四大核心系统
            from domain.memory.legendary.interface import legendary_memory_interface
            from performance.star_performance_monitor import StarPerformanceMonitor, PerformanceMetricType
            from enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 1. 传奇记忆系统
            self.memory_system = legendary_memory_interface
            
            # 2. 绩效监控系统
            self.performance_monitor = StarPerformanceMonitor()
            
            # 3. 层级权限系统
            self.hierarchy_system = EnhancedSevenStarsHierarchy()
            
            # 4. DeepSeek服务
            self.deepseek_service = deepseek_service
            
            # 瑶光星专用配置
            self.yaoguang_config = {
                "role_name": "瑶光星",
                "role_id": "yaoguang_star",
                "authority_level": 7,
                "specialties": ["机器学习", "因子研发", "知识管理", "系统优化"],
                "performance_targets": {
                    PerformanceMetricType.EFFICIENCY: 0.86,
                    PerformanceMetricType.QUALITY_SCORE: 0.84,
                    PerformanceMetricType.ACCURACY: 0.90,
                    PerformanceMetricType.RELIABILITY: 0.88
                }
            }
            
            self.is_initialized = True
            logger.info("[SUCCESS] 瑶光星四大核心系统初始化完成")
            
        except ImportError as e:
            logger.warning(f"[WARNING] 瑶光星核心系统导入失败: {e}")
            self.is_initialized = False
        except Exception as e:
            logger.error(f"[ERROR] 瑶光星核心系统初始化失败: {e}")
            self.is_initialized = False
    
    def is_healthy(self) -> bool:
        """检查核心系统健康状态"""
        return (self.is_initialized and 
                self.memory_system is not None and
                self.performance_monitor is not None and
                self.hierarchy_system is not None and
                self.deepseek_service is not None)
    
    async def record_learning_performance(self, metric_name: str, metric_value: float, 
                                        context: Dict[str, Any] = None) -> bool:
        """记录学习绩效"""
        try:
            if not self.performance_monitor:
                logger.warning("绩效监控系统不可用")
                return False
            
            # 映射绩效类型
            from core.performance.star_performance_monitor import PerformanceMetricType

            metric_type_mapping = {
                "learning_efficiency": PerformanceMetricType.EFFICIENCY,
                "factor_research_quality": PerformanceMetricType.QUALITY_SCORE,
                "automation_efficiency": PerformanceMetricType.EFFICIENCY,
                "system_optimization": PerformanceMetricType.RELIABILITY,
                "accuracy": PerformanceMetricType.ACCURACY,
                "efficiency": PerformanceMetricType.EFFICIENCY,
                "success_rate": PerformanceMetricType.SUCCESS_RATE,
                "quality_score": PerformanceMetricType.QUALITY_SCORE,
                "reliability": PerformanceMetricType.RELIABILITY
            }
            
            metric_type = metric_type_mapping.get(metric_name, PerformanceMetricType.QUALITY_SCORE)
            
            # 记录绩效
            result = await self.performance_monitor.record_performance(
                "瑶光星",
                metric_type,
                metric_value,
                context or {},
                session_id=f"yaoguang_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            if result:
                logger.info(f"瑶光星绩效记录成功: {metric_name}={metric_value}")
            else:
                logger.warning(f"瑶光星绩效记录失败: {metric_name}={metric_value}")
            
            return result
            
        except Exception as e:
            logger.error(f"记录瑶光星绩效失败: {e}")
            return False
    
    async def get_performance_report(self, period_days: int = 30) -> Optional[Dict[str, Any]]:
        """获取瑶光星绩效报告"""
        try:
            if not self.performance_monitor:
                logger.warning("绩效监控系统不可用")
                return None
            
            performance_report = await self.performance_monitor.get_star_performance(
                "瑶光星", period_days
            )
            
            if performance_report:
                return performance_report.__dict__
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取瑶光星绩效报告失败: {e}")
            return None
    
    async def add_memory(self, content: str, message_type_str: str, context: Dict[str, Any] = None) -> bool:
        """添加瑶光星记忆"""
        try:
            if not self.memory_system:
                logger.warning("传奇记忆系统不可用")
                return False
            
            # 导入MessageType和其他枚举
            from core.domain.memory.legendary.models import MessageType, MemoryScope, MemoryPriority
            
            # 映射消息类型
            message_type_mapping = {
                "learning_record": MessageType.LEARNING_RECORD,
                "factor_research": MessageType.FACTOR_RESEARCH,
                "data_quality": MessageType.DATA_QUALITY,
                "system_notification": MessageType.SYSTEM_NOTIFICATION,
                "general": MessageType.GENERAL
            }
            
            message_type = message_type_mapping.get(message_type_str, MessageType.GENERAL)
            
            # 检查是否有瑶光星专用方法
            if hasattr(self.memory_system, 'add_yaoguang_memory'):
                result = await self.memory_system.add_yaoguang_memory(
                    content=content,
                    message_type=message_type
                )
            else:
                # 使用通用方法
                result = await self.memory_system.add_memory(
                    content=content,
                    role="瑶光星",
                    message_type=message_type,
                    scope=MemoryScope.ROLE,
                    priority=MemoryPriority.NORMAL
                )
            
            if result and result.success:
                logger.info(f"瑶光星记忆添加成功: {content[:50]}...")
                return True
            else:
                logger.warning(f"瑶光星记忆添加失败: {result.message if result else 'Unknown error'}")
                return False
                
        except Exception as e:
            logger.error(f"添加瑶光星记忆失败: {e}")
            return False
    
    async def search_memories(self, query_text: str = None, limit: int = 10) -> list:
        """搜索瑶光星记忆"""
        try:
            if not self.memory_system:
                logger.warning("传奇记忆系统不可用")
                return []
            
            if query_text:
                # 语义搜索
                memories = await self.memory_system.semantic_search_memories(
                    query_text=query_text,
                    role="瑶光星",
                    limit=limit
                )
            else:
                # 普通搜索
                memories = await self.memory_system.search_memories(
                    role="瑶光星",
                    limit=limit
                )
            
            return memories or []
            
        except Exception as e:
            logger.error(f"搜索瑶光星记忆失败: {e}")
            return []
    
    async def generate_deepseek_response(self, prompt: str, context_type: str = "learning", 
                                       max_tokens: int = 500) -> Dict[str, Any]:
        """生成DeepSeek响应"""
        try:
            if not self.deepseek_service:
                logger.warning("DeepSeek服务不可用")
                return {
                    "success": False,
                    "content": "DeepSeek服务不可用",
                    "error": "service_unavailable"
                }
            
            # 瑶光星专用提示词
            yaoguang_prompt = f"""
            你是瑶光星，七星量化交易系统中的学习训练专家和量化研究自动化平台。
            你的核心职责包括：
            1. 量化研究自动化 - 7x24小时自动化量化研究
            2. Alpha158因子研究 - 完整的因子计算和分析
            3. 学习环境管理 - 创建和管理学习环境
            4. 数据质量控制 - 数据验证和质量评分
            5. 时间控制引擎 - 时间范围设置和推进
            6. RD-Agent集成 - 实验创建和状态管理
            
            请以瑶光星的身份回答：{prompt}
            """
            
            response = await self.deepseek_service.generate_response(
                yaoguang_prompt,
                max_tokens=max_tokens
            )
            
            return response
            
        except Exception as e:
            logger.error(f"生成DeepSeek响应失败: {e}")
            return {
                "success": False,
                "content": f"响应生成失败: {str(e)}",
                "error": "generation_failed"
            }
    
    async def check_hierarchy_permissions(self, action: str, target_role: str = None) -> bool:
        """检查层级权限"""
        try:
            if not self.hierarchy_system:
                logger.warning("层级系统不可用")
                return True  # 默认允许
            
            # 瑶光星权限等级为7（最低）
            yaoguang_authority = 7
            
            # 权限检查逻辑
            if action in ["factor_research", "learning_management", "data_quality"]:
                return True  # 瑶光星的专业领域
            elif action in ["trading_execution", "risk_management"]:
                return False  # 需要更高权限
            else:
                return True  # 默认允许
                
        except Exception as e:
            logger.error(f"检查层级权限失败: {e}")
            return True  # 默认允许
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取系统集成状态"""
        return {
            "core_systems": {
                "memory_system": self.memory_system is not None,
                "performance_monitor": self.performance_monitor is not None,
                "hierarchy_system": self.hierarchy_system is not None,
                "deepseek_service": self.deepseek_service is not None
            },
            "overall_health": self.is_healthy(),
            "integration_score": sum([
                self.memory_system is not None,
                self.performance_monitor is not None,
                self.hierarchy_system is not None,
                self.deepseek_service is not None
            ]) / 4 * 100,
            "role_config": getattr(self, 'yaoguang_config', {}),
            "last_check": datetime.now().isoformat()
        }

# 全局瑶光星核心系统集成实例
yaoguang_core_systems = YaoguangCoreSystemsIntegration()
