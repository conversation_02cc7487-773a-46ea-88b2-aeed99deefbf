#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星自动化系统修复验证
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_tianji_automation_system():
    """测试天玑星自动化系统"""
    try:
        logger.info("🔧 测试天玑星自动化系统...")
        
        from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
        
        # 测试系统状态
        status = tianji_automation_system.get_automation_status()
        logger.info(f"✅ 天玑星自动化系统状态: {status['system_name']} v{status['version']}")
        
        # 测试风险分析功能
        context = {
            "stock_code": "000001.XSHE",
            "task_type": "comprehensive_risk_analysis",
            "session_id": "test_session",
            "position_size": 100000,
            "market_context": {
                "market_trend": "neutral",
                "volatility_regime": "normal"
            }
        }
        
        logger.info("开始测试风险分析功能...")
        result = await tianji_automation_system.execute_risk_analysis(context)
        
        if result.get("success"):
            analysis = result["analysis_result"]
            logger.info(f"✅ 风险分析成功: {analysis['stock_code']}")
            logger.info(f"  综合风险等级: {analysis['comprehensive_risk']['risk_level']}")
            logger.info(f"  风险评分: {analysis['comprehensive_risk']['comprehensive_risk_score']:.3f}")
            logger.info(f"  建议: {analysis['risk_recommendations']['position_advice']}")
            return True
        else:
            logger.error(f"❌ 风险分析失败: {result.get('error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 天玑星自动化系统测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始天玑星自动化系统修复验证")
    logger.info("=" * 50)
    
    success = await test_tianji_automation_system()
    
    logger.info("=" * 50)
    if success:
        logger.info("🎉 天玑星自动化系统修复成功！")
    else:
        logger.info("❌ 天玑星自动化系统仍有问题")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
