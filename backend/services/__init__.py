"""
Web_pros Backend Services
分类服务模块
"""

# 核心服务 - 按需导入
def get_intelligent_crawler():
    from .core.enhanced_crawl4ai_service import IntelligentCrawlerService
    return IntelligentCrawlerService

def get_websocket_manager():
    from .core.websocket_manager import WebSocketManager
    return WebSocket<PERSON>anager

def get_task_manager():
    from .core.task_manager import TaskManager
    return TaskManager

# 数据服务 - 按需导入
def get_multi_source_manager():
    from .data.multi_source_data_manager import MultiSourceDataManager
    return MultiSourceDataManager

# 直接导入多源数据管理器实例
try:
    from .data.multi_source_data_manager import multi_source_data_manager
except ImportError:
    multi_source_data_manager = None

def get_sina_service():
    from .data.enhanced_sina_service import EnhancedSinaService
    return EnhancedSinaService

def get_tencent_service():
    from .data.enhanced_tencent_service import EnhancedTencentService
    return EnhancedTencentService

# AI算法服务 - 按需导入
def get_sentiment_analyzer():
    from .ai.enhanced_sentiment_analyzer import EnhancedSentimentAnalyzer
    return EnhancedSentimentAnalyzer

def get_concept_drift_detector():
    from .ai.concept_drift_detector import ConceptDriftDetector
    return ConceptDriftDetector

__all__ = [
    "get_intelligent_crawler",
    "get_websocket_manager",
    "get_task_manager",
    "get_multi_source_manager",
    "get_sina_service",
    "get_tencent_service",
    "get_sentiment_analyzer",
    "get_concept_drift_detector",
    "multi_source_data_manager"
]
