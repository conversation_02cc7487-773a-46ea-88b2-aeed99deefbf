#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星专用API路由层
提供完整的风险管理API接口
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Any, Optional, List, Dict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 创建天玑星路由器
tianji_star_router = APIRouter(prefix="/api/tianji", tags=["天玑星-风险管理"])

# ==================== 请求/响应模型 ====================

class RiskAssessmentRequest(BaseModel):
    """风险评估请求"""
    stock_code: str = Field(..., description="股票代码")
    position_size: Optional[float] = Field(default=0, description="持仓规模")
    market_context: Optional[Dict[str, Any]] = Field(default=None, description="市场环境")

class PortfolioRiskRequest(BaseModel):
    """投资组合风险评估请求"""
    portfolio: List[Dict[str, Any]] = Field(..., description="投资组合")
    analysis_type: Optional[str] = Field(default="comprehensive", description="分析类型")

class RiskMonitoringRequest(BaseModel):
    """风险监控请求"""
    stock_codes: List[str] = Field(..., description="股票代码列表")
    monitoring_type: Optional[str] = Field(default="real_time", description="监控类型")
    alert_threshold: Optional[float] = Field(default=0.7, description="预警阈值")

class ApiResponse(BaseModel):
    """统一API响应格式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    timestamp: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat())

# ==================== 服务依赖 ====================

def get_risk_assessment_service():
    """获取风险评估服务"""
    try:
        from backend.roles.tianji_star.services.risk_assessment_service import risk_assessment_service
        return risk_assessment_service
    except ImportError as e:
        logger.error(f"风险评估服务导入失败: {e}")
        return None

def get_risk_knowledge_base_service():
    """获取风险知识库服务"""
    try:
        from backend.roles.tianji_star.services.risk_knowledge_base_service import risk_knowledge_base_service
        return risk_knowledge_base_service
    except ImportError as e:
        logger.error(f"风险知识库服务导入失败: {e}")
        return None

def get_workflow_manager():
    """获取工作流管理器"""
    try:
        from backend.roles.tianji_star.workflows.workflow_manager import workflow_manager
        return workflow_manager
    except ImportError as e:
        logger.error(f"工作流管理器导入失败: {e}")
        return None

# ==================== 基础API ====================

@tianji_star_router.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    try:
        # 检查各个服务状态
        services_status = {}
        
        risk_service = get_risk_assessment_service()
        services_status["risk_assessment"] = "healthy" if risk_service else "unavailable"
        
        knowledge_service = get_risk_knowledge_base_service()
        services_status["knowledge_base"] = "healthy" if knowledge_service else "unavailable"
        
        workflow_service = get_workflow_manager()
        services_status["workflow_manager"] = "healthy" if workflow_service else "unavailable"
        
        return ApiResponse(
            message="天玑星健康检查完成",
            data={
                "role": "天玑星-风险管理",
                "status": "healthy",
                "version": "2.0.0",
                "services": services_status,
                "capabilities": [
                    "股票风险评估",
                    "投资组合风险分析",
                    "实时风险监控",
                    "风险预警告警",
                    "风险知识库管理",
                    "工作流管理"
                ]
            }
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.get("/status", response_model=ApiResponse)
async def get_status():
    """获取天玑星状态"""
    try:
        workflow_manager = get_workflow_manager()
        
        if workflow_manager:
            stats = workflow_manager.get_workflow_statistics()
            active_workflows = workflow_manager.list_active_workflows()
        else:
            stats = {"total_executed": 0, "active_count": 0, "success_rate": 0.0}
            active_workflows = []
        
        return ApiResponse(
            message="天玑星状态获取成功",
            data={
                "service_name": "天玑星风险管理中心",
                "status": "active",
                "version": "2.0.0",
                "workflow_statistics": stats,
                "active_workflows": active_workflows,
                "last_update": datetime.now().isoformat()
            }
        )
    except Exception as e:
        logger.error(f"状态获取失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 风险评估API ====================

@tianji_star_router.post("/risk/assess/stock", response_model=ApiResponse)
async def assess_stock_risk(
    request: RiskAssessmentRequest,
    risk_service = Depends(get_risk_assessment_service)
):
    """评估单只股票风险"""
    try:
        if not risk_service:
            raise HTTPException(status_code=503, detail="风险评估服务不可用")
        
        logger.info(f"开始评估股票风险: {request.stock_code}")
        
        result = await risk_service.assess_stock_risk(
            request.stock_code,
            request.position_size,
            request.market_context
        )
        
        return ApiResponse(
            message=f"股票风险评估完成: {request.stock_code}",
            data=result
        )
        
    except Exception as e:
        logger.error(f"股票风险评估失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.post("/risk/assess/portfolio", response_model=ApiResponse)
async def assess_portfolio_risk(
    request: PortfolioRiskRequest,
    risk_service = Depends(get_risk_assessment_service)
):
    """评估投资组合风险"""
    try:
        if not risk_service:
            raise HTTPException(status_code=503, detail="风险评估服务不可用")
        
        logger.info(f"开始评估投资组合风险: {len(request.portfolio)}只股票")
        
        result = await risk_service.assess_portfolio_risk(request.portfolio)
        
        return ApiResponse(
            message=f"投资组合风险评估完成: {len(request.portfolio)}只股票",
            data=result
        )
        
    except Exception as e:
        logger.error(f"投资组合风险评估失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 风险监控API ====================

@tianji_star_router.post("/monitoring/start", response_model=ApiResponse)
async def start_risk_monitoring(
    request: RiskMonitoringRequest,
    background_tasks: BackgroundTasks,
    workflow_manager = Depends(get_workflow_manager)
):
    """启动风险监控"""
    try:
        if not workflow_manager:
            raise HTTPException(status_code=503, detail="工作流管理器不可用")
        
        logger.info(f"启动风险监控: {len(request.stock_codes)}只股票")
        
        # 启动风险监控工作流
        workflow_input = {
            "stock_codes": request.stock_codes,
            "monitoring_type": request.monitoring_type,
            "alert_threshold": request.alert_threshold
        }
        
        # 在后台执行监控工作流
        background_tasks.add_task(
            workflow_manager.execute_workflow,
            "risk_monitoring",
            workflow_input
        )
        
        return ApiResponse(
            message=f"风险监控已启动: {len(request.stock_codes)}只股票",
            data={
                "monitoring_stocks": request.stock_codes,
                "monitoring_type": request.monitoring_type,
                "alert_threshold": request.alert_threshold,
                "start_time": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"启动风险监控失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.get("/monitoring/metrics", response_model=ApiResponse)
async def get_risk_metrics():
    """获取风险指标"""
    try:
        # 这里应该从风险数据库获取实时风险指标
        # 暂时返回示例数据
        metrics = {
            "market_risk_level": "medium",
            "portfolio_var": 0.05,
            "max_drawdown": 0.12,
            "volatility": 0.18,
            "active_alerts": 3,
            "monitoring_stocks": 150,
            "last_update": datetime.now().isoformat()
        }
        
        return ApiResponse(
            message="风险指标获取成功",
            data=metrics
        )
        
    except Exception as e:
        logger.error(f"获取风险指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 风险预警API ====================

@tianji_star_router.get("/alerts/list", response_model=ApiResponse)
async def list_risk_alerts(
    severity: Optional[str] = "all",
    limit: int = 50,
    knowledge_service = Depends(get_risk_knowledge_base_service)
):
    """获取风险预警列表"""
    try:
        if not knowledge_service:
            # 从数据库直接获取
            alerts = await _get_alerts_from_database(severity, limit)
        else:
            alerts = await knowledge_service.get_risk_alerts(severity, limit)
        
        return ApiResponse(
            message=f"获取到{len(alerts)}条风险预警",
            data={
                "alerts": alerts,
                "total": len(alerts),
                "severity_filter": severity
            }
        )
        
    except Exception as e:
        logger.error(f"获取风险预警失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def _get_alerts_from_database(severity: str, limit: int) -> List[Dict]:
    """从数据库获取预警信息"""
    try:
        import sqlite3
        conn = sqlite3.connect("backend/data/risk_database.db")
        cursor = conn.cursor()
        
        if severity == "all":
            cursor.execute("""
                SELECT id, risk_type, risk_level, description, affected_symbols, 
                       risk_score, detection_time, is_active
                FROM risk_items 
                WHERE is_active = 1
                ORDER BY risk_score DESC
                LIMIT ?
            """, (limit,))
        else:
            cursor.execute("""
                SELECT id, risk_type, risk_level, description, affected_symbols, 
                       risk_score, detection_time, is_active
                FROM risk_items 
                WHERE is_active = 1 AND risk_level = ?
                ORDER BY risk_score DESC
                LIMIT ?
            """, (severity.upper(), limit))
        
        alerts = []
        for row in cursor.fetchall():
            alerts.append({
                "id": row[0],
                "risk_type": row[1],
                "risk_level": row[2],
                "description": row[3],
                "affected_symbols": row[4],
                "risk_score": row[5],
                "detection_time": row[6],
                "is_active": bool(row[7])
            })
        
        conn.close()
        return alerts
        
    except Exception as e:
        logger.error(f"从数据库获取预警失败: {e}")
        return []

# ==================== 风险模型API ====================

@tianji_star_router.post("/risk/model/build", response_model=ApiResponse)
async def build_risk_model(
    config: Dict[str, Any],
    workflow_manager = Depends(get_workflow_manager)
):
    """构建风险模型"""
    try:
        if not workflow_manager:
            raise HTTPException(status_code=503, detail="工作流管理器不可用")

        logger.info(f"开始构建风险模型: {config.get('model_type', 'unknown')}")

        # 执行风险模型构建工作流
        result = await workflow_manager.execute_workflow("risk_model_building", config)

        return ApiResponse(
            message="风险模型构建完成",
            data=result
        )

    except Exception as e:
        logger.error(f"风险模型构建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.get("/risk/model/list", response_model=ApiResponse)
async def list_risk_models():
    """获取风险模型列表"""
    try:
        # 这里应该从数据库获取已构建的风险模型
        models = [
            {
                "model_id": "market_risk_v1",
                "model_type": "market_risk",
                "model_name": "市场风险模型V1",
                "accuracy": 0.85,
                "created_time": "2025-06-19T10:00:00",
                "status": "active"
            },
            {
                "model_id": "portfolio_risk_v1",
                "model_type": "portfolio_risk",
                "model_name": "投资组合风险模型V1",
                "accuracy": 0.82,
                "created_time": "2025-06-19T09:00:00",
                "status": "active"
            }
        ]

        return ApiResponse(
            message=f"获取到{len(models)}个风险模型",
            data={"models": models, "total": len(models)}
        )

    except Exception as e:
        logger.error(f"获取风险模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 工作流管理API ====================

@tianji_star_router.get("/workflow/list", response_model=ApiResponse)
async def list_workflows(workflow_manager = Depends(get_workflow_manager)):
    """获取工作流列表"""
    try:
        if not workflow_manager:
            raise HTTPException(status_code=503, detail="工作流管理器不可用")

        active_workflows = workflow_manager.list_active_workflows()
        statistics = workflow_manager.get_workflow_statistics()

        return ApiResponse(
            message="工作流列表获取成功",
            data={
                "active_workflows": active_workflows,
                "statistics": statistics,
                "available_types": statistics.get("workflow_types", [])
            }
        )

    except Exception as e:
        logger.error(f"获取工作流列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.post("/workflow/execute", response_model=ApiResponse)
async def execute_workflow(
    workflow_type: str,
    input_data: Dict[str, Any],
    workflow_manager = Depends(get_workflow_manager)
):
    """执行工作流"""
    try:
        if not workflow_manager:
            raise HTTPException(status_code=503, detail="工作流管理器不可用")

        logger.info(f"执行工作流: {workflow_type}")

        result = await workflow_manager.execute_workflow(workflow_type, input_data)

        return ApiResponse(
            message=f"工作流执行完成: {workflow_type}",
            data=result
        )

    except Exception as e:
        logger.error(f"工作流执行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.get("/workflow/status/{workflow_id}", response_model=ApiResponse)
async def get_workflow_status(
    workflow_id: str,
    workflow_manager = Depends(get_workflow_manager)
):
    """获取工作流状态"""
    try:
        if not workflow_manager:
            raise HTTPException(status_code=503, detail="工作流管理器不可用")

        status = workflow_manager.get_workflow_status(workflow_id)

        if not status:
            raise HTTPException(status_code=404, detail=f"工作流不存在: {workflow_id}")

        return ApiResponse(
            message="工作流状态获取成功",
            data=status
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工作流状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ==================== 知识库管理API ====================

@tianji_star_router.post("/knowledge/add_risk", response_model=ApiResponse)
async def add_risk_to_knowledge_base(
    risk_data: Dict[str, Any],
    knowledge_service = Depends(get_risk_knowledge_base_service)
):
    """添加风险到知识库"""
    try:
        if not knowledge_service:
            raise HTTPException(status_code=503, detail="风险知识库服务不可用")

        risk_id = await knowledge_service.add_enhanced_risk_analysis(risk_data)

        return ApiResponse(
            message="风险已添加到知识库",
            data={"risk_id": risk_id}
        )

    except Exception as e:
        logger.error(f"添加风险到知识库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianji_star_router.get("/knowledge/stock_risks/{stock_code}", response_model=ApiResponse)
async def get_stock_risks_from_knowledge_base(
    stock_code: str,
    risk_levels: Optional[str] = None,
    knowledge_service = Depends(get_risk_knowledge_base_service)
):
    """从知识库获取股票风险"""
    try:
        if not knowledge_service:
            # 直接从数据库获取
            risks = await _get_stock_risks_from_database(stock_code, risk_levels)
        else:
            risk_level_list = None
            if risk_levels:
                from backend.roles.tianji_star.models.risk_models import RiskLevel
                risk_level_list = []
                for level in risk_levels.split(","):
                    level = level.strip().upper()
                    if hasattr(RiskLevel, level):
                        risk_level_list.append(getattr(RiskLevel, level))

            risks = await knowledge_service.get_enhanced_stock_risks(stock_code, risk_level_list)

        return ApiResponse(
            message=f"获取股票风险成功: {stock_code}",
            data={
                "stock_code": stock_code,
                "risks": [risk.__dict__ if hasattr(risk, '__dict__') else risk for risk in risks],
                "total": len(risks)
            }
        )

    except Exception as e:
        logger.error(f"获取股票风险失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def _get_stock_risks_from_database(stock_code: str, risk_levels: str = None) -> List[Dict]:
    """从数据库直接获取股票风险"""
    try:
        import sqlite3
        conn = sqlite3.connect("backend/data/risk_database.db")
        cursor = conn.cursor()

        if risk_levels:
            levels = [level.strip().upper() for level in risk_levels.split(",")]
            placeholders = ",".join(["?" for _ in levels])
            cursor.execute(f"""
                SELECT id, risk_type, risk_level, description, risk_score,
                       detection_time, mitigation_suggestions
                FROM risk_items
                WHERE affected_symbols LIKE ? AND risk_level IN ({placeholders})
                ORDER BY risk_score DESC
            """, [f"%{stock_code}%"] + levels)
        else:
            cursor.execute("""
                SELECT id, risk_type, risk_level, description, risk_score,
                       detection_time, mitigation_suggestions
                FROM risk_items
                WHERE affected_symbols LIKE ?
                ORDER BY risk_score DESC
            """, (f"%{stock_code}%",))

        risks = []
        for row in cursor.fetchall():
            risks.append({
                "risk_id": row[0],
                "risk_type": row[1],
                "risk_level": row[2],
                "description": row[3],
                "risk_score": row[4],
                "detection_time": row[5],
                "mitigation_suggestions": row[6]
            })

        conn.close()
        return risks

    except Exception as e:
        logger.error(f"从数据库获取股票风险失败: {e}")
        return []

# ==================== API客户端类 ====================

class TianjiStarApi:
    """天玑星API客户端类"""

    @staticmethod
    async def healthCheck():
        """健康检查"""
        return await health_check()

    @staticmethod
    async def getStatus():
        """获取状态"""
        return await get_status()

    @staticmethod
    async def assessStockRisk(request_data: Dict[str, Any]):
        """评估股票风险"""
        request = RiskAssessmentRequest(**request_data)
        risk_service = get_risk_assessment_service()
        return await assess_stock_risk(request, risk_service)

    @staticmethod
    async def assessPortfolioRisk(request_data: Dict[str, Any]):
        """评估投资组合风险"""
        request = PortfolioRiskRequest(**request_data)
        risk_service = get_risk_assessment_service()
        return await assess_portfolio_risk(request, risk_service)

    @staticmethod
    async def startRiskMonitoring(request_data: Dict[str, Any]):
        """启动风险监控"""
        from fastapi import BackgroundTasks
        request = RiskMonitoringRequest(**request_data)
        background_tasks = BackgroundTasks()
        workflow_manager = get_workflow_manager()
        return await start_risk_monitoring(request, background_tasks, workflow_manager)

    @staticmethod
    async def getRiskMetrics():
        """获取风险指标"""
        return await get_risk_metrics()

    @staticmethod
    async def getRiskAlerts(severity: str = "all", limit: int = 50):
        """获取风险预警"""
        knowledge_service = get_risk_knowledge_base_service()
        return await list_risk_alerts(severity, limit, knowledge_service)

    @staticmethod
    async def getWorkflowList():
        """获取工作流列表"""
        workflow_manager = get_workflow_manager()
        return await list_workflows(workflow_manager)

    @staticmethod
    async def executeWorkflow(workflow_data: Dict[str, Any]):
        """执行工作流"""
        workflow_type = workflow_data.get("workflow_type")
        input_data = workflow_data.get("input_data", {})
        workflow_manager = get_workflow_manager()
        return await execute_workflow(workflow_type, input_data, workflow_manager)

    @staticmethod
    async def getRiskDashboardData():
        """获取风险仪表板数据"""
        try:
            status = await TianjiStarApi.getStatus()
            metrics = await TianjiStarApi.getRiskMetrics()
            alerts = await TianjiStarApi.getRiskAlerts("all", 5)
            workflows = await TianjiStarApi.getWorkflowList()

            return ApiResponse(
                message="风险仪表板数据获取成功",
                data={
                    "service_status": status.data,
                    "risk_metrics": metrics.data,
                    "recent_alerts": alerts.data,
                    "workflow_status": workflows.data
                }
            )
        except Exception as e:
            logger.error(f"获取风险仪表板数据失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def executePortfolioAnalysis(portfolio: List[Dict[str, Any]]):
        """执行投资组合分析"""
        return await TianjiStarApi.executeWorkflow({
            "workflow_type": "portfolio_analysis",
            "input_data": {"portfolio": portfolio}
        })

    @staticmethod
    async def executeStressTest(portfolio: List[Dict[str, Any]], scenarios: List[str] = None):
        """执行压力测试"""
        if scenarios is None:
            scenarios = ["market_crash"]

        return await TianjiStarApi.executeWorkflow({
            "workflow_type": "stress_test",
            "input_data": {"portfolio": portfolio, "scenarios": scenarios}
        })

    @staticmethod
    async def getRealTimeRiskData():
        """获取实时风险数据"""
        try:
            metrics = await TianjiStarApi.getRiskMetrics()
            alerts = await TianjiStarApi.getRiskAlerts("all", 10)
            status = await TianjiStarApi.getStatus()

            return ApiResponse(
                message="实时风险数据获取成功",
                data={
                    "metrics": metrics.data,
                    "alerts": alerts.data,
                    "status": status.data
                }
            )
        except Exception as e:
            logger.error(f"获取实时风险数据失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def send_message_to_tianquan(message_data: Dict[str, Any]) -> Dict[str, Any]:
        """向天权星发送消息"""
        try:
            message_id = f"tianji_to_tianquan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 构造消息
            message = {
                "message_id": message_id,
                "from": "tianji_star",
                "to": "tianquan_star",
                "message_type": message_data.get("message_type", "risk_report"),
                "content": message_data.get("content", {}),
                "priority": message_data.get("priority", "NORMAL"),
                "requires_action": message_data.get("requires_action", False),
                "timestamp": datetime.now().isoformat()
            }

            # 记录到传奇记忆系统
            try:
                from core.memory_integration_service import memory_integration_service
                await memory_integration_service.add_tianji_memory(
                    f"向天权星发送消息: {message['message_type']} - {message_id}",
                    "decision"
                )
            except Exception as e:
                logger.warning(f"记录消息到记忆系统失败: {e}")

            logger.info(f"[TIANJI->TIANQUAN] 消息发送: {message_id}")

            return {
                "success": True,
                "message_id": message_id,
                "message": "消息已发送给天权星",
                "delivery_status": "delivered",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"向天权星发送消息失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @staticmethod
    async def send_message_to_yuheng(message_data: Dict[str, Any]) -> Dict[str, Any]:
        """向玉衡星发送消息"""
        try:
            instruction_id = f"tianji_to_yuheng_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 构造指令消息
            instruction = {
                "instruction_id": instruction_id,
                "from": "tianji_star",
                "to": "yuheng_star",
                "message_type": message_data.get("message_type", "risk_control_instruction"),
                "content": message_data.get("content", {}),
                "priority": message_data.get("priority", "HIGH"),
                "requires_immediate_action": message_data.get("requires_immediate_action", True),
                "timestamp": datetime.now().isoformat()
            }

            # 记录到传奇记忆系统
            try:
                from core.memory_integration_service import memory_integration_service
                await memory_integration_service.add_tianji_memory(
                    f"向玉衡星发送指令: {instruction['message_type']} - {instruction_id}",
                    "execution"
                )
            except Exception as e:
                logger.warning(f"记录指令到记忆系统失败: {e}")

            logger.info(f"[TIANJI->YUHENG] 指令发送: {instruction_id}")

            return {
                "success": True,
                "instruction_id": instruction_id,
                "message": "指令已发送给玉衡星",
                "delivery_status": "delivered",
                "execution_priority": instruction["priority"],
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"向玉衡星发送指令失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @staticmethod
    async def assess_investment_risk(investment_request: Dict[str, Any]) -> Dict[str, Any]:
        """评估投资风险"""
        try:
            stock_code = investment_request.get("stock_code", "")
            target_amount = investment_request.get("target_amount", 0)
            current_portfolio = investment_request.get("current_portfolio", {})

            # 计算风险指标
            portfolio_value = current_portfolio.get("total_value", 0)
            position_ratio = target_amount / portfolio_value if portfolio_value > 0 else 0

            # 风险评估逻辑
            risk_score = 0.0
            risk_factors = []

            # 仓位集中度风险
            if position_ratio > 0.3:
                risk_score += 0.4
                risk_factors.append("仓位集中度过高")
            elif position_ratio > 0.2:
                risk_score += 0.2
                risk_factors.append("仓位集中度较高")

            # 市场风险（模拟）
            market_risk = 0.15  # 假设当前市场风险
            risk_score += market_risk
            risk_factors.append(f"市场系统性风险: {market_risk:.2f}")

            # 流动性风险
            if target_amount > 5000000:  # 大额投资
                risk_score += 0.1
                risk_factors.append("大额投资流动性风险")

            # 风险评级
            if risk_score <= 0.3:
                risk_level = "LOW"
                approved = True
            elif risk_score <= 0.6:
                risk_level = "MEDIUM"
                approved = True
            elif risk_score <= 0.8:
                risk_level = "HIGH"
                approved = False
            else:
                risk_level = "CRITICAL"
                approved = False

            # 生成建议
            recommendations = []
            if not approved:
                recommendations.append("建议拒绝此投资")
                recommendations.append("风险过高，超出可接受范围")
            else:
                if position_ratio > 0.2:
                    recommendations.append("建议分批建仓")
                recommendations.append("设置适当的止损位")
                recommendations.append("密切监控市场变化")

            return {
                "approved": approved,
                "risk_score": risk_score,
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "recommendations": recommendations,
                "position_ratio": position_ratio,
                "execution_parameters": {
                    "max_single_order": min(target_amount * 0.3, 1000000),
                    "execution_timeframe": "1-3 days",
                    "price_limit": "market_price + 2%"
                } if approved else {},
                "risk_controls": {
                    "stop_loss": "entry_price - 8%",
                    "position_limit": min(position_ratio, 0.15),
                    "monitoring_frequency": "real_time"
                } if approved else {},
                "assessment_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"投资风险评估失败: {e}")
            return {
                "approved": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @staticmethod
    async def assess_risk(test_data: Dict[str, Any]) -> Dict[str, Any]:
        """基础风险评估"""
        try:
            stock_code = test_data.get("stock_code", "")
            position_size = test_data.get("position_size", 0)
            market_data = test_data.get("market_data", {})

            # 计算风险评分
            volatility = market_data.get("volatility", 0.2)
            beta = market_data.get("beta", 1.0)

            # 基础风险评分
            risk_score = volatility * 0.4 + abs(beta - 1.0) * 0.3 + 0.2

            return {
                "risk_score": min(risk_score, 1.0),
                "volatility": volatility,
                "beta": beta,
                "assessment_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"基础风险评估失败: {e}")
            return {"error": str(e)}

    @staticmethod
    async def assess_portfolio_risk(portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """投资组合风险评估"""
        try:
            positions = portfolio_data.get("positions", [])
            total_value = portfolio_data.get("total_value", 0)

            # 计算组合风险
            portfolio_beta = sum(pos.get("weight", 0) * pos.get("beta", 1.0) for pos in positions)
            concentration_risk = max(pos.get("weight", 0) for pos in positions) if positions else 0

            portfolio_risk_score = portfolio_beta * 0.5 + concentration_risk * 0.5

            return {
                "portfolio_risk_score": min(portfolio_risk_score, 1.0),
                "portfolio_beta": portfolio_beta,
                "concentration_risk": concentration_risk,
                "assessment_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"投资组合风险评估失败: {e}")
            return {"error": str(e)}

    @staticmethod
    async def run_stress_test(stress_data: Dict[str, Any]) -> Dict[str, Any]:
        """运行压力测试"""
        try:
            portfolio = stress_data.get("portfolio", {})
            scenarios = stress_data.get("scenarios", {})

            stress_results = {}

            for scenario_name, scenario_params in scenarios.items():
                # 模拟压力测试计算
                market_drop = scenario_params.get("market_drop", -0.1)
                volatility_spike = scenario_params.get("volatility_spike", 1.5)

                # 计算潜在损失
                portfolio_value = portfolio.get("total_value", 0)
                potential_loss = portfolio_value * abs(market_drop) * volatility_spike

                stress_results[scenario_name] = {
                    "potential_loss": potential_loss,
                    "loss_percentage": abs(market_drop) * volatility_spike,
                    "scenario_params": scenario_params
                }

            return {
                "stress_test_results": stress_results,
                "test_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"压力测试失败: {e}")
            return {"error": str(e)}

    @staticmethod
    async def monitor_real_time_risk(monitoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """实时风险监控"""
        try:
            portfolio_id = monitoring_data.get("portfolio_id", "")
            monitoring_interval = monitoring_data.get("monitoring_interval", 60)

            return {
                "monitoring_status": "active",
                "portfolio_id": portfolio_id,
                "monitoring_interval": monitoring_interval,
                "start_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"实时风险监控失败: {e}")
            return {"error": str(e)}

    @staticmethod
    async def check_risk_alerts(alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """检查风险预警"""
        try:
            current_metrics = alert_data.get("current_metrics", {})

            alerts = []

            # 检查VaR超限
            var_95 = current_metrics.get("var_95", 0)
            if var_95 > 0.05:
                alerts.append({
                    "type": "VAR_EXCEEDED",
                    "message": f"VaR 95%超限: {var_95:.3f}",
                    "severity": "HIGH"
                })

            # 检查集中度风险
            concentration_risk = current_metrics.get("concentration_risk", 0)
            if concentration_risk > 0.30:
                alerts.append({
                    "type": "CONCENTRATION_RISK",
                    "message": f"集中度风险过高: {concentration_risk:.3f}",
                    "severity": "MEDIUM"
                })

            return {
                "alerts": alerts,
                "alert_count": len(alerts),
                "check_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"风险预警检查失败: {e}")
            return {"error": str(e)}

    @staticmethod
    async def health_check() -> Dict[str, Any]:
        """健康检查"""
        try:
            return {
                "status": "healthy",
                "service": "tianji_star",
                "timestamp": datetime.now().isoformat(),
                "version": "2.0"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @staticmethod
    async def get_system_status() -> Dict[str, Any]:
        """获取系统状态"""
        try:
            return {
                "service_name": "tianji_star",
                "status": "running",
                "uptime": "24h",
                "memory_usage": "45%",
                "cpu_usage": "12%",
                "active_workflows": 3,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {"error": str(e)}
