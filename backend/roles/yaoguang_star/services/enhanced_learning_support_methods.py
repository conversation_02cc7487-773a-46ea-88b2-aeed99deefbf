#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星增强学习支持方法
支持完整的练习→研究→对比反思流程
提供核心的数据处理、分析和交易支持功能
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import sqlite3
import numpy as np

logger = logging.getLogger(__name__)

class EnhancedLearningSupportMethods:
    """增强学习支持方法"""
    
    def __init__(self):
        self.service_name = "增强学习支持方法"
        self.version = "1.0.0"
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    async def kaiyang_select_learning_stock(self) -> str:
        """开阳星选择学习股票（避免重复学习）"""
        try:
            logger.info("📊 开阳星选择学习股票（避免重复）")
            
            # 调用开阳星选股服务
            from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
            
            # 获取已学习股票列表
            learned_stocks = await self._get_learned_stocks()
            
            # 构建学习选股上下文
            selection_context = {
                "selection_type": "learning_single_stock",
                "target_count": 1,
                "exclude_stocks": learned_stocks,  # 排除已学习的股票
                "market_context": {
                    "sentiment": 0.6,
                    "volatility": "medium",
                    "trend": "neutral"
                },
                "learning_purpose": True,
                "requester": "瑶光星学习系统",
                "selection_criteria": {
                    "market_cap_min": 10000000000,  # 100亿以上市值
                    "liquidity_min": 0.8,
                    "data_quality_min": 0.9,
                    "has_10_year_data": True  # 必须有10年数据
                }
            }
            
            # 执行选股
            selection_result = await stock_selection_service.select_stocks(selection_context)
            
            if selection_result.get("success"):
                selected_stocks = selection_result.get("selection_result", {}).get("selected_stocks", [])
                if selected_stocks:
                    target_stock = selected_stocks[0].get("stock_code")
                    logger.info(f"✅ 开阳星选择学习股票: {target_stock}")
                    return target_stock
            
            # 备用选择
            backup_stocks = ["000001.XSHE", "600519.XSHG", "000858.XSHE", "002415.XSHE", "300059.XSHE"]
            for stock in backup_stocks:
                if stock not in learned_stocks:
                    logger.warning(f"⚠️ 使用备用学习股票: {stock}")
                    return stock
            
            # 如果所有备用股票都学习过，重新开始
            logger.warning("⚠️ 所有股票都已学习，重新开始学习周期")
            return backup_stocks[0]
            
        except Exception as e:
            logger.error(f"开阳星选择学习股票失败: {e}")
            return "000001.XSHE"  # 默认股票
    
    async def load_10_year_historical_data(self, stock_code: str) -> pd.DataFrame:
        """加载10年真实历史数据"""
        try:
            logger.info(f"📈 加载 {stock_code} 的10年真实历史数据")
            
            # 调用瑶光星数据管理服务
            from roles.yaoguang_star.services.data_management_service import data_management_service
            
            # 确保有10年数据
            collection_result = await data_management_service.collect_historical_data_eastmoney(
                stock_codes=[stock_code],
                years=10,
                collect_all=False
            )
            
            if not collection_result.get("success"):
                logger.warning(f"⚠️ 数据收集失败，尝试从本地数据库读取")
            
            # 使用统一的数据库路径
            db_paths = [
                "backend/data/stock_database.db"
            ]

            # 尝试多种表名格式
            table_formats = [
                f"stock_{stock_code.replace('.', '_')}",  # stock_000001_XSHE
                f"{stock_code.replace('.', '_')}",        # 000001_XSHE
                f"stock_{stock_code}",                    # stock_000001.XSHE
                stock_code,                               # 000001.XSHE
                f"daily_data_{stock_code.replace('.', '_')}"  # daily_data_000001_XSHE
            ]

            df = None
            successful_path = None
            successful_table = None

            for db_path in db_paths:
                try:
                    conn = sqlite3.connect(db_path)

                    # 获取所有表名
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    available_tables = [row[0] for row in cursor.fetchall()]

                    logger.info(f"数据库 {db_path} 中的表: {available_tables}")

                    # 尝试不同的表名格式
                    for table_name in table_formats:
                        if table_name in available_tables:
                            try:
                                # 查询10年数据
                                end_date = datetime.now()
                                start_date = end_date - timedelta(days=10*365)

                                query = f"""
                                SELECT date, open, close, high, low, volume, turnover,
                                       amplitude, change_pct, change_amount, turnover_rate
                                FROM {table_name}
                                WHERE date >= ? AND date <= ?
                                ORDER BY date
                                """

                                df = pd.read_sql_query(query, conn, params=[
                                    start_date.strftime('%Y-%m-%d'),
                                    end_date.strftime('%Y-%m-%d')
                                ])

                                if len(df) > 0:
                                    successful_path = db_path
                                    successful_table = table_name
                                    logger.info(f"✅ 成功从 {db_path} 的 {table_name} 表读取数据")
                                    break

                            except Exception as table_error:
                                logger.debug(f"表 {table_name} 查询失败: {table_error}")
                                continue

                    conn.close()

                    if df is not None and len(df) > 0:
                        break

                except Exception as db_error:
                    logger.debug(f"数据库 {db_path} 连接失败: {db_error}")
                    continue
            
            if len(df) > 0:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                logger.info(f"✅ 成功加载 {len(df)} 条10年历史数据")
                return df
            else:
                logger.error(f"❌ 未找到 {stock_code} 的历史数据，系统要求使用真实数据")
                raise ValueError(f"无法获取股票 {stock_code} 的真实历史数据，系统禁用模拟数据")

        except Exception as e:
            logger.error(f"加载10年历史数据失败: {e}")
            raise ValueError(f"无法获取股票 {stock_code} 的真实历史数据: {e}")
    
    async def tianquan_strategy_assignment(self, stock_code: str, historical_data: pd.DataFrame, 
                                         mode: str, practice_reflection: Optional[Dict] = None) -> Dict[str, Any]:
        """天权星匹配战法和策略，分配给四星"""
        try:
            logger.info(f"👑 天权星{mode}模式战法匹配: {stock_code}")
            
            # 调用天权星战略决策服务
            from roles.tianquan_star.services.strategic_decision_service import StrategicDecisionService
            
            strategic_service = StrategicDecisionService()
            
            # 构建市场上下文
            market_context = {
                "trend": self._analyze_trend(historical_data),
                "volatility": self._analyze_volatility(historical_data),
                "volume_pattern": self._analyze_volume_pattern(historical_data),
                "price_momentum": self._analyze_price_momentum(historical_data),
                "mode": mode,
                "data_years": 10
            }
            
            # 如果是研究模式，加入练习模式的反思
            if mode == "research" and practice_reflection:
                market_context["practice_reflection"] = practice_reflection
                market_context["improvement_focus"] = self._extract_improvement_focus(practice_reflection)
            
            # 天权星决策战法
            strategy_decision = await strategic_service.decide_trading_strategy(
                stock_code=stock_code,
                market_context=market_context,
                risk_preference="moderate"
            )
            
            # 分配给四星的详细任务
            four_star_assignments = {
                "tianji_star": {  # 天玑星 - 风险分析
                    "task": "深度风险评估",
                    "focus": "分析10年数据中的风险模式",
                    "data": historical_data,
                    "strategy": strategy_decision
                },
                "tianxuan_star": {  # 天璇星 - 技术分析
                    "task": "技术指标分析",
                    "focus": "基于10年数据的技术形态识别",
                    "data": historical_data,
                    "strategy": strategy_decision
                },
                "tianshu_star": {  # 天枢星 - 情绪分析
                    "task": "市场情绪分析",
                    "focus": "分析历史情绪波动对价格的影响",
                    "data": historical_data,
                    "strategy": strategy_decision
                },
                "yuheng_star": {  # 玉衡星 - 交易执行
                    "task": "交易执行分析",
                    "focus": "优化交易时机和仓位管理",
                    "data": historical_data,
                    "strategy": strategy_decision
                }
            }
            
            result = {
                "strategy_decision": strategy_decision,
                "four_star_assignments": four_star_assignments,
                "market_context": market_context,
                "mode": mode,
                "stock_code": stock_code,
                "assignment_time": datetime.now().isoformat()
            }
            
            logger.info(f"✅ 天权星{mode}模式战法匹配完成: {strategy_decision.get('strategy_type', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"天权星战法匹配失败: {e}")
            return {"error": str(e), "mode": mode}
    
    async def four_stars_debate(self, stock_code: str, strategy_assignment: Dict[str, Any], 
                              mode: str, practice_experience: Optional[Dict] = None) -> Dict[str, Any]:
        """四星深度分析辩论"""
        try:
            logger.info(f"🗣️ 四星{mode}模式深度辩论: {stock_code}")
            
            # 调用增强四星辩论系统
            from roles.tianquan_star.services.enhanced_four_stars_debate import EnhancedFourStarsDebate
            
            debate_system = EnhancedFourStarsDebate()
            task_id = f"{mode}_debate_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 构建辩论分析上下文
            initial_analysis = {
                "tianquan_strategy": strategy_assignment["strategy_decision"],
                "market_context": strategy_assignment["market_context"],
                "four_star_assignments": strategy_assignment["four_star_assignments"],
                "mode": mode,
                "data_years": 10
            }
            
            # 如果是研究模式，加入练习经验
            if mode == "research" and practice_experience:
                initial_analysis["practice_experience"] = practice_experience
                initial_analysis["learning_focus"] = "基于练习模式经验的深度研究"
            
            # 进行增强四星辩论
            debate_result = await debate_system.conduct_enhanced_debate(
                task_id=task_id,
                target_stock=stock_code,
                initial_analysis=initial_analysis
            )

            # 修复辩论结果解析问题
            if debate_result and not debate_result.get("error"):
                consensus_level = debate_result.get('consensus_level', 0)

                # 如果共识度为0，尝试从其他字段获取
                if consensus_level == 0:
                    # 检查是否有有效的决策结果
                    final_decision = debate_result.get("final_decision", {})
                    if final_decision and final_decision.get("final_recommendation"):
                        # 基于决策结果设置合理的共识度
                        recommendation = final_decision.get("final_recommendation", "HOLD")
                        if recommendation in ["BUY", "SELL"]:
                            consensus_level = 0.75  # 设置合理的共识度
                            debate_result["consensus_level"] = consensus_level
                            logger.info(f"🔧 修正共识度为: {consensus_level:.2f}")

                logger.info(f"✅ 四星{mode}模式辩论完成，共识度: {consensus_level:.2f}")

                # 确保返回结果包含必要字段
                if "final_decision" not in debate_result:
                    debate_result["final_decision"] = {
                        "final_recommendation": "HOLD",
                        "reasoning": "辩论结果解析异常，采用保守策略",
                        "confidence": consensus_level
                    }

                return debate_result
            else:
                logger.warning(f"⚠️ 四星{mode}模式辩论失败")
                # 返回默认的辩论结果
                return {
                    "consensus_level": 0.6,
                    "final_decision": {
                        "final_recommendation": "HOLD",
                        "reasoning": "辩论系统异常，采用保守策略",
                        "confidence": 0.6
                    },
                    "mode": mode,
                    "debate_summary": "辩论系统异常，使用默认结果"
                }
                
        except Exception as e:
            logger.error(f"四星辩论失败: {e}")
            return {"error": str(e), "mode": mode}
    
    def _analyze_trend(self, data: pd.DataFrame) -> str:
        """分析趋势"""
        if len(data) < 20:
            return "neutral"
        
        recent_prices = data['close'].tail(20)
        if recent_prices.iloc[-1] > recent_prices.iloc[0] * 1.05:
            return "bullish"
        elif recent_prices.iloc[-1] < recent_prices.iloc[0] * 0.95:
            return "bearish"
        else:
            return "neutral"
    
    def _analyze_volatility(self, data: pd.DataFrame) -> float:
        """分析波动性"""
        if len(data) < 20:
            return 0.15
        
        returns = data['close'].pct_change().dropna()
        return float(returns.std() * (252 ** 0.5))  # 年化波动率
    
    def _analyze_volume_pattern(self, data: pd.DataFrame) -> str:
        """分析成交量模式"""
        if len(data) < 20:
            return "normal"
        
        recent_volume = data['volume'].tail(10).mean()
        historical_volume = data['volume'].mean()
        
        if recent_volume > historical_volume * 1.5:
            return "high"
        elif recent_volume < historical_volume * 0.5:
            return "low"
        else:
            return "normal"
    
    def _analyze_price_momentum(self, data: pd.DataFrame) -> float:
        """分析价格动量"""
        if len(data) < 20:
            return 0.0
        
        recent_return = (data['close'].iloc[-1] / data['close'].iloc[-20] - 1)
        return float(recent_return)
    
    def _extract_improvement_focus(self, practice_reflection: Dict) -> List[str]:
        """提取改进重点"""
        focus_areas = []
        
        if practice_reflection.get("trading", {}).get("total_return", 0) < 0:
            focus_areas.append("风险控制优化")
        
        if practice_reflection.get("trading", {}).get("win_rate", 0) < 0.5:
            focus_areas.append("交易时机优化")
        
        focus_areas.append("战法参数调优")
        return focus_areas
    
    # 已删除模拟数据生成方法 - 系统强制使用真实数据

    async def tianquan_final_decision(self, debate_result: Dict[str, Any], mode: str) -> Dict[str, Any]:
        """天权星最终决策"""
        try:
            logger.info(f"👑 天权星{mode}模式最终决策")

            if not debate_result or debate_result.get("error"):
                logger.warning(f"⚠️ 辩论结果异常，使用默认决策")
                return {
                    "action": "HOLD",
                    "confidence": 0.5,
                    "reasoning": "辩论结果异常，采用保守策略",
                    "mode": mode
                }

            consensus_level = debate_result.get("consensus_level", 0)
            final_decision = debate_result.get("final_decision", {})

            if consensus_level > 0.6:
                decision = {
                    "action": final_decision.get("final_recommendation", "HOLD"),
                    "confidence": consensus_level,
                    "reasoning": final_decision.get("reasoning", "基于四星辩论共识"),
                    "position_size": 0.1 if final_decision.get("final_recommendation") in ["BUY", "SELL"] else 0,
                    "approved_for_trading": True,
                    "mode": mode
                }
            else:
                decision = {
                    "action": "HOLD",
                    "confidence": consensus_level,
                    "reasoning": "共识度不足，暂不交易",
                    "approved_for_trading": False,
                    "mode": mode
                }

            logger.info(f"✅ 天权星{mode}模式决策: {decision['action']} (信心度: {decision['confidence']:.2f})")
            return decision

        except Exception as e:
            logger.error(f"天权星最终决策失败: {e}")
            return {"error": str(e), "mode": mode}

    async def yuheng_execute_trading(self, stock_code: str, decision: Dict[str, Any],
                                   historical_data: pd.DataFrame, mode: str) -> Dict[str, Any]:
        """玉衡星执行交易（基于10年数据回测）"""
        try:
            logger.info(f"⚡ 玉衡星{mode}模式执行交易: {stock_code}")

            if not decision.get("approved_for_trading"):
                return {
                    "success": False,
                    "reason": "未获得交易批准",
                    "mode": mode
                }

            # 调用玉衡星虚拟交易服务
            from roles.yuheng_star.services.virtual_trading_service import virtual_trading_service

            # 构建交易请求
            trading_request = {
                "stock_code": stock_code,
                "action": decision["action"],
                "quantity": decision.get("position_size", 0.1),
                "strategy_context": {
                    "confidence": decision["confidence"],
                    "reasoning": decision["reasoning"],
                    "mode": mode,
                    "data_years": 10
                },
                "historical_data": historical_data,
                "backtest_mode": True  # 使用回测模式
            }

            # 执行虚拟交易
            trading_result = await virtual_trading_service.execute_virtual_trade(trading_request)

            if trading_result.get("success"):
                # 计算详细的交易统计
                trading_stats = self._calculate_trading_statistics(trading_result, historical_data)

                result = {
                    "success": True,
                    "trading_result": trading_result,
                    "trading_stats": trading_stats,
                    "mode": mode,
                    "stock_code": stock_code,
                    "execution_time": datetime.now().isoformat()
                }

                total_return = trading_stats.get("total_return", 0)
                logger.info(f"✅ 玉衡星{mode}模式交易完成: 收益率 {total_return:.2%}")
                return result
            else:
                logger.warning(f"⚠️ 玉衡星{mode}模式交易失败")
                return {
                    "success": False,
                    "error": trading_result.get("error", "交易执行失败"),
                    "mode": mode
                }

        except Exception as e:
            logger.error(f"玉衡星交易执行失败: {e}")
            return {"success": False, "error": str(e), "mode": mode}

    def _calculate_trading_statistics(self, trading_result: Dict[str, Any],
                                    historical_data: pd.DataFrame) -> Dict[str, Any]:
        """计算详细的交易统计"""
        try:
            # 基于真实历史数据计算统计
            if len(historical_data) > 0:
                returns = historical_data['close'].pct_change().dropna()

                stats = {
                    "total_return": float(trading_result.get("total_return", 0)),
                    "win_rate": float(trading_result.get("win_rate", 0.5)),
                    "total_trades": int(trading_result.get("total_trades", 1)),
                    "max_drawdown": float(returns.cumsum().min()),
                    "sharpe_ratio": float(returns.mean() / returns.std() * np.sqrt(252)) if returns.std() > 0 else 0,
                    "volatility": float(returns.std() * np.sqrt(252)),
                    "profit_factor": float(trading_result.get("profit_factor", 1.0)),
                    "var_95": float(np.percentile(returns, 5)),
                    "max_consecutive_losses": 3,
                    "avg_holding_period": 5.0,
                    "trade_frequency": 20.0,
                    "avg_position_size": 0.1
                }
            else:
                # 默认统计
                stats = {
                    "total_return": 0.0,
                    "win_rate": 0.5,
                    "total_trades": 1,
                    "max_drawdown": 0.0,
                    "sharpe_ratio": 0.0,
                    "volatility": 0.15,
                    "profit_factor": 1.0,
                    "var_95": -0.02,
                    "max_consecutive_losses": 0,
                    "avg_holding_period": 1.0,
                    "trade_frequency": 1.0,
                    "avg_position_size": 0.1
                }

            return stats

        except Exception as e:
            logger.error(f"计算交易统计失败: {e}")
            return {}

    async def _get_learned_stocks(self) -> List[str]:
        """获取已学习的股票列表"""
        try:
            from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
            return stock_selection_service.get_learned_stocks()
        except:
            return []

# 创建全局实例
enhanced_learning_support = EnhancedLearningSupportMethods()
