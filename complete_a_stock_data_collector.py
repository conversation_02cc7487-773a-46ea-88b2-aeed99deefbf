#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整A股数据收集系统
结合天枢星crawl4ai爬虫，收集5000+只A股10年全字段数据
"""

import sys
sys.path.append('backend')

import asyncio
import requests
import sqlite3
import time
import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from urllib.parse import quote

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteAStockDataCollector:
    """完整A股数据收集系统"""
    
    def __init__(self):
        self.db_path = "backend/data/stock_database.db"
        self.collected_count = 0
        self.failed_count = 0
        self.total_records = 0
        
        # 请求配置
        self.request_interval = 12  # 12秒间隔
        self.batch_size = 20  # 每批20只股票
        self.max_retries = 3
        
        # 东方财富API配置（按您提供的规格）
        self.eastmoney_base_url = "https://push2.eastmoney.com/api/qt/ulist/get"
        self.eastmoney_fields = "f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f350,f351"
        
        # 历史数据API
        self.history_base_url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        
        # 字段映射
        self.field_mapping = {
            'f2': 'current_price',      # 当前最新价格
            'f3': 'change_percent',     # 涨跌幅度
            'f4': 'change_amount',      # 涨跌额
            'f5': 'volume',             # 成交量
            'f6': 'amount',             # 成交额
            'f7': 'amplitude',          # 振幅
            'f8': 'turnover_rate',      # 换手率
            'f9': 'pe_ratio',           # 市盈率
            'f10': 'pb_ratio',          # 市净率
            'f12': 'stock_code',        # 股票代码
            'f13': 'market',            # 市场
            'f14': 'stock_name',        # 股票名称
            'f15': 'high_price',        # 最高
            'f16': 'low_price',         # 最低
            'f17': 'open_price',        # 今开
            'f18': 'pre_close_price',   # 昨收
            'f20': 'total_market_cap',  # 总市值
            'f21': 'float_market_cap',  # 流通市值
            'f350': 'limit_up_price',   # 涨停价
            'f351': 'limit_down_price'  # 跌停价
        }
        
        logger.info("🌟 完整A股数据收集系统初始化完成")
    
    def get_all_a_stocks(self) -> List[Dict[str, Any]]:
        """获取全部A股股票列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有A股股票（修复SQLite兼容性）
            cursor.execute("""
                SELECT DISTINCT stock_code, stock_name
                FROM stock_info
                WHERE LENGTH(stock_code) >= 6
                AND (stock_code LIKE '0%' OR stock_code LIKE '3%' OR stock_code LIKE '6%')
                ORDER BY stock_code
            """)
            
            stocks = cursor.fetchall()
            conn.close()
            
            stock_list = []
            for stock_code, stock_name in stocks:
                # 转换为东方财富secid格式
                clean_code = stock_code[:6]
                if clean_code.startswith('6'):
                    secid = f"1.{clean_code}"  # 上海交易所
                elif clean_code.startswith(('0', '3')):
                    secid = f"0.{clean_code}"  # 深圳交易所
                else:
                    continue
                
                stock_list.append({
                    "code": clean_code,
                    "name": stock_name or f"股票{clean_code}",
                    "secid": secid,
                    "full_code": stock_code
                })
            
            logger.info(f"✅ 获取到 {len(stock_list)} 只A股股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"❌ 获取A股股票列表失败: {e}")
            return []
    
    async def collect_realtime_data_batch(self, stocks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量收集实时数据"""
        logger.info(f"📊 开始批量收集 {len(stocks)} 只股票的实时数据...")
        
        # 分批处理
        total_batches = (len(stocks) + self.batch_size - 1) // self.batch_size
        collected_data = {}
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * self.batch_size
            end_idx = min((batch_idx + 1) * self.batch_size, len(stocks))
            batch_stocks = stocks[start_idx:end_idx]
            
            logger.info(f"📈 处理第 {batch_idx + 1}/{total_batches} 批，股票 {start_idx + 1}-{end_idx}")
            
            # 构建secids参数
            secids = ",".join([stock["secid"] for stock in batch_stocks])
            
            # 构建完整的请求参数
            params = {
                'fltt': '1',
                'invt': '2',
                'fields': self.eastmoney_fields,
                'secids': secids,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'pn': '1',
                'np': '1',
                'pz': str(len(batch_stocks)),
                'dect': '1',
                'wbp2u': '|0|0|0|web'
            }
            
            success = await self._request_batch_data(params, batch_stocks, collected_data)
            
            if not success:
                logger.warning(f"   ⚠️ 批次 {batch_idx + 1} 处理失败")
                self.failed_count += 1
            
            # 请求间隔
            if batch_idx < total_batches - 1:
                wait_time = self.request_interval + random.randint(2, 5)
                logger.info(f"   ⏰ 等待 {wait_time} 秒...")
                await asyncio.sleep(wait_time)
        
        logger.info(f"📊 实时数据收集完成: 成功 {self.collected_count}, 失败 {self.failed_count}")
        return collected_data
    
    async def _request_batch_data(self, params: Dict[str, str], batch_stocks: List[Dict[str, Any]], 
                                collected_data: Dict[str, Any]) -> bool:
        """请求批次数据"""
        for attempt in range(self.max_retries):
            try:
                # 使用天枢星的crawl4ai进行请求（如果可用）
                try:
                    response = await self._crawl4ai_request(params)
                except:
                    # 回退到普通requests
                    response = requests.get(self.eastmoney_base_url, params=params, timeout=30)
                
                if response.status_code == 200:
                    # 处理JSONP响应
                    text = response.text if hasattr(response, 'text') else response
                    
                    # 提取JSON数据（去除JSONP包装）
                    if 'jQuery' in text and '(' in text:
                        start = text.find('(') + 1
                        end = text.rfind(')')
                        json_text = text[start:end]
                    else:
                        json_text = text
                    
                    data = json.loads(json_text)
                    
                    if data.get('rc') == 0 and data.get('data'):
                        stock_data_list = data['data'].get('diff', [])
                        
                        for stock_data in stock_data_list:
                            if stock_data:
                                stock_code = stock_data.get('f12', '')
                                if stock_code:
                                    parsed_data = self._parse_stock_data(stock_data)
                                    collected_data[stock_code] = parsed_data
                                    self.collected_count += 1
                        
                        logger.info(f"   ✅ 批次成功，获取 {len(stock_data_list)} 只股票数据")
                        return True
                    else:
                        logger.warning(f"   ⚠️ 批次返回数据异常: {data}")
                else:
                    logger.error(f"   ❌ 批次请求失败: {response.status_code}")
                
            except Exception as e:
                logger.error(f"   ❌ 批次请求异常: {e}")
            
            if attempt < self.max_retries - 1:
                wait_time = 10 + random.randint(5, 10)
                logger.info(f"   🔄 重试 {attempt + 1}/{self.max_retries}，等待 {wait_time} 秒...")
                await asyncio.sleep(wait_time)
        
        return False
    
    async def _crawl4ai_request(self, params: Dict[str, str]):
        """使用天枢星crawl4ai进行请求"""
        try:
            # 尝试调用天枢星的crawl4ai服务
            crawl4ai_url = "http://127.0.0.1:8002/api/intelligence/crawl4ai"
            
            crawl_request = {
                "url": self.eastmoney_base_url,
                "params": params,
                "method": "GET",
                "timeout": 30
            }
            
            response = requests.post(crawl4ai_url, json=crawl_request, timeout=35)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    # 模拟response对象
                    class MockResponse:
                        def __init__(self, text, status_code):
                            self.text = text
                            self.status_code = status_code
                    
                    return MockResponse(result['data']['content'], 200)
            
            raise Exception("crawl4ai服务不可用")
            
        except Exception as e:
            logger.debug(f"crawl4ai请求失败，回退到普通请求: {e}")
            raise e
    
    def _parse_stock_data(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析股票数据"""
        parsed = {
            'trade_date': datetime.now().strftime('%Y-%m-%d'),
            'data_source': 'eastmoney_complete',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 映射所有字段 - 修复数据类型转换问题
        for field_code, field_name in self.field_mapping.items():
            value = stock_data.get(field_code)
            if value is not None and value != '-':
                try:
                    # 确保数值类型转换
                    if isinstance(value, str):
                        # 处理字符串数值
                        if value.replace('.', '').replace('-', '').isdigit():
                            numeric_value = float(value)
                        else:
                            numeric_value = 0
                    else:
                        numeric_value = float(value) if value != 0 else 0

                    # 处理特殊字段的数值转换
                    if field_name in ['change_percent', 'amplitude', 'turnover_rate']:
                        parsed[field_name] = numeric_value / 100 if numeric_value != 0 else 0  # 百分比转换
                    elif field_name in ['pe_ratio', 'pb_ratio']:
                        parsed[field_name] = numeric_value / 100 if numeric_value != 0 else None  # PE/PB转换
                    elif field_name in ['current_price', 'change_amount', 'high_price', 'low_price',
                                      'open_price', 'pre_close_price', 'limit_up_price', 'limit_down_price']:
                        parsed[field_name] = numeric_value / 100 if numeric_value != 0 else 0  # 价格转换（分转元）
                    elif field_name in ['stock_code', 'stock_name', 'market']:
                        parsed[field_name] = str(value)  # 字符串字段
                    else:
                        parsed[field_name] = numeric_value
                except (ValueError, TypeError) as e:
                    logger.warning(f"字段 {field_name} 数值转换失败: {value}, 错误: {e}")
                    parsed[field_name] = None
            else:
                parsed[field_name] = None
        
        return parsed
    
    async def collect_historical_data(self, stock_code: str, years: int = 10) -> List[Dict[str, Any]]:
        """收集单只股票的历史数据"""
        try:
            # 确定secid格式
            if stock_code.startswith('6'):
                secid = f"1.{stock_code}"
            else:
                secid = f"0.{stock_code}"
            
            # 计算需要的数据量（10年约2500个交易日）
            limit_days = years * 250
            
            params = {
                'secid': secid,
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K线
                'fqt': '1',    # 前复权
                'end': '20500101',
                'lmt': str(limit_days)
            }
            
            response = requests.get(self.history_base_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('rc') == 0:
                    klines = data.get('data', {}).get('klines', [])
                    
                    historical_data = []
                    for kline in klines:
                        kline_data = kline.split(',')
                        if len(kline_data) >= 11:
                            historical_data.append({
                                'stock_code': stock_code,
                                'trade_date': kline_data[0],
                                'open_price': float(kline_data[1]) if kline_data[1] else 0,
                                'close_price': float(kline_data[2]) if kline_data[2] else 0,
                                'high_price': float(kline_data[3]) if kline_data[3] else 0,
                                'low_price': float(kline_data[4]) if kline_data[4] else 0,
                                'volume': int(float(kline_data[5])) if kline_data[5] else 0,
                                'amount': float(kline_data[6]) if kline_data[6] else 0,
                                'amplitude': float(kline_data[7]) if kline_data[7] else 0,
                                'change_percent': float(kline_data[8]) if kline_data[8] else 0,
                                'change_amount': float(kline_data[9]) if kline_data[9] else 0,
                                'turnover_rate': float(kline_data[10]) if kline_data[10] else 0,
                                'data_source': 'eastmoney_historical',
                                'created_at': datetime.now().isoformat(),
                                'updated_at': datetime.now().isoformat()
                            })
                    
                    return historical_data
            
            return []
            
        except Exception as e:
            logger.error(f"获取 {stock_code} 历史数据失败: {e}")
            return []
    
    def save_to_database(self, realtime_data: Dict[str, Any], historical_data: Dict[str, List[Dict[str, Any]]]):
        """保存数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 保存实时数据
            logger.info("💾 开始保存实时数据...")
            for stock_code, data in realtime_data.items():
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_data (
                            stock_code, trade_date, open_price, close_price, high_price, low_price,
                            volume, amount, change_percent, turnover_rate, total_market_cap, 
                            float_market_cap, amplitude, price_change, pre_close_price,
                            pe_ratio, pb_ratio, data_source, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        data['stock_code'], data['trade_date'], data['open_price'], 
                        data['current_price'], data['high_price'], data['low_price'],
                        data['volume'], data['amount'], data['change_percent'], 
                        data['turnover_rate'], data['total_market_cap'], data['float_market_cap'],
                        data['amplitude'], data['change_amount'], data['pre_close_price'],
                        data['pe_ratio'], data['pb_ratio'], data['data_source'], 
                        data['created_at'], data['updated_at']
                    ))
                    self.total_records += 1
                except Exception as e:
                    logger.error(f"保存实时数据失败 {stock_code}: {e}")
            
            # 保存历史数据
            logger.info("💾 开始保存历史数据...")
            for stock_code, data_list in historical_data.items():
                for data in data_list:
                    try:
                        cursor.execute("""
                            INSERT OR REPLACE INTO daily_data (
                                stock_code, trade_date, open_price, close_price, high_price, low_price,
                                volume, amount, change_percent, turnover_rate, amplitude,
                                price_change, data_source, created_at, updated_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            data['stock_code'], data['trade_date'], data['open_price'],
                            data['close_price'], data['high_price'], data['low_price'],
                            data['volume'], data['amount'], data['change_percent'],
                            data['turnover_rate'], data['amplitude'], data['change_amount'],
                            data['data_source'], data['created_at'], data['updated_at']
                        ))
                        self.total_records += 1
                    except Exception as e:
                        logger.error(f"保存历史数据失败 {stock_code} {data['trade_date']}: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"💾 数据保存完成，总计 {self.total_records} 条记录")
            
        except Exception as e:
            logger.error(f"❌ 数据库保存失败: {e}")

async def main():
    """主函数"""
    print("🌟 完整A股数据收集系统启动")
    print("="*80)
    print("📋 收集目标:")
    print("   - 5000+只A股股票")
    print("   - 10年历史数据")
    print("   - 20个完整字段")
    print("   - 结合天枢星crawl4ai")
    print("="*80)
    
    collector = CompleteAStockDataCollector()
    
    # 1. 获取股票列表
    stocks = collector.get_all_a_stocks()
    if not stocks:
        print("❌ 无法获取股票列表")
        return
    
    print(f"📋 准备收集 {len(stocks)} 只股票的完整数据")
    
    # 2. 收集实时数据（全字段）
    print("\n📊 第一阶段：收集实时全字段数据...")
    realtime_data = await collector.collect_realtime_data_batch(stocks)
    
    # 3. 收集历史数据（全部5151只A股股票）
    print("\n📈 第二阶段：收集全部A股10年历史数据...")
    historical_data = {}

    print(f"🎯 目标：收集全部 {len(stocks)} 只A股的10年历史数据")
    print("⏰ 预计时间：约21小时（每只股票15秒间隔）")
    print("📊 数据量：约500万条历史记录")

    # 收集全部股票的历史数据
    for i, stock in enumerate(stocks):
        stock_code = stock['code']
        print(f"📈 收集历史数据 {i+1}/{len(stocks)}: {stock_code} ({stock['name']})")

        hist_data = await collector.collect_historical_data(stock_code, years=10)
        if hist_data:
            historical_data[stock_code] = hist_data
            print(f"   ✅ 获取 {len(hist_data)} 条历史记录")
        else:
            print(f"   ❌ 历史数据获取失败")

        # 每100只股票保存一次数据
        if (i + 1) % 100 == 0:
            print(f"\n💾 阶段性保存：已收集 {i+1} 只股票的历史数据...")
            collector.save_to_database({}, historical_data)
            historical_data = {}  # 清空已保存的数据
            print(f"✅ 阶段性保存完成，继续收集...")

        # 请求间隔
        if i < len(stocks) - 1:
            wait_time = collector.request_interval + random.randint(3, 7)
            print(f"   ⏰ 等待 {wait_time} 秒...")
            await asyncio.sleep(wait_time)
    
    # 4. 保存到数据库
    print("\n💾 第三阶段：保存数据到数据库...")
    collector.save_to_database(realtime_data, historical_data)
    
    # 5. 生成收集报告
    print("\n📊 数据收集完成报告")
    print("="*80)
    print(f"📈 实时数据: {len(realtime_data)} 只股票")
    print(f"📊 历史数据: {len(historical_data)} 只股票")
    print(f"💾 总记录数: {collector.total_records:,}")
    print(f"✅ 成功率: {collector.collected_count/(collector.collected_count+collector.failed_count)*100:.1f}%")
    
    print("\n🎉 完整A股数据收集完成！")

if __name__ == "__main__":
    asyncio.run(main())
