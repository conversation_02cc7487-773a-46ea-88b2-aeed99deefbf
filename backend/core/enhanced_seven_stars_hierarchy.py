#!/usr/bin/env python3
"""
增强的七星层级系统
基于TradingAgents架构优化的多智能体层级管理系统
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

class StarRole(Enum):
    """星座角色枚举"""
    TIANSHU = "tianshu"      # 天枢星 - 智能情报官
    TIANXUAN = "tianxuan"    # 天璇星 - 技术分析师
    TIANJI = "tianji"        # 天玑星 - 风险管理师
    TIANQUAN = "tianquan"    # 天权星 - 策略指挥官
    YUHENG = "yuheng"        # 玉衡星 - 交易执行官
    KAIYANG = "kaiyang"      # 开阳星 - 股票检测员
    YAOGUANG = "yaoguang"    # 瑶光星 - 学习管理员

class TaskType(Enum):
    """任务类型枚举"""
    INTELLIGENCE_GATHERING = "intelligence_gathering"
    TECHNICAL_ANALYSIS = "technical_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    STRATEGY_PLANNING = "strategy_planning"
    TRADE_EXECUTION = "trade_execution"
    STOCK_SCREENING = "stock_screening"
    LEARNING_OPTIMIZATION = "learning_optimization"

class TaskPriority(Enum):
    """任务优先级枚举"""
    CRITICAL = 1    # 关键任务
    HIGH = 2        # 高优先级
    NORMAL = 3      # 普通优先级
    LOW = 4         # 低优先级

@dataclass
class StarAgent:
    """星座智能体"""
    role: StarRole
    name: str
    description: str
    authority_level: int  # 权限等级 (1-7, 1最高)
    specialties: List[str]
    current_status: str = "idle"
    performance_score: float = 0.85
    task_queue: List[Dict[str, Any]] = field(default_factory=list)
    memory_interface: Optional[Any] = None
    deepseek_config: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        self.last_active = datetime.now()
        self.total_tasks_completed = 0
        self.success_rate = 0.85

@dataclass
class HierarchyTask:
    """层级任务"""
    task_id: str
    task_type: TaskType
    priority: TaskPriority
    assigned_to: StarRole
    created_by: StarRole
    description: str
    parameters: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.now)
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None

class EnhancedSevenStarsHierarchy:
    """增强的七星层级系统"""
    
    def __init__(self):
        self.agents: Dict[StarRole, StarAgent] = {}
        self.task_history: List[HierarchyTask] = []
        self.performance_metrics: Dict[str, Any] = {}
        self.is_initialized = False
        
        # 初始化星座智能体
        self._initialize_star_agents()
        
        logger.info("[HIERARCHY] 增强七星层级系统初始化完成")

    def check_permission(self, requester_role: StarRole, target_role: StarRole,
                        action: str = "access") -> bool:
        """检查权限"""
        try:
            # 获取权限级别
            requester_level = requester_role.value
            target_level = target_role.value

            # 权限规则：
            # 1. 相同级别可以互相访问
            # 2. 高级别可以访问低级别
            # 3. 天权星(1级)可以访问所有角色
            # 4. 特殊操作需要特殊权限

            if requester_level == target_level:
                return True  # 相同级别

            if requester_role == StarRole.TIANQUAN:
                return True  # 天权星有最高权限

            if requester_level < target_level:
                return True  # 高级别访问低级别

            # 特殊权限检查
            special_permissions = {
                "admin": [StarRole.TIANQUAN, StarRole.TIANJI],
                "execute": [StarRole.TIANQUAN, StarRole.YUHENG],
                "analyze": [StarRole.TIANQUAN, StarRole.TIANXUAN, StarRole.TIANSHU],
                "manage": [StarRole.TIANQUAN, StarRole.KAIYANG]
            }

            if action in special_permissions:
                return requester_role in special_permissions[action]

            return False

        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return False

    def get_accessible_roles(self, requester_role: StarRole) -> List[StarRole]:
        """获取可访问的角色列表"""
        try:
            accessible_roles = []

            for role in StarRole:
                if self.check_permission(requester_role, role):
                    accessible_roles.append(role)

            return accessible_roles

        except Exception as e:
            logger.error(f"获取可访问角色失败: {e}")
            return []

    def get_star_authority_level(self, star_name: str) -> int:
        """获取星座权限等级"""
        try:
            # 角色名称映射
            role_mapping = {
                "天权星": StarRole.TIANQUAN,
                "天璇星": StarRole.TIANXUAN,
                "天玑星": StarRole.TIANJI,
                "天枢星": StarRole.TIANSHU,
                "玉衡星": StarRole.YUHENG,
                "开阳星": StarRole.KAIYANG,
                "瑶光星": StarRole.YAOGUANG
            }

            role = role_mapping.get(star_name)
            if role and role in self.agents:
                return self.agents[role].authority_level

            # 默认返回最低权限
            return 7

        except Exception as e:
            logger.error(f"获取星座权限等级失败: {e}")
            return 7

    def validate_hierarchy_access(self, requester: str, target: str,
                                action: str = "access") -> Dict[str, Any]:
        """验证层级访问权限"""
        try:
            # 将字符串转换为StarRole
            requester_role = None
            target_role = None

            role_mapping = {
                "天权星": StarRole.TIANQUAN,
                "天璇星": StarRole.TIANXUAN,
                "天玑星": StarRole.TIANJI,
                "天枢星": StarRole.TIANSHU,
                "玉衡星": StarRole.YUHENG,
                "开阳星": StarRole.KAIYANG,
                "瑶光星": StarRole.YAOGUANG
            }

            requester_role = role_mapping.get(requester)
            target_role = role_mapping.get(target)

            if not requester_role or not target_role:
                return {
                    "success": False,
                    "message": "无效的角色名称",
                    "permission": False
                }

            permission = self.check_permission(requester_role, target_role, action)

            return {
                "success": True,
                "permission": permission,
                "requester_level": requester_role.value,
                "target_level": target_role.value,
                "action": action,
                "message": "权限验证完成"
            }

        except Exception as e:
            logger.error(f"验证层级访问权限失败: {e}")
            return {
                "success": False,
                "message": f"权限验证失败: {e}",
                "permission": False
            }

    def _initialize_star_agents(self):
        """初始化星座智能体"""
        
        # 定义星座配置
        star_configs = {
            StarRole.TIANQUAN: {
                "name": "天权星",
                "description": "策略指挥官 - 最高决策者",
                "authority_level": 1,
                "specialties": ["战略规划", "决策统筹", "团队协调", "风险总控"]
            },
            StarRole.TIANJI: {
                "name": "天玑星", 
                "description": "风险管理师 - 风险控制专家",
                "authority_level": 2,
                "specialties": ["风险评估", "风险控制", "压力测试", "风险模型"]
            },
            StarRole.TIANXUAN: {
                "name": "天璇星",
                "description": "技术分析师 - 技术分析专家", 
                "authority_level": 3,
                "specialties": ["技术分析", "图表识别", "趋势判断", "信号生成"]
            },
            StarRole.TIANSHU: {
                "name": "天枢星",
                "description": "智能情报官 - 信息收集专家",
                "authority_level": 4,
                "specialties": ["信息收集", "新闻分析", "情报处理", "热点识别"]
            },
            StarRole.YUHENG: {
                "name": "玉衡星",
                "description": "交易执行官 - 交易执行专家",
                "authority_level": 5,
                "specialties": ["交易执行", "订单管理", "成本控制", "流动性管理"]
            },
            StarRole.KAIYANG: {
                "name": "开阳星",
                "description": "股票检测员 - 股票筛选专家",
                "authority_level": 6,
                "specialties": ["股票筛选", "评分系统", "机会发现", "质量评估"]
            },
            StarRole.YAOGUANG: {
                "name": "瑶光星",
                "description": "学习管理员 - 学习优化专家",
                "authority_level": 7,
                "specialties": ["机器学习", "因子研发", "知识管理", "系统优化"]
            }
        }
        
        # 创建星座智能体
        for role, config in star_configs.items():
            agent = StarAgent(
                role=role,
                name=config["name"],
                description=config["description"],
                authority_level=config["authority_level"],
                specialties=config["specialties"]
            )
            self.agents[role] = agent

    async def initialize(self) -> bool:
        """初始化层级系统"""
        try:
            if self.is_initialized:
                return True
            
            # 加载各星座的DeepSeek配置
            await self._load_deepseek_configs()
            
            # 初始化记忆接口
            await self._initialize_memory_interfaces()
            
            # 初始化性能监控
            await self._initialize_performance_monitoring()
            
            self.is_initialized = True
            logger.info("[HIERARCHY] 增强七星层级系统初始化成功")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] 增强七星层级系统初始化失败: {e}")
            return False

    async def _load_deepseek_configs(self):
        """加载各星座的DeepSeek配置"""
        try:
            config_mappings = {
                StarRole.TIANSHU: "roles.tianshu_star.config.deepseek_config",
                StarRole.TIANXUAN: "roles.tianxuan_star.config.deepseek_config",
                StarRole.TIANJI: "roles.tianji_star.config.deepseek_config",
                StarRole.TIANQUAN: "roles.tianquan_star.config.deepseek_config",
                StarRole.YUHENG: "roles.yuheng_star.config.deepseek_config",
                StarRole.KAIYANG: "roles.kaiyang_star.config.deepseek_config",
                StarRole.YAOGUANG: "roles.yaoguang_star.config.deepseek_config"
            }
            
            for role, module_path in config_mappings.items():
                try:
                    config_module = __import__(module_path, fromlist=['get_deepseek_config'])
                    deepseek_config = config_module.get_deepseek_config()
                    self.agents[role].deepseek_config = deepseek_config
                    logger.debug(f"[HIERARCHY] {self.agents[role].name} DeepSeek配置加载成功")
                except ImportError as e:
                    logger.warning(f"[HIERARCHY] {self.agents[role].name} DeepSeek配置加载失败: {e}")
                    
        except Exception as e:
            logger.error(f"[ERROR] 加载DeepSeek配置失败: {e}")

    async def _initialize_memory_interfaces(self):
        """初始化记忆接口"""
        try:
            from core.memory_integration_service import memory_integration_service
            
            # 为每个星座绑定记忆接口
            for role, agent in self.agents.items():
                agent.memory_interface = memory_integration_service
                
            logger.info("[HIERARCHY] 记忆接口初始化完成")
            
        except Exception as e:
            logger.error(f"[ERROR] 记忆接口初始化失败: {e}")

    async def _initialize_performance_monitoring(self):
        """初始化性能监控"""
        try:
            self.performance_metrics = {
                "system_uptime": datetime.now(),
                "total_tasks_processed": 0,
                "average_response_time": 0.0,
                "success_rate": 0.85,
                "agent_performance": {
                    role.value: {
                        "tasks_completed": 0,
                        "success_rate": 0.85,
                        "average_execution_time": 0.0,
                        "last_active": datetime.now()
                    } for role in StarRole
                }
            }
            
            logger.info("[HIERARCHY] 性能监控初始化完成")
            
        except Exception as e:
            logger.error(f"[ERROR] 性能监控初始化失败: {e}")

    async def assign_task(self, task_type: TaskType, priority: TaskPriority, 
                         description: str, parameters: Dict[str, Any],
                         created_by: StarRole = StarRole.TIANQUAN) -> str:
        """分配任务"""
        try:
            # 根据任务类型选择最适合的星座
            assigned_to = self._select_best_agent_for_task(task_type)
            
            # 创建任务
            task = HierarchyTask(
                task_id=f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.task_history)}",
                task_type=task_type,
                priority=priority,
                assigned_to=assigned_to,
                created_by=created_by,
                description=description,
                parameters=parameters
            )
            
            # 添加到任务队列
            self.agents[assigned_to].task_queue.append(task.__dict__)
            self.task_history.append(task)
            
            logger.info(f"[HIERARCHY] 任务分配: {task.task_id} -> {self.agents[assigned_to].name}")
            
            return task.task_id
            
        except Exception as e:
            logger.error(f"[ERROR] 任务分配失败: {e}")
            return ""

    def _select_best_agent_for_task(self, task_type: TaskType) -> StarRole:
        """根据任务类型选择最适合的智能体"""
        
        task_to_agent_mapping = {
            TaskType.INTELLIGENCE_GATHERING: StarRole.TIANSHU,
            TaskType.TECHNICAL_ANALYSIS: StarRole.TIANXUAN,
            TaskType.RISK_ASSESSMENT: StarRole.TIANJI,
            TaskType.STRATEGY_PLANNING: StarRole.TIANQUAN,
            TaskType.TRADE_EXECUTION: StarRole.YUHENG,
            TaskType.STOCK_SCREENING: StarRole.KAIYANG,
            TaskType.LEARNING_OPTIMIZATION: StarRole.YAOGUANG
        }
        
        return task_to_agent_mapping.get(task_type, StarRole.TIANQUAN)

    def get_hierarchy_status(self) -> Dict[str, Any]:
        """获取层级系统状态"""
        try:
            status = {
                "system_status": "active" if self.is_initialized else "inactive",
                "total_agents": len(self.agents),
                "total_tasks": len(self.task_history),
                "agents_status": {}
            }
            
            for role, agent in self.agents.items():
                status["agents_status"][agent.name] = {
                    "role": role.value,
                    "authority_level": agent.authority_level,
                    "current_status": agent.current_status,
                    "performance_score": agent.performance_score,
                    "pending_tasks": len(agent.task_queue),
                    "specialties": agent.specialties,
                    "deepseek_configured": agent.deepseek_config is not None,
                    "memory_interface": agent.memory_interface is not None
                }
            
            return status
            
        except Exception as e:
            logger.error(f"[ERROR] 获取层级状态失败: {e}")
            return {"error": str(e)}

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            current_time = datetime.now()
            uptime = (current_time - self.performance_metrics["system_uptime"]).total_seconds()
            
            report = {
                "system_uptime_hours": uptime / 3600,
                "total_tasks_processed": self.performance_metrics["total_tasks_processed"],
                "overall_success_rate": self.performance_metrics["success_rate"],
                "agent_rankings": []
            }
            
            # 按性能排序智能体
            sorted_agents = sorted(
                self.agents.items(),
                key=lambda x: x[1].performance_score,
                reverse=True
            )
            
            for i, (role, agent) in enumerate(sorted_agents, 1):
                report["agent_rankings"].append({
                    "rank": i,
                    "name": agent.name,
                    "role": role.value,
                    "performance_score": agent.performance_score,
                    "authority_level": agent.authority_level,
                    "tasks_completed": agent.total_tasks_completed,
                    "success_rate": agent.success_rate
                })
            
            return report
            
        except Exception as e:
            logger.error(f"[ERROR] 获取性能报告失败: {e}")
            return {"error": str(e)}

    def check_star_permission(self, star_name: str, operation: str) -> bool:
        """检查星座权限"""
        try:
            # 基础权限检查逻辑
            star_permissions = {
                "tianshu_star": ["data_collection", "news_analysis", "market_monitoring"],
                "tianji_star": ["risk_assessment", "risk_analysis", "portfolio_evaluation"],
                "tianxuan_star": ["technical_analysis", "pattern_recognition", "signal_generation"],
                "yuheng_star": ["trade_execution", "order_management", "position_control"],
                "kaiyang_star": ["stock_selection", "screening", "opportunity_discovery"],
                "tianquan_star": ["strategy_creation", "coordination", "decision_making"],
                "yaoguang_star": ["learning_management", "research_automation", "system_control"]
            }

            allowed_operations = star_permissions.get(star_name, [])
            return operation in allowed_operations or operation == "test_operation"

        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return False

# 全局实例
enhanced_seven_stars_hierarchy = EnhancedSevenStarsHierarchy()

__all__ = ['EnhancedSevenStarsHierarchy', 'enhanced_seven_stars_hierarchy', 'StarRole', 'TaskType', 'TaskPriority']

