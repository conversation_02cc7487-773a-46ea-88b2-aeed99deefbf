#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星核心系统集成服务
确保传奇记忆、绩效监控、DeepSeek配置、层级权限正确集成
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class TianshuCoreSystemsIntegration:
    """天枢星核心系统集成器"""
    
    def __init__(self):
        self.memory_system = None
        self.performance_monitor = None
        self.hierarchy_system = None
        self.deepseek_config = None
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """初始化所有核心系统"""
        try:
            # 1. 初始化传奇记忆系统
            await self._init_memory_system()
            
            # 2. 初始化绩效监控系统
            await self._init_performance_monitor()
            
            # 3. 初始化层级权限系统
            await self._init_hierarchy_system()
            
            # 4. 初始化DeepSeek配置
            await self._init_deepseek_config()
            
            self.is_initialized = True
            logger.info("天枢星核心系统集成完成")
            return True
            
        except Exception as e:
            logger.error(f"天枢星核心系统集成失败: {e}")
            return False
    
    async def _init_memory_system(self):
        """初始化传奇记忆系统"""
        try:
            from backend.core.domain.memory.legendary.interface import legendary_memory_interface
            
            self.memory_system = legendary_memory_interface
            await self.memory_system.initialize()
            
            logger.info("✅ 天枢星传奇记忆系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星传奇记忆系统初始化失败: {e}")
            self.memory_system = None
    
    async def _init_performance_monitor(self):
        """初始化绩效监控系统"""
        try:
            from backend.core.performance.star_performance_monitor import star_performance_monitor
            
            self.performance_monitor = star_performance_monitor
            
            logger.info("✅ 天枢星绩效监控系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星绩效监控系统初始化失败: {e}")
            self.performance_monitor = None
    
    async def _init_hierarchy_system(self):
        """初始化层级权限系统"""
        try:
            from backend.core.enhanced_seven_stars_hierarchy import EnhancedSevenStarsHierarchy
            
            self.hierarchy_system = EnhancedSevenStarsHierarchy()
            
            logger.info("✅ 天枢星层级权限系统初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星层级权限系统初始化失败: {e}")
            self.hierarchy_system = None
    
    async def _init_deepseek_config(self):
        """初始化DeepSeek配置"""
        try:
            from backend.roles.tianshu_star.config.deepseek_config import (
                get_deepseek_config, get_role_setting, get_memory_config
            )
            
            self.deepseek_config = {
                "api_config": get_deepseek_config(),
                "role_setting": get_role_setting(),
                "memory_config": get_memory_config()
            }
            
            logger.info("✅ 天枢星DeepSeek配置初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 天枢星DeepSeek配置初始化失败: {e}")
            self.deepseek_config = None
    
    async def store_memory(self, memory_type: str, content: str, importance: float = 0.8) -> Dict[str, Any]:
        """存储记忆"""
        if not self.memory_system:
            return {"success": False, "error": "记忆系统不可用"}
        
        try:
            result = await self.memory_system.store_memory(
                role_name="天枢星",
                memory_type=memory_type,
                content=content,
                importance=importance
            )
            return result
            
        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def record_performance(self, metrics: Dict[str, float]) -> bool:
        """记录绩效"""
        if not self.performance_monitor:
            return False
        
        try:
            await self.performance_monitor.record_performance(
                star_name="天枢星",
                metrics=metrics
            )
            return True
            
        except Exception as e:
            logger.error(f"记录绩效失败: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "initialized": self.is_initialized,
            "memory_system": self.memory_system is not None,
            "performance_monitor": self.performance_monitor is not None,
            "hierarchy_system": self.hierarchy_system is not None,
            "deepseek_config": self.deepseek_config is not None
        }

# 全局实例
tianshu_core_systems = TianshuCoreSystemsIntegration()

    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用天枢星专用DeepSeek分析"""
        try:
            from backend.roles.tianshu_star.config.deepseek_config import (
                get_deepseek_config, get_role_setting, get_professional_prompt, create_context_prompt
            )
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            deepseek_config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建专业提示词
            if context_data:
                role_prompt = create_context_prompt(context_type, context_data)
            else:
                role_prompt = get_professional_prompt(context_type, prompt)
            
            # 调用DeepSeek服务
            response = await deepseek_service.analyze_with_context(
                role_prompt,
                context_type=context_type,
                max_tokens=deepseek_config.get("max_tokens", 2000),
                temperature=deepseek_config.get("temperature", 0.5)
            )
            
            if response.get("success"):
                logger.info(f"✅ 天枢星DeepSeek分析完成")
                return {
                    "success": True,
                    "analysis": response.get("analysis", ""),
                    "role": "天枢星",
                    "context_type": context_type,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                logger.warning(f"⚠️ 天枢星DeepSeek分析失败: {response.get('error')}")
                return {
                    "success": False,
                    "error": response.get("error", "DeepSeek分析失败"),
                    "role": "天枢星"
                }
            
        except Exception as e:
            logger.error(f"❌ 调用天枢星DeepSeek异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "role": "天枢星"
            }
    
    async def _enhance_with_deepseek_analysis(self, data: Dict[str, Any], analysis_type: str = "general") -> Dict[str, Any]:
        """使用DeepSeek增强分析结果"""
        try:
            # 调用角色专用DeepSeek
            deepseek_result = await self._call_role_deepseek(
                prompt=f"请分析以下数据并提供专业见解",
                context_type=analysis_type,
                context_data=data
            )
            
            if deepseek_result.get("success"):
                # 将DeepSeek分析结果融入原数据
                data["deepseek_analysis"] = deepseek_result.get("analysis", "")
                data["ai_enhanced"] = True
                data["analysis_role"] = "天枢星"
                data["analysis_timestamp"] = deepseek_result.get("timestamp")
                
                logger.info(f"✅ 天枢星数据已通过DeepSeek增强")
            else:
                data["deepseek_analysis"] = "AI分析暂时不可用"
                data["ai_enhanced"] = False
                logger.warning(f"⚠️ 天枢星DeepSeek增强失败")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ DeepSeek增强分析异常: {e}")
            data["deepseek_analysis"] = f"分析异常: {str(e)}"
            data["ai_enhanced"] = False
            return data

