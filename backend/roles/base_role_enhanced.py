#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强角色基类 - 集成记忆系统、成本管理和双层闭环
为所有现有角色提供统一的增强功能
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from core.closed_loop_history import ClosedLoopHistoryManager, LoopType, ProcessStepType
from core.investment_cost_manager import InvestmentCostManager, CostType, CostUnit
from core.investment_memory_manager import InvestmentMemoryManager
from core.investment_serialization import InvestmentStatePersistence
from backend.core.domain.messaging.message import InvestmentMessage, MessageType, MessagePriority

import logging

def setup_logger(name):
    return logging.getLogger(name)

logger = setup_logger(__name__)


class RoleEnhancementMixin:
    """角色增强混入类 - 为现有角色添加记忆、成本管理和双层闭环功能"""
    
    def __init__(self):
        # 集成组件
        self.memory_manager: Optional[InvestmentMemoryManager] = None
        self.cost_manager: Optional[InvestmentCostManager] = None
        self.state_persistence: Optional[InvestmentStatePersistence] = None
        self.loop_history_manager: Optional[ClosedLoopHistoryManager] = None
        
        # 角色状态
        self.role_id: str = ""
        self.role_type: str = ""
        self.is_enhanced: bool = False
        
        # 性能指标
        self.messages_processed: int = 0
        self.tasks_completed: int = 0
        self.success_rate: float = 0.0
        self.avg_response_time: float = 0.0
        
        # 双层闭环状态
        self.inner_loop_active: bool = False
        self.outer_loop_active: bool = False
        self.current_inner_loop_id: Optional[str] = None
        self.current_outer_loop_id: Optional[str] = None
        
        # 时间信息
        self.last_activity: Optional[datetime] = None
        self.enhancement_initialized_at: Optional[datetime] = None
    
    async def initialize_role_enhancement(self, role_id: str, role_type: str):
        """初始化角色增强功能"""
        
        self.role_id = role_id
        self.role_type = role_type
        
        try:
            # 初始化记忆管理器
            self.memory_manager = InvestmentMemoryManager()
            await self.memory_manager.initialize(
                role_id=role_id,
                watched_actions=self._get_role_watched_actions()
            )
            
            # 初始化成本管理器（使用全局实例）
            from core.investment_cost_manager import investment_cost_manager
            self.cost_manager = investment_cost_manager
            
            # 初始化状态持久化（使用全局实例）
            from core.investment_serialization import investment_state_persistence
            self.state_persistence = investment_state_persistence
            
            # 初始化闭环历史管理器（使用全局实例）
            from core.closed_loop_history import closed_loop_manager
            self.loop_history_manager = closed_loop_manager
            
            self.is_enhanced = True
            self.enhancement_initialized_at = datetime.now()
            self.last_activity = datetime.now()
            
            logger.info(f"Role enhancement initialized for {role_id} ({role_type})")
            
        except Exception as e:
            logger.error(f"Failed to initialize role enhancement for {role_id}: {e}")
            raise
    
    async def enhanced_process_message(self, message: InvestmentMessage, 
                                     original_handler: callable) -> List[InvestmentMessage]:
        """增强的消息处理 - 包装原有的消息处理逻辑"""
        
        if not self.is_enhanced:
            # 如果未增强，直接调用原处理器
            return await original_handler(message)
        
        start_time = datetime.now()
        
        try:
            # 1. 启动内层闭环
            inner_loop_id = await self._start_inner_loop(message)
            
            # 2. 记录到记忆系统
            if self.memory_manager:
                await self.memory_manager.add_message(message)
            
            # 3. 记录处理成本
            if self.cost_manager:
                self.cost_manager.record_cost(
                    cost_type=CostType.COMPUTE_RESOURCE,
                    service_name=f"role_{self.role_type}",
                    operation="message_processing",
                    cost_amount=0.01,  # 基础处理成本
                    role_id=self.role_id
                )
            
            # 4. 获取记忆驱动的上下文
            context_recommendations = []
            if self.memory_manager:
                context_recommendations = await self.memory_manager.get_contextual_recommendations(
                    f"处理{message.message_type.value}类型消息的最佳实践"
                )
            
            # 5. 调用原有的处理逻辑
            responses = await original_handler(message)
            
            # 6. 增强响应（基于记忆和上下文）
            enhanced_responses = await self._enhance_responses(responses, context_recommendations)
            
            # 7. 完成内层闭环
            await self._complete_inner_loop(inner_loop_id, len(enhanced_responses) > 0)
            
            # 8. 更新性能指标
            self._update_performance_metrics(start_time)
            
            # 9. 保存状态
            if self.state_persistence:
                self.state_persistence.save_role_state(self.role_id, self._get_role_state())
            
            return enhanced_responses
            
        except Exception as e:
            logger.error(f"Enhanced message processing failed for {self.role_id}: {e}")
            
            # 失败时也要完成闭环
            if hasattr(self, 'current_inner_loop_id') and self.current_inner_loop_id:
                await self._complete_inner_loop(self.current_inner_loop_id, False)
            
            # 返回原始处理结果或空列表
            try:
                return await original_handler(message)
            except:
                return []
    
    async def start_outer_loop_collaboration(self, collaboration_context: str) -> str:
        """启动外层闭环协作"""
        
        if not self.loop_history_manager:
            return ""
        
        try:
            outer_loop = self.loop_history_manager.create_loop(
                loop_type=LoopType.ROLE_COLLABORATION,
                loop_name=f"{self.role_type}_外层协作闭环",
                initiator=self.role_id,
                initial_inputs={"collaboration_context": collaboration_context},
                context={"role_type": self.role_type}
            )
            
            self.current_outer_loop_id = outer_loop.loop_id
            self.outer_loop_active = True
            
            logger.info(f"Started outer loop collaboration: {outer_loop.loop_id}")
            return outer_loop.loop_id
            
        except Exception as e:
            logger.error(f"Failed to start outer loop collaboration: {e}")
            return ""
    
    async def complete_outer_loop_collaboration(self, success: bool = True, 
                                              results: Dict[str, Any] = None):
        """完成外层闭环协作"""
        
        if not self.current_outer_loop_id or not self.loop_history_manager:
            return
        
        try:
            self.loop_history_manager.complete_loop(
                self.current_outer_loop_id,
                success=success,
                final_outputs=results or {}
            )
            
            self.outer_loop_active = False
            self.current_outer_loop_id = None
            
            logger.info(f"Completed outer loop collaboration with success={success}")
            
        except Exception as e:
            logger.error(f"Failed to complete outer loop collaboration: {e}")
    
    async def get_memory_insights(self) -> List[Dict[str, Any]]:
        """获取记忆洞察"""
        
        if not self.memory_manager:
            return []
        
        try:
            insights = await self.memory_manager.generate_insights()
            return [
                {
                    "insight_id": insight.insight_id,
                    "type": insight.insight_type,
                    "title": insight.title,
                    "description": insight.description,
                    "confidence": insight.confidence,
                    "importance": insight.importance
                }
                for insight in insights
            ]
        except Exception as e:
            logger.error(f"Failed to get memory insights: {e}")
            return []
    
    async def get_contextual_recommendations(self, context: str) -> List[Dict[str, Any]]:
        """获取上下文推荐"""
        
        if not self.memory_manager:
            return []
        
        try:
            return await self.memory_manager.get_contextual_recommendations(context)
        except Exception as e:
            logger.error(f"Failed to get contextual recommendations: {e}")
            return []
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """获取成本摘要"""
        
        if not self.cost_manager:
            return {}
        
        try:
            summary = self.cost_manager.get_cost_summary(days=7)
            
            # 过滤出本角色的成本
            role_costs = {}
            for record in self.cost_manager.cost_records:
                if record.role_id == self.role_id:
                    cost_type = record.cost_type.value
                    role_costs[cost_type] = role_costs.get(cost_type, 0) + record.cost_amount
            
            return {
                "role_id": self.role_id,
                "role_costs": role_costs,
                "total_role_cost": sum(role_costs.values()),
                "global_summary": summary
            }
        except Exception as e:
            logger.error(f"Failed to get cost summary: {e}")
            return {}
    
    def get_enhancement_status(self) -> Dict[str, Any]:
        """获取增强状态"""
        
        return {
            "role_id": self.role_id,
            "role_type": self.role_type,
            "is_enhanced": self.is_enhanced,
            "enhancement_initialized_at": self.enhancement_initialized_at.isoformat() if self.enhancement_initialized_at else None,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "messages_processed": self.messages_processed,
            "tasks_completed": self.tasks_completed,
            "success_rate": self.success_rate,
            "avg_response_time": self.avg_response_time,
            "inner_loop_active": self.inner_loop_active,
            "outer_loop_active": self.outer_loop_active,
            "current_inner_loop_id": self.current_inner_loop_id,
            "current_outer_loop_id": self.current_outer_loop_id,
            "memory_manager_initialized": self.memory_manager is not None,
            "cost_manager_initialized": self.cost_manager is not None,
            "state_persistence_initialized": self.state_persistence is not None,
            "loop_history_manager_initialized": self.loop_history_manager is not None
        }
    
    async def _start_inner_loop(self, message: InvestmentMessage) -> str:
        """启动内层闭环"""
        
        if not self.loop_history_manager:
            return ""
        
        try:
            inner_loop = self.loop_history_manager.create_loop(
                loop_type=LoopType.ROLE_INTERNAL,
                loop_name=f"{self.role_type}_内层处理闭环",
                initiator=self.role_id,
                initial_inputs={"message_content": message.content, "message_type": message.message_type.value},
                context={"role_type": self.role_type, "message_id": message.id}
            )
            
            self.current_inner_loop_id = inner_loop.loop_id
            self.inner_loop_active = True
            
            return inner_loop.loop_id
            
        except Exception as e:
            logger.error(f"Failed to start inner loop: {e}")
            return ""
    
    async def _complete_inner_loop(self, loop_id: str, success: bool):
        """完成内层闭环"""
        
        if not loop_id or not self.loop_history_manager:
            return
        
        try:
            self.loop_history_manager.complete_loop(
                loop_id,
                success=success,
                final_outputs={"processing_success": success, "role_type": self.role_type}
            )
            
            self.inner_loop_active = False
            self.current_inner_loop_id = None
            
        except Exception as e:
            logger.error(f"Failed to complete inner loop: {e}")
    
    async def _enhance_responses(self, responses: List[InvestmentMessage], 
                               context_recommendations: List[Dict[str, Any]]) -> List[InvestmentMessage]:
        """增强响应"""
        
        if not context_recommendations:
            return responses
        
        # 为响应添加基于记忆的增强信息
        enhanced_responses = []
        
        for response in responses:
            # 检查是否有相关的历史经验可以添加
            if context_recommendations:
                recommendation = context_recommendations[0]  # 使用第一个推荐
                
                # 在响应内容中添加历史经验参考
                enhanced_content = response.content
                if recommendation.get('type') == 'historical_decision':
                    enhanced_content += f" (参考历史经验: {recommendation.get('description', '')})"
                
                # 创建增强的响应
                enhanced_response = InvestmentMessage(
                    content=enhanced_content,
                    sent_from=response.sent_from,
                    send_to=response.send_to,
                    cause_by=response.cause_by,
                    message_type=response.message_type,
                    priority=response.priority,
                    metadata={
                        **response.metadata,
                        "enhanced_by_memory": True,
                        "recommendation_confidence": recommendation.get('confidence', 0)
                    }
                )
                enhanced_responses.append(enhanced_response)
            else:
                enhanced_responses.append(response)
        
        return enhanced_responses
    
    def _update_performance_metrics(self, start_time: datetime):
        """更新性能指标"""
        
        self.messages_processed += 1
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 更新平均响应时间
        self.avg_response_time = (
            (self.avg_response_time * (self.messages_processed - 1) + processing_time) 
            / self.messages_processed
        )
        
        self.last_activity = datetime.now()
    
    def _get_role_watched_actions(self) -> List[str]:
        """获取角色监听的动作列表"""
        
        action_map = {
            "intelligence_officer": ["market_analysis", "data_collection", "news_analysis"],
            "strategy_architect": ["strategy_design", "portfolio_optimization", "model_training"],
            "risk_manager": ["risk_assessment", "risk_monitoring", "alert_generation"],
            "stock_manager": ["stock_analysis", "position_management", "performance_tracking"],
            "trader": ["order_execution", "trade_monitoring", "settlement"],
            "decision_commander": ["decision_making", "command_coordination", "workflow_management"]
        }
        
        return action_map.get(self.role_type, [])
    
    def _get_role_state(self) -> Dict[str, Any]:
        """获取角色状态"""
        
        return {
            "role_id": self.role_id,
            "role_type": self.role_type,
            "is_enhanced": self.is_enhanced,
            "messages_processed": self.messages_processed,
            "tasks_completed": self.tasks_completed,
            "success_rate": self.success_rate,
            "avg_response_time": self.avg_response_time,
            "inner_loop_active": self.inner_loop_active,
            "outer_loop_active": self.outer_loop_active,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "enhancement_initialized_at": self.enhancement_initialized_at.isoformat() if self.enhancement_initialized_at else None
        }
