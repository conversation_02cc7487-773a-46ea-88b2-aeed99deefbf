#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据管理器
管理30年A股完整数据 - 真实实现版本
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
import logging
import os
import sqlite3
from pathlib import Path

# 导入现有数据库管理器
try:
    from backend.core.infrastructure.database.real_database_manager import real_database_manager
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

logger = logging.getLogger(__name__)

class HistoricalDataManager:
    """历史数据管理器 - 真实实现版本"""

    def __init__(self):
        self.service_name = "HistoricalDataManager"
        self.version = "2.0.0"

        # 使用现有数据库管理器
        self.database_manager = real_database_manager if DATABASE_AVAILABLE else None

        # 数据缓存配置
        self.cache_config = {
            "daily_cache_hours": 24,      # 日线数据缓存24小时
            "minute_cache_hours": 1,      # 分钟数据缓存1小时
            "fundamental_cache_hours": 72, # 基本面数据缓存72小时
            "max_cache_size": 1000        # 最大缓存条目数
        }

        # 内存缓存
        self.data_cache: Dict[str, Dict] = {}

        # 本地数据库路径
        self.db_path = Path("backend/data/historical_data.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 初始化本地数据库
        self._init_local_database()

        # 数据质量统计
        self.quality_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "db_hits": 0,
            "api_calls": 0,
            "quality_issues": 0
        }

        logger.info(f"{self.service_name} v{self.version} 初始化完成 - 使用现有数据库管理器")

    def _init_local_database(self):
        """初始化本地SQLite数据库"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # 创建历史数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS historical_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    data_type TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume INTEGER,
                    turnover REAL,
                    created_time TEXT,
                    data_source TEXT,
                    UNIQUE(stock_code, data_type, trade_date)
                )
            ''')

            # 创建索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_stock_date
                ON historical_data(stock_code, trade_date)
            ''')

            # 创建数据质量表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS data_quality_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    data_type TEXT NOT NULL,
                    date_range TEXT NOT NULL,
                    quality_score REAL,
                    missing_ratio REAL,
                    issues TEXT,
                    created_time TEXT
                )
            ''')

            conn.commit()
            conn.close()
            logger.info("  本地历史数据数据库初始化完成")

        except Exception as e:
            logger.error(f"  本地数据库初始化失败: {e}")

    async def get_historical_data(self, stock_code: str, data_type: str,
                                start_date: str, end_date: str,
                                force_refresh: bool = False) -> pd.DataFrame:
        """获取历史数据 - 真实实现"""

        self.quality_stats["total_requests"] += 1
        cache_key = f"{stock_code}_{data_type}_{start_date}_{end_date}"

        # 1. 检查内存缓存
        if not force_refresh and cache_key in self.data_cache:
            cache_data = self.data_cache[cache_key]
            cache_time = datetime.fromisoformat(cache_data["timestamp"])
            cache_duration = self._get_cache_duration(data_type)

            if (datetime.now() - cache_time).total_seconds() < cache_duration * 3600:
                self.quality_stats["cache_hits"] += 1
                logger.info(f"  内存缓存命中: {cache_key}")
                return cache_data["data"]

        # 2. 检查本地数据库
        if not force_refresh:
            db_data = await self._get_from_local_db(stock_code, data_type, start_date, end_date)
            if db_data is not None and not db_data.empty:
                self.quality_stats["db_hits"] += 1
                logger.info(f"  本地数据库命中: {cache_key}")

                # 更新内存缓存
                self._update_cache(cache_key, db_data)
                return db_data

        # 3. 从真实数据源获取
        logger.info(f"  从真实数据源获取历史数据: {stock_code} ({start_date} ~ {end_date})")
        data = await self._fetch_from_real_source(stock_code, data_type, start_date, end_date)

        if data is not None and not data.empty:
            self.quality_stats["api_calls"] += 1

            # 数据质量验证
            quality_result = await self.validate_data_quality(data)
            if quality_result["is_valid"]:
                # 保存到本地数据库
                await self._save_to_local_db(stock_code, data_type, data)

                # 更新内存缓存
                self._update_cache(cache_key, data)

                logger.info(f"  历史数据获取成功: {stock_code} ({len(data)}条记录)")
                return data
            else:
                self.quality_stats["quality_issues"] += 1
                logger.warning(f"  数据质量问题: {stock_code} - {quality_result['issues']}")

        # 4. 返回空DataFrame作为降级方案
        logger.warning(f"  无法获取历史数据: {stock_code}")
        return pd.DataFrame()

    async def _fetch_from_real_source(self, stock_code: str, data_type: str,
                                    start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从10年真实数据库获取数据"""

        try:
            if data_type == "daily":
                # 直接从10年数据表获取数据
                df = await self._get_from_10year_db(stock_code, start_date, end_date)

                if df is not None and not df.empty:
                    logger.info(f"  从10年数据库获取成功: {stock_code} ({len(df)}条记录)")
                    return df
                else:
                    logger.warning(f"  10年数据库无数据: {stock_code}")
                    return pd.DataFrame()
            else:
                logger.warning(f"  不支持的数据类型: {data_type}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"  从10年数据库获取数据失败 {stock_code}: {e}")
            return pd.DataFrame()

    # 已删除模拟数据生成方法 - 系统强制使用真实数据
        """已禁用：不再生成模拟数据，强制使用真实数据源"""
        logger.warning(f"  模拟数据生成已禁用，请使用真实数据源: {stock_code}")
        logger.warning(f"  请确保本地数据库包含 {stock_code} 的真实历史数据")
        return pd.DataFrame()

    async def _fetch_from_backup_source(self, stock_code: str, data_type: str,
                                      start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从备用数据源获取数据"""
        try:
            # 使用市场数据服务作为备用
            # 这里可以实现从新浪、腾讯等获取历史数据的逻辑
            # 返回真实数据
            logger.info(f"尝试从备用数据源获取 {stock_code} 的 {data_type} 数据")
            return None  # 暂时返回None，后续可以实现具体逻辑

        except Exception as e:
            logger.error(f"备用数据源获取失败: {e}")
            return None

    def _get_cache_duration(self, data_type: str) -> int:
        """获取缓存时长"""
        return self.cache_config.get(f"{data_type}_cache_hours", 24)

    def _update_cache(self, cache_key: str, data: pd.DataFrame):
        """更新内存缓存"""
        self.data_cache[cache_key] = {
            "data": data,
            "timestamp": datetime.now().isoformat()
        }

        # 限制缓存大小
        if len(self.data_cache) > self.cache_config["max_cache_size"]:
            # 删除最旧的缓存项
            oldest_key = min(self.data_cache.keys(),
                           key=lambda k: self.data_cache[k]["timestamp"])
            del self.data_cache[oldest_key]

    async def _get_from_local_db(self, stock_code: str, data_type: str,
                               start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从本地数据库获取数据"""
        try:
            conn = sqlite3.connect(str(self.db_path))

            query = '''
                SELECT trade_date, open_price, high_price, low_price, close_price, volume
                FROM historical_data
                WHERE stock_code = ? AND data_type = ?
                AND trade_date >= ? AND trade_date <= ?
                ORDER BY trade_date
            '''

            df = pd.read_sql_query(query, conn, params=(stock_code, data_type, start_date, end_date))
            conn.close()

            if not df.empty:
                df['date'] = pd.to_datetime(df['trade_date'])
                df.set_index('date', inplace=True)
                df.rename(columns={
                    'open_price': 'open',
                    'high_price': 'high',
                    'low_price': 'low',
                    'close_price': 'close'
                }, inplace=True)
                return df

            return None

        except Exception as e:
            logger.error(f"从本地数据库获取数据失败: {e}")
            return None

    async def _save_to_local_db(self, stock_code: str, data_type: str, data: pd.DataFrame):
        """保存数据到本地数据库"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            for index, row in data.iterrows():
                cursor.execute('''
                    INSERT OR REPLACE INTO historical_data
                    (stock_code, data_type, trade_date, open_price, high_price,
                     low_price, close_price, volume, created_time, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    stock_code, data_type, index.strftime('%Y-%m-%d'),
                    row.get('open', 0), row.get('high', 0), row.get('low', 0),
                    row.get('close', 0), row.get('volume', 0),
                    datetime.now().isoformat(), 'generated'
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"保存数据到本地数据库失败: {e}")

    async def _get_from_10year_db(self, stock_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从10年数据表获取数据"""
        try:
            # 连接到10年数据库
            db_path = Path('data/complete_a_stock_library/complete_a_stock_data.db')

            if not db_path.exists():
                logger.warning(f"  10年数据库不存在: {db_path}")
                return None

            conn = sqlite3.connect(db_path)

            query = '''
                SELECT trade_date, open_price, high_price, low_price, close_price, volume, amount
                FROM daily_data
                WHERE stock_code = ? AND trade_date >= ? AND trade_date <= ?
                ORDER BY trade_date
            '''

            df = pd.read_sql_query(query, conn, params=(stock_code, start_date, end_date))
            conn.close()

            if not df.empty:
                # 转换为标准格式
                df['date'] = pd.to_datetime(df['trade_date'])
                df.set_index('date', inplace=True)
                df.rename(columns={
                    'open_price': 'open',
                    'high_price': 'high',
                    'low_price': 'low',
                    'close_price': 'close'
                }, inplace=True)

                # 确保数值类型
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                logger.info(f"  从10年数据库获取: {stock_code} ({len(df)}条记录)")
                return df
            else:
                logger.warning(f"  10年数据库中无数据: {stock_code}")
                return None

        except Exception as e:
            logger.error(f"  从10年数据库获取数据失败 {stock_code}: {e}")
            return None

    async def validate_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """验证数据质量"""
        if data.empty:
            return {"is_valid": False, "issues": ["数据为空"]}

        issues = []

        # 检查必要列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            issues.append(f"缺少列: {missing_columns}")

        # 检查数据完整性
        if data.isnull().any().any():
            issues.append("存在空值")

        # 检查价格逻辑
        if 'high' in data.columns and 'low' in data.columns:
            if (data['high'] < data['low']).any():
                issues.append("最高价低于最低价")

        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "quality_score": max(0, 1 - len(issues) * 0.2)
        }

# 全局历史数据管理器实例
historical_data_manager = HistoricalDataManager()

__all__ = ['HistoricalDataManager', 'historical_data_manager']