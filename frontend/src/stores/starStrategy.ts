import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
// 移除模拟数据提供者，使用真实API数据

export const useStarStrategyStore = defineStore('starStrategy', () => {
  // 系统状态
  const systemStatus = ref('online') // online, offline, maintenance
  const isCollaborating = ref(false)
  const activeRoles = ref(new Set(['intelligence', 'stock-manager']))
  
  // 角色数据
  const roles = reactive({
    intelligence: {
      id: 'intelligence',
      name: '情报官',
      star: '天枢星',
      icon: 'Search',
      description: '数据收集与市场分析',
      status: 'working',
      progress: 75,
      isActive: true,
      metrics: {
        todayCount: 1247,
        successRate: 94.5,
        reportCount: 89,
        accuracy: 91.2
      }
    },
    architect: {
      id: 'architect',
      name: '策略架构师',
      star: '天璇星',
      icon: 'Setting',
      description: '策略研发与优化',
      status: 'online',
      progress: 60,
      isActive: true,
      metrics: {
        strategiesCount: 23,
        optimizationRate: 87.3,
        backtestScore: 8.7
      }
    },
    risk: {
      id: 'risk',
      name: '风控总监',
      star: '天玑星',
      icon: 'Warning',
      description: '风险监控与管理',
      status: 'idle',
      progress: 0,
      isActive: false,
      metrics: {
        riskScore: 3.2,
        alertsCount: 5,
        complianceRate: 99.8
      }
    },
    commander: {
      id: 'commander',
      name: '决策指挥官',
      star: '天权星',
      icon: 'Management',
      description: '决策整合与协调',
      status: 'idle',
      progress: 0,
      isActive: false,
      metrics: {
        decisionsCount: 12,
        coordinationScore: 8.9,
        efficiency: 92.1
      }
    },
    trader: {
      id: 'trader',
      name: '执行操盘手',
      star: '玉衡星',
      icon: 'Tools',
      description: '虚拟交易执行',
      status: 'idle',
      progress: 0,
      isActive: false,
      metrics: {
        tradesCount: 156,
        successRate: 78.2,
        profitRate: 12.5
      }
    },
    'stock-manager': {
      id: 'stock-manager',
      name: '股票经理',
      star: '开阳星',
      icon: 'User',
      description: '客户服务交互',
      status: 'online',
      progress: 100,
      isActive: true,
      metrics: {
        clientsCount: 89,
        satisfactionRate: 96.7,
        responseTime: 1.2
      }
    },
    distribution: {
      id: 'distribution',
      name: '分销管理总监',
      star: '瑶光星',
      icon: 'Management',
      description: '三级分销层级管理',
      status: 'idle',
      progress: 0,
      isActive: false,
      metrics: {
        distributorsCount: 156,
        commissionRate: 15.8,
        networkHealth: 94.2
      }
    }
  })

  // 消息系统
  const messages = ref([])
  const unreadCount = ref(0)

  // 数据流
  const dataStream = ref([])
  const isStreamActive = ref(true)

  // 计算属性
  const activeRolesList = computed(() => {
    return Object.values(roles).filter(role => role.isActive)
  })

  const workingRoles = computed(() => {
    return Object.values(roles).filter(role => role.status === 'working')
  })

  const systemHealth = computed(() => {
    const activeCount = activeRolesList.value.length
    const workingCount = workingRoles.value.length
    
    if (workingCount >= 2) return 'excellent'
    if (workingCount >= 1) return 'good'
    if (activeCount >= 2) return 'fair'
    return 'poor'
  })

  // 方法
  const updateRoleStatus = (roleId: string, status: string) => {
    if (roles[roleId]) {
      roles[roleId].status = status
      roles[roleId].isActive = status !== 'offline'
      
      if (status === 'working') {
        activeRoles.value.add(roleId)
      } else if (status === 'offline') {
        activeRoles.value.delete(roleId)
      }
    }
  }

  const updateRoleProgress = (roleId: string, progress: number) => {
    if (roles[roleId]) {
      roles[roleId].progress = Math.max(0, Math.min(100, progress))
    }
  }

  const updateRoleMetrics = (roleId: string, metrics: any) => {
    if (roles[roleId]) {
      roles[roleId].metrics = { ...roles[roleId].metrics, ...metrics }
    }
  }

  const addMessage = (message: any) => {
    messages.value.unshift({
      id: Date.now(),
      timestamp: new Date(),
      ...message
    })
    
    if (messages.value.length > 100) {
      messages.value = messages.value.slice(0, 100)
    }
    
    unreadCount.value++
  }

  const markMessagesAsRead = () => {
    unreadCount.value = 0
  }

  const addDataStreamItem = (item: any) => {
    dataStream.value.unshift({
      id: Date.now(),
      timestamp: new Date(),
      ...item
    })
    
    if (dataStream.value.length > 50) {
      dataStream.value = dataStream.value.slice(0, 50)
    }
  }

  const startCollaboration = () => {
    isCollaborating.value = true
    // 激活主要角色
    updateRoleStatus('intelligence', 'working')
    updateRoleStatus('architect', 'working')
    updateRoleStatus('stock-manager', 'online')
  }

  const stopCollaboration = () => {
    isCollaborating.value = false
    // 重置角色状态
    Object.keys(roles).forEach(roleId => {
      if (roles[roleId].status === 'working') {
        updateRoleStatus(roleId, 'online')
      }
    })
  }

  const getRole = (roleId: string) => {
    return roles[roleId] || null
  }

  const getAllRoles = () => {
    return Object.values(roles)
  }

  const getVisibleRoles = () => {
    // 根据用户权限过滤角色
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const userLevel = userInfo.user_level

    // 调试信息
    console.log('🔍 当前用户信息:', userInfo)
    console.log('🔍 用户级别:', userLevel)

    // 超级管理员可以看到所有角色，包括瑶光星（分销管理）
    if (userLevel === 'super_admin') {
      console.log('👑 超级管理员，显示所有角色包括瑶光星')
      return Object.values(roles)
    }

    // 如果没有用户信息或者是默认用户，临时设置为超级管理员用于测试
    if (!userInfo.user_level || userInfo.username === 'admin' || userInfo.user_id === 'admin') {
      console.log('[WRENCH] 测试模式：设置为超级管理员，显示所有角色包括瑶光星')
      return Object.values(roles)
    }

    // 其他用户不显示瑶光星
    console.log('👤 普通用户，隐藏瑶光星')
    return Object.values(roles).filter(role => role.id !== 'distribution')
  }

  // 真实数据更新 - 使用真实API
  const startRealTimeUpdates = () => {
    setInterval(async () => {
      // 使用真实API获取角色状态更新
      try {
        // 这里应该调用真实的API来获取角色状态
        // 暂时移除模拟数据更新逻辑，等待真实API集成
      } catch (error) {
        console.error('获取真实数据失败:', error)
      }
    }, 3000)
  }

  return {
    // 状态
    systemStatus,
    isCollaborating,
    activeRoles,
    roles,
    messages,
    unreadCount,
    dataStream,
    isStreamActive,
    
    // 计算属性
    activeRolesList,
    workingRoles,
    systemHealth,
    
    // 方法
    updateRoleStatus,
    updateRoleProgress,
    updateRoleMetrics,
    addMessage,
    markMessagesAsRead,
    addDataStreamItem,
    startCollaboration,
    stopCollaboration,
    getRole,
    getAllRoles,
    getVisibleRoles,
    startRealTimeUpdates
  }
})
