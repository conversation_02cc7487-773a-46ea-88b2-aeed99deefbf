#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星精简API - 真实情报收集与分析系统
专注核心功能，移除所有模拟代码和重复功能
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Any, Optional, List, Dict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 创建天枢星路由器
tianshu_router = APIRouter(prefix="/api/tianshu", tags=["天枢星-情报收集与分析"])

# ==================== 请求/响应模型 ====================

class NewsCollectionRequest(BaseModel):
    """新闻收集请求"""
    symbols: Optional[List[str]] = Field(default=[], description="股票代码列表")
    limit: Optional[int] = Field(default=10, description="新闻数量限制")

class IntelligenceRequest(BaseModel):
    """情报分析请求"""
    symbols: Optional[List[str]] = Field(default=[], description="股票代码列表")
    analysis_type: Optional[str] = Field(default="comprehensive", description="分析类型")

class ApiResponse(BaseModel):
    """统一API响应格式"""
    success: bool = Field(description="请求是否成功")
    message: str = Field(description="响应消息")
    data: Optional[Any] = Field(default=None, description="响应数据")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间")

# ==================== 服务依赖 ====================

def get_tianshu_core_service():
    """获取天枢星核心服务"""
    try:
        from backend.roles.tianshu_star.services.tianshu_core_service import tianshu_core_service
        return tianshu_core_service
    except ImportError as e:
        logger.error(f"导入天枢星核心服务失败: {e}")
        raise HTTPException(status_code=500, detail="天枢星核心服务不可用")

# ==================== API端点 ====================

@tianshu_router.post("/intelligence/analyze", response_model=ApiResponse)
async def analyze_intelligence(request: IntelligenceRequest):
    """智能情报分析 - 核心功能"""
    try:
        core_service = get_tianshu_core_service()
        
        # 执行情报收集和分析
        result = await core_service.collect_and_analyze_news(
            symbols=request.symbols,
            limit=10
        )
        
        if result.get("success"):
            return ApiResponse(
                success=True,
                message="情报分析完成",
                data={
                    "analysis_result": result,
                    "summary": {
                        "news_count": result.get("news_count", 0),
                        "events_count": result.get("events_count", 0),
                        "impact_level": result.get("impact_analysis", {}).get("impact_level", "unknown")
                    }
                }
            )
        else:
            return ApiResponse(
                success=False,
                message=f"情报分析失败: {result.get('error', '未知错误')}",
                data=None
            )
            
    except Exception as e:
        logger.error(f"情报分析API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianshu_router.post("/news/collect", response_model=ApiResponse)
async def collect_news(request: NewsCollectionRequest):
    """收集新闻"""
    try:
        core_service = get_tianshu_core_service()
        
        # 收集新闻
        result = await core_service._collect_news(
            symbols=request.symbols,
            limit=request.limit
        )
        
        return ApiResponse(
            success=True,
            message=f"新闻收集完成，获取{len(result)}条新闻",
            data={
                "news_count": len(result),
                "news_data": result
            }
        )
        
    except Exception as e:
        logger.error(f"新闻收集API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianshu_router.get("/intelligence/latest", response_model=ApiResponse)
async def get_latest_intelligence():
    """获取最新情报"""
    try:
        core_service = get_tianshu_core_service()
        
        result = await core_service.get_latest_intelligence(limit=5)
        
        if result.get("success"):
            return ApiResponse(
                success=True,
                message="最新情报获取成功",
                data=result.get("intelligence", {})
            )
        else:
            return ApiResponse(
                success=False,
                message=f"获取最新情报失败: {result.get('error', '未知错误')}",
                data=None
            )
            
    except Exception as e:
        logger.error(f"获取最新情报API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianshu_router.get("/events/identify", response_model=ApiResponse)
async def identify_events():
    """事件识别"""
    try:
        core_service = get_tianshu_core_service()
        
        # 先收集新闻，再识别事件
        news_data = await core_service._collect_news(limit=5)
        events = await core_service._identify_events(news_data)
        
        return ApiResponse(
            success=True,
            message=f"事件识别完成，发现{len(events)}个事件",
            data={
                "events_count": len(events),
                "events": events,
                "source_news_count": len(news_data)
            }
        )
        
    except Exception as e:
        logger.error(f"事件识别API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianshu_router.post("/impact/assess", response_model=ApiResponse)
async def assess_impact(request: IntelligenceRequest):
    """影响评估"""
    try:
        core_service = get_tianshu_core_service()
        
        # 收集新闻并识别事件
        news_data = await core_service._collect_news(symbols=request.symbols, limit=5)
        events = await core_service._identify_events(news_data)
        
        # 评估影响
        impact_analysis = await core_service._assess_impact(events, request.symbols)
        
        return ApiResponse(
            success=True,
            message="影响评估完成",
            data={
                "impact_analysis": impact_analysis,
                "events_analyzed": len(events)
            }
        )
        
    except Exception as e:
        logger.error(f"影响评估API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianshu_router.post("/push/news", response_model=ApiResponse)
async def push_news_alert():
    """推送新闻预警"""
    try:
        core_service = get_tianshu_core_service()
        
        # 获取最新情报
        intelligence = await core_service.get_latest_intelligence(limit=3)
        
        if intelligence.get("success"):
            # 推送信息
            push_result = await core_service._push_information(
                intelligence.get("intelligence", {})
            )
            
            return ApiResponse(
                success=True,
                message="新闻预警推送完成",
                data={
                    "push_result": push_result,
                    "intelligence_summary": intelligence.get("intelligence", {})
                }
            )
        else:
            return ApiResponse(
                success=False,
                message="获取情报失败，无法推送",
                data=None
            )
            
    except Exception as e:
        logger.error(f"新闻预警推送API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@tianshu_router.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    try:
        core_service = get_tianshu_core_service()
        status = core_service.get_service_status()
        
        return ApiResponse(
            success=True,
            message="天枢星健康检查完成",
            data={
                "role": "天枢星-情报收集与分析",
                "status": "healthy",
                "version": status.get("version", "3.0.0"),
                "components": status.get("components", {}),
                "capabilities": status.get("capabilities", [])
            }
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 导出路由器
tianshu_star_router = tianshu_router  # 向后兼容
__all__ = ["tianshu_router", "tianshu_star_router"]
