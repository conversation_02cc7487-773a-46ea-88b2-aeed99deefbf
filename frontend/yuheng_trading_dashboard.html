<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玉衡星 - 交易执行官控制台</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        /* 模式切换区域 */
        .mode-switch {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }

        .mode-button {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
        }

        .mode-button.learning {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .mode-button.automation {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .mode-button.active {
            transform: scale(1.05);
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        .mode-button:not(.active) {
            opacity: 0.6;
            transform: scale(0.95);
        }

        .mode-info {
            margin-left: 20px;
            padding: 10px 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        /* 交易数据表格 */
        .trading-data {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .trading-data h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #555;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .profit {
            color: #28a745;
            font-weight: bold;
        }

        .loss {
            color: #dc3545;
            font-weight: bold;
        }

        /* 统计图表区域 */
        .charts-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .charts-section h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        /* 统计卡片 */
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-card .value {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-card .label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* 消息日志区域 */
        .message-logs {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            grid-column: 1 / -1;
            max-height: 400px;
            overflow-y: auto;
        }

        .message-logs h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3em;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .log-entry {
            margin-bottom: 15px;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .log-entry .timestamp {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }

        .log-entry .content {
            line-height: 1.6;
        }

        .log-entry.analysis {
            border-left-color: #28a745;
        }

        .log-entry.discussion {
            border-left-color: #ffc107;
        }

        .log-entry.execution {
            border-left-color: #dc3545;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .mode-switch {
                flex-direction: column;
                gap: 10px;
            }
            
            .stats-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>🌟 玉衡星 - 交易执行官</h1>
            <div class="subtitle">双轨交易系统控制台</div>
        </div>

        <!-- 模式切换区域 -->
        <div class="mode-switch">
            <button class="mode-button learning active" onclick="switchMode('learning')">
                🎮 学习模式
            </button>
            <button class="mode-button automation" onclick="switchMode('automation')">
                💰 自动化模式
            </button>
            <div class="mode-info">
                <div id="mode-description">
                    <strong>当前模式：学习模式</strong><br>
                    使用瑶光星历史数据库，无风险策略验证
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 交易数据表格 -->
            <div class="trading-data">
                <h3 id="data-title">📊 学习模式 - 虚拟持仓数据</h3>
                
                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="value" id="total-amount">¥0.00</div>
                        <div class="label">总金额</div>
                    </div>
                    <div class="stat-card">
                        <div class="value" id="total-pnl">¥0.00</div>
                        <div class="label">总盈亏</div>
                    </div>
                </div>

                <!-- 持仓表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>股票名称</th>
                            <th>持仓数量</th>
                            <th>成本价格</th>
                            <th>当前价格</th>
                            <th>持仓金额</th>
                            <th>盈亏金额</th>
                            <th>盈亏比例</th>
                            <th>交易日期</th>
                        </tr>
                    </thead>
                    <tbody id="holdings-table">
                        <tr>
                            <td colspan="9" class="loading">正在加载数据...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 统计图表区域 -->
            <div class="charts-section">
                <h3>📈 统计图表</h3>
                
                <!-- 盈亏趋势图 -->
                <div class="chart-container">
                    <canvas id="pnlChart"></canvas>
                </div>
                
                <!-- 交易统计 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="value" id="win-rate">0%</div>
                        <div class="label">胜率</div>
                    </div>
                    <div class="stat-card">
                        <div class="value" id="total-trades">0</div>
                        <div class="label">总交易数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息日志区域 -->
        <div class="message-logs">
            <h3>📝 玉衡星消息日志</h3>
            <div id="logs-container">
                <div class="loading">正在加载日志...</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentMode = 'learning';
        let pnlChart = null;
        const API_BASE = 'http://127.0.0.1:8000/api/yuheng';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadData();
            
            // 定时刷新数据
            setInterval(loadData, 30000); // 30秒刷新一次
        });

        // 初始化页面
        function initializePage() {
            initializeChart();
        }

        // 模式切换
        function switchMode(mode) {
            currentMode = mode;
            
            // 更新按钮状态
            document.querySelectorAll('.mode-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.mode-button.${mode === 'learning' ? 'learning' : 'automation'}`).classList.add('active');
            
            // 更新模式描述
            const descriptions = {
                learning: '<strong>当前模式：学习模式</strong><br>使用瑶光星历史数据库，无风险策略验证',
                automation: '<strong>当前模式：自动化模式</strong><br>使用东方财富实时API，四星辩论决策'
            };
            document.getElementById('mode-description').innerHTML = descriptions[mode];
            
            // 更新数据标题
            const titles = {
                learning: '📊 学习模式 - 虚拟持仓数据',
                automation: '💰 自动化模式 - 真实持仓数据'
            };
            document.getElementById('data-title').textContent = titles[mode];
            
            // 重新加载数据
            loadData();
        }

        // 加载数据
        async function loadData() {
            await Promise.all([
                loadHoldingsData(),
                loadStatisticsData(),
                loadLogsData()
            ]);
        }

        // 加载持仓数据
        async function loadHoldingsData() {
            try {
                const mode = currentMode === 'learning' ? 'learning' : 'automation';
                const response = await axios.get(`${API_BASE}/frontend/holdings/${mode}`);

                if (response.data.success) {
                    const data = response.data.data;
                    updateHoldingsTable(data.holdings);
                    updateSummaryStats(data.summary);

                    // 更新数据源信息
                    console.log(`数据源: ${data.data_source}`);
                } else {
                    throw new Error(response.data.message);
                }

            } catch (error) {
                console.error('加载持仓数据失败:', error);
                // 降级到模拟数据
                const mockData = generateMockHoldingsData();
                updateHoldingsTable(mockData);
                updateSummaryStats(mockData);
            }
        }

        // 加载统计数据
        async function loadStatisticsData() {
            try {
                const endpoint = currentMode === 'learning' ? 'virtual' : 'real';
                const response = await axios.get(`${API_BASE}/statistics/${endpoint}/daily`);
                
                if (response.data.success) {
                    const stats = response.data.data.statistics;
                    updateStatistics(stats);
                    updateChart(stats);
                }
                
            } catch (error) {
                console.error('加载统计数据失败:', error);
                // 使用模拟数据
                const mockStats = generateMockStatistics();
                updateStatistics(mockStats);
                updateChart(mockStats);
            }
        }

        // 加载日志数据
        async function loadLogsData() {
            try {
                const mode = currentMode === 'learning' ? 'learning' : 'automation';
                const response = await axios.get(`${API_BASE}/frontend/logs/${mode}?limit=15`);

                if (response.data.success) {
                    const logs = response.data.data.logs;
                    updateLogsDisplay(logs);
                } else {
                    throw new Error(response.data.message);
                }

            } catch (error) {
                console.error('加载日志数据失败:', error);
                // 降级到模拟数据
                const mockLogs = generateMockLogs();
                updateLogsDisplay(mockLogs);
            }
        }

        // 生成模拟持仓数据
        function generateMockHoldingsData() {
            const stocks = [
                { code: '000001', name: '平安银行', quantity: 1000, cost: currentMode === 'learning' ? 51.79 : 11.84 },
                { code: '000002', name: '万科A', quantity: 500, cost: currentMode === 'learning' ? 28.45 : 12.56 },
                { code: '600036', name: '招商银行', quantity: 800, cost: currentMode === 'learning' ? 45.23 : 35.67 }
            ];
            
            return stocks.map(stock => {
                const currentPrice = stock.cost * (0.95 + Math.random() * 0.1); // ±5%波动
                const holdingAmount = stock.quantity * currentPrice;
                const pnlAmount = (currentPrice - stock.cost) * stock.quantity;
                const pnlRatio = (currentPrice - stock.cost) / stock.cost * 100;
                
                return {
                    ...stock,
                    currentPrice: currentPrice.toFixed(2),
                    holdingAmount: holdingAmount.toFixed(2),
                    pnlAmount: pnlAmount.toFixed(2),
                    pnlRatio: pnlRatio.toFixed(2),
                    tradeDate: new Date().toLocaleDateString()
                };
            });
        }

        // 生成模拟统计数据
        function generateMockStatistics() {
            return {
                total_trades: Math.floor(Math.random() * 50) + 10,
                win_rate: (Math.random() * 0.4 + 0.4).toFixed(2), // 40%-80%
                net_profit: (Math.random() * 10000 - 5000).toFixed(2),
                total_return: (Math.random() * 20000).toFixed(2)
            };
        }

        // 生成模拟日志数据
        function generateMockLogs() {
            const logTypes = ['analysis', 'discussion', 'execution'];
            const messages = {
                analysis: [
                    '📊 市场分析：检测到000001平安银行技术指标RSI超买，建议减仓',
                    '🔍 数据分析：当前市场流动性充足，适合大额交易执行',
                    '📈 趋势分析：沪深300指数呈现上升趋势，建议增加权重股配置'
                ],
                discussion: [
                    '🤝 四星辩论：天玑星提出风险警告，当前市场波动率较高',
                    '💬 协作讨论：与天枢星确认数据源，东方财富API响应正常',
                    '🎯 策略讨论：天璇星建议采用VWAP算法执行大额订单'
                ],
                execution: [
                    '⚡ 交易执行：成功买入000001平安银行1000股，执行价格¥11.84',
                    '✅ 订单完成：虚拟交易VT_93e0a5bd执行完毕，盈亏+¥156.78',
                    '🎮 学习模式：策略验证完成，胜率提升至68.5%'
                ]
            };
            
            const logs = [];
            for (let i = 0; i < 10; i++) {
                const type = logTypes[Math.floor(Math.random() * logTypes.length)];
                const messageList = messages[type];
                const message = messageList[Math.floor(Math.random() * messageList.length)];
                
                logs.push({
                    type: type,
                    timestamp: new Date(Date.now() - Math.random() * 3600000).toLocaleString(),
                    content: message
                });
            }
            
            return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        }

        // 更新持仓表格
        function updateHoldingsTable(data) {
            const tbody = document.getElementById('holdings-table');

            if (!data || data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; color: #666;">暂无持仓数据</td></tr>';
                return;
            }

            tbody.innerHTML = data.map(item => `
                <tr>
                    <td>${item.stock_code || item.code}</td>
                    <td>${item.stock_name || item.name}</td>
                    <td>${item.quantity}</td>
                    <td>¥${(item.cost_price || item.cost).toFixed(2)}</td>
                    <td>¥${(item.current_price || item.currentPrice).toFixed(2)}</td>
                    <td>¥${(item.holding_amount || item.holdingAmount).toFixed(2)}</td>
                    <td class="${(item.pnl_amount || parseFloat(item.pnlAmount)) >= 0 ? 'profit' : 'loss'}">¥${(item.pnl_amount || item.pnlAmount).toFixed(2)}</td>
                    <td class="${(item.pnl_ratio || parseFloat(item.pnlRatio)) >= 0 ? 'profit' : 'loss'}">${(item.pnl_ratio || item.pnlRatio).toFixed(2)}%</td>
                    <td>${item.trade_date || item.tradeDate}</td>
                </tr>
            `).join('');
        }

        // 更新汇总统计
        function updateSummaryStats(data) {
            let totalAmount, totalPnl;

            if (data.total_amount !== undefined) {
                // API返回的汇总数据
                totalAmount = data.total_amount;
                totalPnl = data.total_pnl;
            } else {
                // 从持仓数据计算
                totalAmount = data.reduce((sum, item) => sum + parseFloat(item.holding_amount || item.holdingAmount), 0);
                totalPnl = data.reduce((sum, item) => sum + parseFloat(item.pnl_amount || item.pnlAmount), 0);
            }

            document.getElementById('total-amount').textContent = `¥${totalAmount.toFixed(2)}`;
            document.getElementById('total-pnl').textContent = `¥${totalPnl.toFixed(2)}`;
            document.getElementById('total-pnl').className = totalPnl >= 0 ? 'value profit' : 'value loss';
        }

        // 更新统计数据
        function updateStatistics(stats) {
            document.getElementById('win-rate').textContent = `${(stats.win_rate * 100).toFixed(1)}%`;
            document.getElementById('total-trades').textContent = stats.total_trades;
        }

        // 更新日志显示
        function updateLogsDisplay(logs) {
            const container = document.getElementById('logs-container');

            if (!logs || logs.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666;">暂无日志数据</div>';
                return;
            }

            container.innerHTML = logs.map(log => `
                <div class="log-entry ${log.type}">
                    <div class="timestamp">
                        ${log.timestamp}
                        ${log.mode_tag ? `<span style="margin-left: 10px; padding: 2px 8px; background: #007bff; color: white; border-radius: 12px; font-size: 0.8em;">${log.mode_tag}</span>` : ''}
                    </div>
                    <div class="content">${log.content}</div>
                    ${log.source ? `<div style="font-size: 0.8em; color: #888; margin-top: 5px;">来源: ${log.source}</div>` : ''}
                </div>
            `).join('');
        }

        // 初始化图表
        function initializeChart() {
            const ctx = document.getElementById('pnlChart').getContext('2d');
            pnlChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '盈亏趋势',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toFixed(2);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // 更新图表
        function updateChart(stats) {
            // 生成模拟的历史数据
            const labels = [];
            const data = [];
            const baseValue = parseFloat(stats.net_profit) || 0;
            
            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString());
                
                const value = baseValue * (0.7 + Math.random() * 0.6);
                data.push(value);
            }
            
            pnlChart.data.labels = labels;
            pnlChart.data.datasets[0].data = data;
            pnlChart.update();
        }
    </script>
</body>
</html>
