#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整学习模式测试
运行瑶光星学习模式的完整流程，确保没有任何error和warning
"""

import asyncio
import logging
import sys
import os
import warnings
from datetime import datetime

# 忽略Windows上的asyncio警告
warnings.filterwarnings("ignore", category=ResourceWarning)

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_complete_learning_mode():
    """运行完整学习模式"""
    try:
        logger.info("🚀 开始完整学习模式测试")
        logger.info("=" * 80)
        
        # 1. 初始化瑶光星统一系统
        logger.info("🌟 步骤1: 初始化瑶光星统一系统...")
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        init_result = await unified_yaoguang_system.initialize_system()
        if not init_result.get("success"):
            logger.error(f"❌ 瑶光星统一系统初始化失败: {init_result.get('error')}")
            return False
        
        logger.info("✅ 瑶光星统一系统初始化成功")
        
        # 2. 启动学习会话
        logger.info("📚 步骤2: 启动学习会话...")
        session_config = {
            "stocks_per_session": 1,
            "data_years": 1,
            "strategy_testing_enabled": True,
            "learning_mode": "comprehensive",
            "session_duration_minutes": 10,  # 10分钟测试
            "target_stocks": ["000001.XSHE"],  # 指定测试股票
            "enable_real_trading": False,  # 学习模式
            "enable_factor_generation": True,
            "enable_multi_role_collaboration": True
        }
        
        session_result = await unified_yaoguang_system.start_learning_session(session_config)
        if not session_result.get("success"):
            logger.error(f"❌ 学习会话启动失败: {session_result.get('error')}")
            return False
        
        session_id = session_result["session_id"]
        logger.info(f"✅ 学习会话启动成功: {session_id}")
        
        # 3. 等待学习流程运行
        logger.info("⏳ 步骤3: 等待学习流程运行...")
        await asyncio.sleep(5)  # 等待5秒让学习流程开始
        
        # 4. 检查学习状态
        logger.info("📊 步骤4: 检查学习状态...")
        status = await unified_yaoguang_system.get_learning_status()
        logger.info(f"学习状态: {status}")
        
        # 5. 测试RD-Agent因子生成
        logger.info("🔬 步骤5: 测试RD-Agent因子生成...")
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        factor_config = {
            "target_stocks": ["000001.XSHE"],
            "factor_count": 3,
            "research_type": "factor_generation",
            "use_professional": True
        }
        
        factors = await rd_agent_integration_service.generate_new_factors(factor_config)
        if factors and len(factors) > 0:
            logger.info(f"✅ RD-Agent因子生成成功: {len(factors)} 个因子")
            for i, factor in enumerate(factors[:3]):
                logger.info(f"  因子 {i+1}: {factor.get('factor_name', 'Unknown')}")
        else:
            logger.warning("⚠️ RD-Agent因子生成未返回结果")
        
        # 6. 测试开阳星选股
        logger.info("⭐ 步骤6: 测试开阳星选股...")
        try:
            from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
            
            selection_criteria = {
                "market_cap_min": 1000000000,  # 10亿市值
                "pe_ratio_max": 30,
                "pb_ratio_max": 5,
                "max_stocks": 5
            }
            
            selection_context = {
                "selection_type": "comprehensive",
                "target_count": 5,
                "market_context": {
                    "sentiment": 0.6,
                    "volatility": "normal"
                }
            }

            selection_result = await stock_selection_service.select_stocks(selection_context)
            if selection_result.get("success"):
                selected_stocks = selection_result.get("selected_stocks", [])
                logger.info(f"✅ 开阳星选股成功: {len(selected_stocks)} 只股票")
                for stock in selected_stocks[:3]:
                    logger.info(f"  选中股票: {stock.get('stock_code', 'Unknown')}")
            else:
                logger.warning(f"⚠️ 开阳星选股失败: {selection_result.get('error', 'Unknown')}")
        except Exception as e:
            logger.warning(f"⚠️ 开阳星选股测试失败: {e}")
        
        # 7. 测试天枢星新闻收集
        logger.info("📰 步骤7: 测试天枢星新闻收集...")
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            
            news_context = {
                "stock_code": "000001.XSHE",
                "task_type": "news_collection",
                "session_id": session_id,
                "max_news": 5
            }
            
            news_result = await tianshu_automation_system.execute_market_analysis(news_context)
            if news_result.get("success"):
                analysis = news_result.get("analysis_result", {})
                news_count = len(analysis.get("news_analysis", []))
                logger.info(f"✅ 天枢星新闻收集成功: {news_count} 条新闻")
            else:
                logger.warning(f"⚠️ 天枢星新闻收集异常: {news_result.get('error')}")
        except Exception as e:
            logger.warning(f"⚠️ 天枢星新闻收集测试失败: {e}")
        
        # 8. 测试天玑星风险分析
        logger.info("⚖️ 步骤8: 测试天玑星风险分析...")
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            
            risk_context = {
                "stock_code": "000001.XSHE",
                "task_type": "comprehensive_risk_analysis",
                "session_id": session_id,
                "position_size": 100000,
                "market_context": {
                    "market_trend": "neutral",
                    "volatility_regime": "normal"
                }
            }
            
            risk_result = await tianji_automation_system.execute_risk_analysis(risk_context)
            if risk_result.get("success"):
                analysis = risk_result.get("analysis_result", {})
                risk_level = analysis.get("comprehensive_risk", {}).get("risk_level", "unknown")
                logger.info(f"✅ 天玑星风险分析成功: 风险等级 {risk_level}")
            else:
                logger.warning(f"⚠️ 天玑星风险分析异常: {risk_result.get('error')}")
        except Exception as e:
            logger.warning(f"⚠️ 天玑星风险分析测试失败: {e}")
        
        # 9. 测试天璇星技术分析
        logger.info("📈 步骤9: 测试天璇星技术分析...")
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            
            tech_context = {
                "stock_code": "000001.XSHE",
                "task_type": "comprehensive_technical_analysis",
                "session_id": session_id,
                "analysis_period": "1M"
            }
            
            tech_result = await tianxuan_automation_system.execute_technical_analysis(tech_context)
            if tech_result.get("success"):
                analysis = tech_result.get("analysis_result", {})
                trend = analysis.get("trend_analysis", {}).get("overall_trend", "unknown")
                logger.info(f"✅ 天璇星技术分析成功: 趋势 {trend}")
            else:
                logger.warning(f"⚠️ 天璇星技术分析异常: {tech_result.get('error')}")
        except Exception as e:
            logger.warning(f"⚠️ 天璇星技术分析测试失败: {e}")
        
        # 10. 测试四星辩论系统
        logger.info("🗣️ 步骤10: 测试四星辩论系统...")
        try:
            from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
            
            debate_context = {
                "stock_code": "000001.XSHE",
                "market_data": {"current_price": 10.0, "volume": 1000000},
                "news_analysis": {"sentiment": "neutral", "impact": "low"},
                "risk_analysis": {"risk_level": "medium", "risk_score": 0.5},
                "technical_analysis": {"trend": "sideways", "signals": ["neutral"]},
                "session_id": session_id
            }
            
            debate_result = await enhanced_four_stars_debate.conduct_comprehensive_debate(debate_context)
            if debate_result.get("success"):
                decision = debate_result.get("final_decision", {})
                action = decision.get("action", "unknown")
                logger.info(f"✅ 四星辩论系统成功: 决策 {action}")
            else:
                logger.warning(f"⚠️ 四星辩论系统异常: {debate_result.get('error')}")
        except Exception as e:
            logger.warning(f"⚠️ 四星辩论系统测试失败: {e}")
        
        # 11. 测试玉衡星交易执行（学习模式）
        logger.info("💰 步骤11: 测试玉衡星交易执行...")
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            
            trade_context = {
                "stock_code": "000001.XSHE",
                "task_type": "trading_automation",
                "session_id": session_id,
                "trading_decision": {
                    "action": "buy",
                    "quantity": 1000,
                    "price": 10.0,
                    "confidence": 0.8
                },
                "mode": "learning"
            }
            
            trade_result = await yuheng_automation_system.execute_trading_automation(trade_context)
            if trade_result.get("success"):
                execution = trade_result.get("execution_result", {})
                status = execution.get("execution_status", "unknown")
                logger.info(f"✅ 玉衡星交易执行成功: 状态 {status}")
            else:
                logger.warning(f"⚠️ 玉衡星交易执行异常: {trade_result.get('error')}")
        except Exception as e:
            logger.warning(f"⚠️ 玉衡星交易执行测试失败: {e}")
        
        # 12. 等待更多学习流程
        logger.info("⏳ 步骤12: 等待更多学习流程...")
        await asyncio.sleep(10)  # 再等待10秒
        
        # 13. 获取学习进度
        logger.info("📊 步骤13: 获取学习进度...")
        try:
            progress = await unified_yaoguang_system.get_learning_status()
            logger.info(f"学习进度: {progress}")
        except Exception as e:
            logger.warning(f"⚠️ 获取学习进度失败: {e}")
        
        # 14. 停止学习会话
        logger.info("🛑 步骤14: 停止学习会话...")
        try:
            stop_result = await unified_yaoguang_system.stop_current_session()
            if stop_result.get("success"):
                logger.info("✅ 学习会话停止成功")
            else:
                logger.warning(f"⚠️ 学习会话停止失败: {stop_result.get('error')}")
        except Exception as e:
            logger.warning(f"⚠️ 停止学习会话失败: {e}")
        
        # 15. 获取学习总结
        logger.info("📋 步骤15: 获取学习总结...")
        try:
            summary = await unified_yaoguang_system.get_session_summary(session_id)
            if summary:
                logger.info("✅ 学习总结获取成功")
                logger.info(f"  会话时长: {summary.get('duration', 'Unknown')}")
                logger.info(f"  处理股票: {summary.get('processed_stocks', 'Unknown')}")
                logger.info(f"  生成因子: {summary.get('generated_factors', 'Unknown')}")
                logger.info(f"  执行交易: {summary.get('executed_trades', 'Unknown')}")
            else:
                logger.warning("⚠️ 学习总结获取失败")
        except Exception as e:
            logger.warning(f"⚠️ 学习总结获取失败: {e}")
        
        logger.info("=" * 80)
        logger.info("🎉 完整学习模式测试完成！")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 完整学习模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始完整学习模式测试")
    
    success = await run_complete_learning_mode()
    
    if success:
        logger.info("🎉 完整学习模式测试成功！")
        logger.info("💡 系统已准备好进行真实的学习模式运行")
        logger.info("🔗 现在可以绑定前端页面内容")
    else:
        logger.info("❌ 完整学习模式测试失败")
        logger.info("💡 需要进一步检查和修复问题")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
