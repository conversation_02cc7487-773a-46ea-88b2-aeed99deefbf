#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星学习自动化完整测试
测试8个阶段的完整学习流程
"""

import asyncio
import sys
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_yaoguang_learning_automation():
    """测试瑶光星学习自动化的完整8阶段流程"""
    try:
        print('🌟 开始测试瑶光星学习自动化系统')
        print('=' * 60)
        
        # 导入瑶光星统一系统
        print('📦 导入瑶光星统一系统...')
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 阶段1：系统初始化
        print('\n🔧 阶段1：初始化瑶光星统一系统...')
        init_result = await unified_yaoguang_system.initialize_system()
        print(f'初始化结果: {init_result.get("success", False)}')
        
        if not init_result.get('success'):
            print(f'❌ 系统初始化失败: {init_result.get("error")}')
            return False
        
        print('✅ 瑶光星统一系统初始化成功')
        
        # 获取系统状态
        print('\n📊 获取系统状态...')
        status = await unified_yaoguang_system.get_system_status()
        system_info = status.get('system_info', {})
        print(f'系统名称: {system_info.get("name")}')
        print(f'系统版本: {system_info.get("version")}')
        print(f'系统激活: {system_info.get("is_active")}')
        
        # 阶段2：启动学习自动化
        print('\n🚀 阶段2：启动瑶光星学习自动化...')
        learning_config = {
            'stocks_per_session': 1,  # 学习模式只选择1只股票
            'data_years': 5,
            'strategy_testing_enabled': True,
            'four_stars_debate_enabled': True
        }
        
        learning_result = await unified_yaoguang_system.start_learning_session(learning_config)
        print(f'学习会话启动: {learning_result.get("success", False)}')
        
        if not learning_result.get('success'):
            print(f'❌ 学习会话启动失败: {learning_result.get("error")}')
            return False
        
        session_id = learning_result.get('session_id')
        print(f'✅ 学习会话已启动: {session_id}')
        
        # 阶段3-8：监控学习进度
        print('\n👀 阶段3-8：监控学习自动化进度...')
        print('监控8个学习阶段的执行情况：')
        print('  1. 瑶光发起学习 ✅')
        print('  2. 练习阶段 - 多角色配合')
        print('  3. 研究阶段 - 反思分析')
        print('  4. 因子研发流程')
        print('  5. 模型训练流程')
        print('  6. 策略生成流程')
        print('  7. 回测验证流程')
        print('  8. 技能库上传')
        
        # 监控学习进度
        max_monitoring_cycles = 60  # 最多监控60次，每次10秒
        monitoring_interval = 10
        
        for cycle in range(max_monitoring_cycles):
            await asyncio.sleep(monitoring_interval)
            
            # 获取当前状态
            current_status = await unified_yaoguang_system.get_system_status()
            current_session_id = current_status.get('system_info', {}).get('current_session')
            
            if current_session_id:
                session_obj = unified_yaoguang_system.current_session
                if session_obj:
                    progress = session_obj.get('progress', {})
                    current_step = progress.get('current_step', '未知')
                    processed_stocks = progress.get('processed_stocks', 0)
                    total_stocks = progress.get('total_stocks', 0)
                    status_value = session_obj.get('status', 'unknown')
                    
                    print(f'\n📈 监控周期 [{cycle+1}/{max_monitoring_cycles}]:')
                    print(f'   当前阶段: {current_step}')
                    print(f'   处理进度: {processed_stocks}/{total_stocks} 股票')
                    print(f'   会话状态: {status_value}')
                    
                    # 显示详细结果
                    results = session_obj.get('results', {})
                    if results:
                        print(f'   已完成组件:')
                        if results.get('selected_stocks'):
                            print(f'     ✅ 开阳星选股: {len(results["selected_stocks"])} 只')
                        if results.get('data_collection'):
                            print(f'     ✅ 瑶光星数据收集: 完成')
                        if results.get('strategy_testing'):
                            print(f'     ✅ 天权星战法测试: 完成')
                        if results.get('debate_results'):
                            print(f'     ✅ 四星深度辩论: 完成')
                        if results.get('learning_optimization'):
                            print(f'     ✅ 瑶光星学习优化: 完成')
                        if results.get('factor_research'):
                            print(f'     ✅ RD-Agent因子研究: 完成')
                    
                    # 检查是否完成
                    if status_value == 'completed':
                        print('\n🎉 学习自动化流程成功完成!')
                        print('=' * 60)
                        print('📊 最终学习结果摘要:')
                        
                        # 详细结果分析
                        selected_stocks = results.get('selected_stocks', [])
                        print(f'  📈 选择股票数量: {len(selected_stocks)}')
                        if selected_stocks:
                            print(f'     股票代码: {", ".join(selected_stocks[:5])}')
                        
                        data_collection = results.get('data_collection', {})
                        if data_collection:
                            print(f'  📊 数据收集: 成功')
                            print(f'     数据源: {data_collection.get("data_source", "未知")}')
                            print(f'     成功股票: {len(data_collection.get("successful_stocks", []))}')
                        
                        strategy_testing = results.get('strategy_testing', {})
                        if strategy_testing:
                            print(f'  🎯 战法测试: 成功')
                            print(f'     测试数量: {strategy_testing.get("total_tested", 0)}')
                            best_strategy = strategy_testing.get('best_strategy')
                            if best_strategy:
                                print(f'     最佳战法: {best_strategy.get("strategy_type")} (收益: {best_strategy.get("total_return", 0):.2%})')
                        
                        debate_results = results.get('debate_results', {})
                        if debate_results:
                            print(f'  🗣️ 四星辩论: 成功')
                            print(f'     达成共识: {debate_results.get("consensus_reached", False)}')
                            print(f'     辩论结论: {debate_results.get("debate_conclusion", "")[:50]}...')
                        
                        learning_optimization = results.get('learning_optimization', {})
                        if learning_optimization:
                            print(f'  🧠 学习优化: 成功')
                            print(f'     优化股票: {learning_optimization.get("total_optimized", 0)}')
                            print(f'     性能提升: {learning_optimization.get("performance_improvement", 0):.2%}')
                        
                        factor_research = results.get('factor_research', {})
                        if factor_research:
                            print(f'  🔬 因子研究: 成功')
                            print(f'     新因子数量: {factor_research.get("total_new_factors", 0)}')
                            print(f'     Alpha158因子: {len(factor_research.get("alpha158_factors", []))}')
                        
                        print('\n✅ 瑶光星学习自动化8阶段流程全部完成!')
                        return True
                        
                    elif status_value == 'failed':
                        print(f'\n❌ 学习自动化流程失败!')
                        error = session_obj.get('error', '未知错误')
                        print(f'错误信息: {error}')
                        return False
                        
                else:
                    print(f'⚠️ 监控周期 [{cycle+1}]: 当前会话对象为空')
            else:
                print(f'⚠️ 监控周期 [{cycle+1}]: 没有活跃的学习会话')
                break
        
        print('\n⏰ 监控超时，学习流程可能仍在进行中')
        print('建议稍后检查系统状态或查看日志')
        return False
        
    except Exception as e:
        print(f'\n❌ 测试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print('🌟 瑶光星学习自动化完整测试')
    print(f'测试时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print('=' * 60)
    
    success = await test_yaoguang_learning_automation()
    
    print('\n' + '=' * 60)
    if success:
        print('🎉 测试成功完成! 瑶光星学习自动化系统运行正常')
    else:
        print('❌ 测试未完全成功，需要进一步检查和修复')
    
    print(f'测试结束时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

if __name__ == "__main__":
    asyncio.run(main())
