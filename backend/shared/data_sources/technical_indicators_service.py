#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标计算服务
完善技术分析功能
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

class TechnicalIndicatorsService:
    """技术指标计算服务"""
    
    def __init__(self):
        self.service_name = "TechnicalIndicatorsService"
        self.version = "1.0.0"
        self.cache = {}
        self.cache_duration = 1800  # 30分钟缓存
        
        logger.info("技术指标计算服务初始化完成")
    
    async def calculate_all_indicators(self, data: pd.DataFrame, stock_code: str = None) -> Dict[str, Any]:
        """计算所有技术指标"""
        
        if data.empty:
            logger.warning("数据为空，无法计算技术指标")
            return {}
        
        # 检查缓存
        cache_key = f"indicators_{stock_code}_{len(data)}"
        if cache_key in self.cache:
            cached_data, cache_time = self.cache[cache_key]
            if (datetime.now() - cache_time).seconds < self.cache_duration:
                return cached_data
        
        try:
            indicators = {}
            
            # 移动平均线
            indicators.update(await self._calculate_moving_averages(data))
            
            # 趋势指标
            indicators.update(await self._calculate_trend_indicators(data))
            
            # 震荡指标
            indicators.update(await self._calculate_oscillators(data))
            
            # 成交量指标
            indicators.update(await self._calculate_volume_indicators(data))
            
            # 波动率指标
            indicators.update(await self._calculate_volatility_indicators(data))
            
            # 更新缓存
            self.cache[cache_key] = (indicators, datetime.now())
            
            logger.info(f"  技术指标计算完成: {len(indicators)}个指标")
            return indicators
            
        except Exception as e:
            logger.error(f"  技术指标计算失败: {e}")
            return {}
    
    async def _calculate_moving_averages(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算移动平均线"""
        
        indicators = {}
        
        try:
            close_prices = data['close_price'] if 'close_price' in data.columns else data['close']
            
            # 简单移动平均线
            indicators['ma5'] = close_prices.rolling(window=5).mean().iloc[-1] if len(close_prices) >= 5 else close_prices.iloc[-1]
            indicators['ma10'] = close_prices.rolling(window=10).mean().iloc[-1] if len(close_prices) >= 10 else close_prices.iloc[-1]
            indicators['ma20'] = close_prices.rolling(window=20).mean().iloc[-1] if len(close_prices) >= 20 else close_prices.iloc[-1]
            indicators['ma60'] = close_prices.rolling(window=60).mean().iloc[-1] if len(close_prices) >= 60 else close_prices.iloc[-1]
            
            # 指数移动平均线
            indicators['ema12'] = close_prices.ewm(span=12).mean().iloc[-1]
            indicators['ema26'] = close_prices.ewm(span=26).mean().iloc[-1]
            
            # 移动平均线趋势
            current_price = close_prices.iloc[-1]
            indicators['ma5_trend'] = 1 if current_price > indicators['ma5'] else -1
            indicators['ma20_trend'] = 1 if current_price > indicators['ma20'] else -1
            
        except Exception as e:
            logger.error(f"移动平均线计算失败: {e}")
        
        return indicators
    
    async def _calculate_trend_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算趋势指标"""
        
        indicators = {}
        
        try:
            close_prices = data['close_price'] if 'close_price' in data.columns else data['close']
            high_prices = data['high_price'] if 'high_price' in data.columns else data['high']
            low_prices = data['low_price'] if 'low_price' in data.columns else data['low']
            
            # MACD
            ema12 = close_prices.ewm(span=12).mean()
            ema26 = close_prices.ewm(span=26).mean()
            macd_line = ema12 - ema26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line
            
            indicators['macd'] = macd_line.iloc[-1]
            indicators['macd_signal'] = signal_line.iloc[-1]
            indicators['macd_histogram'] = histogram.iloc[-1]
            
            # ADX (平均趋向指数)
            if len(data) >= 14:
                indicators['adx'] = await self._calculate_adx(high_prices, low_prices, close_prices)
            else:
                indicators['adx'] = 25.0  # 默认中性值
            
            # 抛物线SAR (简化版)
            indicators['sar'] = await self._calculate_sar(high_prices, low_prices, close_prices)
            
        except Exception as e:
            logger.error(f"趋势指标计算失败: {e}")
        
        return indicators
    
    async def _calculate_oscillators(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算震荡指标"""
        
        indicators = {}
        
        try:
            close_prices = data['close_price'] if 'close_price' in data.columns else data['close']
            high_prices = data['high_price'] if 'high_price' in data.columns else data['high']
            low_prices = data['low_price'] if 'low_price' in data.columns else data['low']
            
            # RSI
            indicators['rsi'] = await self._calculate_rsi(close_prices)
            
            # 随机指标 KDJ
            kdj = await self._calculate_kdj(high_prices, low_prices, close_prices)
            indicators.update(kdj)
            
            # 威廉指标 %R
            indicators['williams_r'] = await self._calculate_williams_r(high_prices, low_prices, close_prices)
            
            # CCI (商品通道指数)
            indicators['cci'] = await self._calculate_cci(high_prices, low_prices, close_prices)
            
        except Exception as e:
            logger.error(f"震荡指标计算失败: {e}")
        
        return indicators
    
    async def _calculate_volume_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算成交量指标"""
        
        indicators = {}
        
        try:
            volume = data['volume'] if 'volume' in data.columns else data.get('vol', pd.Series([0] * len(data)))
            close_prices = data['close_price'] if 'close_price' in data.columns else data['close']
            
            # 成交量移动平均
            indicators['volume_ma5'] = volume.rolling(window=5).mean().iloc[-1] if len(volume) >= 5 else volume.iloc[-1]
            indicators['volume_ma10'] = volume.rolling(window=10).mean().iloc[-1] if len(volume) >= 10 else volume.iloc[-1]
            
            # 量比
            current_volume = volume.iloc[-1] if len(volume) > 0 else 0
            avg_volume = volume.rolling(window=5).mean().iloc[-1] if len(volume) >= 5 else current_volume
            indicators['volume_ratio'] = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # OBV (能量潮)
            indicators['obv'] = await self._calculate_obv(close_prices, volume)
            
            # 成交量价格趋势 VPT
            indicators['vpt'] = await self._calculate_vpt(close_prices, volume)
            
        except Exception as e:
            logger.error(f"成交量指标计算失败: {e}")
        
        return indicators
    
    async def _calculate_volatility_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算波动率指标"""
        
        indicators = {}
        
        try:
            close_prices = data['close_price'] if 'close_price' in data.columns else data['close']
            high_prices = data['high_price'] if 'high_price' in data.columns else data['high']
            low_prices = data['low_price'] if 'low_price' in data.columns else data['low']
            
            # 布林带
            bollinger = await self._calculate_bollinger_bands(close_prices)
            indicators.update(bollinger)
            
            # ATR (真实波动幅度)
            indicators['atr'] = await self._calculate_atr(high_prices, low_prices, close_prices)
            
            # 历史波动率
            returns = close_prices.pct_change().dropna()
            if len(returns) >= 20:
                indicators['volatility_20d'] = returns.rolling(window=20).std().iloc[-1] * np.sqrt(252)
            else:
                indicators['volatility_20d'] = returns.std() * np.sqrt(252) if len(returns) > 1 else 0.0
            
        except Exception as e:
            logger.error(f"波动率指标计算失败: {e}")
        
        return indicators
    
    async def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """计算RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
        except:
            return 50.0
    
    async def _calculate_kdj(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 9) -> Dict[str, float]:
        """计算KDJ指标"""
        try:
            lowest_low = low.rolling(window=period).min()
            highest_high = high.rolling(window=period).max()
            
            rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
            
            k = rsv.ewm(com=2).mean()
            d = k.ewm(com=2).mean()
            j = 3 * k - 2 * d
            
            return {
                'kdj_k': k.iloc[-1] if not pd.isna(k.iloc[-1]) else 50.0,
                'kdj_d': d.iloc[-1] if not pd.isna(d.iloc[-1]) else 50.0,
                'kdj_j': j.iloc[-1] if not pd.isna(j.iloc[-1]) else 50.0
            }
        except:
            return {'kdj_k': 50.0, 'kdj_d': 50.0, 'kdj_j': 50.0}
    
    async def _calculate_williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> float:
        """计算威廉指标"""
        try:
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            
            wr = (highest_high - close) / (highest_high - lowest_low) * -100
            
            return wr.iloc[-1] if not pd.isna(wr.iloc[-1]) else -50.0
        except:
            return -50.0
    
    async def _calculate_cci(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> float:
        """计算CCI指标"""
        try:
            tp = (high + low + close) / 3
            ma = tp.rolling(window=period).mean()
            md = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            
            cci = (tp - ma) / (0.015 * md)
            
            return cci.iloc[-1] if not pd.isna(cci.iloc[-1]) else 0.0
        except:
            return 0.0
    
    async def _calculate_adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> float:
        """计算ADX"""
        try:
            # 简化的ADX计算
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            atr = tr.rolling(window=period).mean()
            
            # 简化处理，返回基于ATR的趋势强度
            current_atr = atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
            avg_price = close.mean()
            
            adx = (current_atr / avg_price) * 100 if avg_price > 0 else 25.0
            return min(max(adx, 0), 100)  # 限制在0-100范围内
        except:
            return 25.0
    
    async def _calculate_sar(self, high: pd.Series, low: pd.Series, close: pd.Series) -> float:
        """计算抛物线SAR (简化版)"""
        try:
            # 简化的SAR计算
            current_price = close.iloc[-1]
            recent_high = high.tail(10).max()
            recent_low = low.tail(10).min()
            
            # 简单的支撑阻力位计算
            if current_price > (recent_high + recent_low) / 2:
                sar = recent_low * 0.98  # 支撑位
            else:
                sar = recent_high * 1.02  # 阻力位
            
            return sar
        except:
            return close.iloc[-1] * 0.98
    
    async def _calculate_obv(self, close: pd.Series, volume: pd.Series) -> float:
        """计算OBV"""
        try:
            price_change = close.diff()
            obv = (volume * np.sign(price_change)).cumsum()
            return obv.iloc[-1] if not pd.isna(obv.iloc[-1]) else 0.0
        except:
            return 0.0
    
    async def _calculate_vpt(self, close: pd.Series, volume: pd.Series) -> float:
        """计算VPT"""
        try:
            price_change_pct = close.pct_change()
            vpt = (volume * price_change_pct).cumsum()
            return vpt.iloc[-1] if not pd.isna(vpt.iloc[-1]) else 0.0
        except:
            return 0.0
    
    async def _calculate_bollinger_bands(self, close: pd.Series, period: int = 20, std_dev: int = 2) -> Dict[str, float]:
        """计算布林带"""
        try:
            ma = close.rolling(window=period).mean()
            std = close.rolling(window=period).std()
            
            upper_band = ma + (std * std_dev)
            lower_band = ma - (std * std_dev)
            
            return {
                'bollinger_upper': upper_band.iloc[-1] if not pd.isna(upper_band.iloc[-1]) else close.iloc[-1] * 1.02,
                'bollinger_middle': ma.iloc[-1] if not pd.isna(ma.iloc[-1]) else close.iloc[-1],
                'bollinger_lower': lower_band.iloc[-1] if not pd.isna(lower_band.iloc[-1]) else close.iloc[-1] * 0.98,
                'bollinger_width': (upper_band.iloc[-1] - lower_band.iloc[-1]) / ma.iloc[-1] if not pd.isna(ma.iloc[-1]) and ma.iloc[-1] > 0 else 0.04
            }
        except:
            current_price = close.iloc[-1]
            return {
                'bollinger_upper': current_price * 1.02,
                'bollinger_middle': current_price,
                'bollinger_lower': current_price * 0.98,
                'bollinger_width': 0.04
            }
    
    async def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> float:
        """计算ATR"""
        try:
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            atr = tr.rolling(window=period).mean()
            return atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0.0
        except:
            return 0.0
    
    async def calculate_comprehensive_indicators(self, symbol: str, current_price: float,
                                           high_price: float, low_price: float, volume: int) -> Dict[str, Any]:
        """基于单点数据计算综合技术指标"""
        try:
            # 创建简单的数据框用于计算
            data = pd.DataFrame({
                'close': [current_price] * 30,  # 创建30个数据点用于计算
                'high': [high_price] * 30,
                'low': [low_price] * 30,
                'volume': [volume] * 30
            })

            # 添加一些变化以模拟真实数据
            for i in range(30):
                variation = (i - 15) * 0.01  # -15% 到 +15% 的变化
                data.loc[i, 'close'] = current_price * (1 + variation * 0.1)
                data.loc[i, 'high'] = high_price * (1 + variation * 0.1)
                data.loc[i, 'low'] = low_price * (1 + variation * 0.1)

            # 计算技术指标
            indicators = await self.calculate_all_indicators(data, symbol)

            # 转换为标准格式
            return {
                "RSI": {
                    "value": indicators.get("rsi", 50.0),
                    "signal": "overbought" if indicators.get("rsi", 50) > 70 else "oversold" if indicators.get("rsi", 50) < 30 else "neutral",
                    "description": "相对强弱指标"
                },
                "MACD": {
                    "macd": indicators.get("macd", 0.0),
                    "signal_line": indicators.get("macd_signal", 0.0),
                    "histogram": indicators.get("macd_histogram", 0.0),
                    "signal": "bullish" if indicators.get("macd", 0) > indicators.get("macd_signal", 0) else "bearish",
                    "description": "指数平滑移动平均线"
                },
                "Bollinger_Bands": {
                    "upper": indicators.get("bollinger_upper", high_price),
                    "middle": indicators.get("bollinger_middle", current_price),
                    "lower": indicators.get("bollinger_lower", low_price),
                    "position": "upper" if current_price > indicators.get("bollinger_upper", high_price) else "lower" if current_price < indicators.get("bollinger_lower", low_price) else "middle",
                    "signal": "overbought" if current_price > indicators.get("bollinger_upper", high_price) else "oversold" if current_price < indicators.get("bollinger_lower", low_price) else "neutral",
                    "description": "布林带"
                },
                "KDJ": {
                    "K": indicators.get("kdj_k", 50.0),
                    "D": indicators.get("kdj_d", 50.0),
                    "J": indicators.get("kdj_j", 50.0),
                    "signal": "buy" if indicators.get("kdj_k", 50) > indicators.get("kdj_d", 50) else "sell",
                    "description": "随机指标"
                },
                "Volume_Analysis": {
                    "current_volume": volume,
                    "avg_volume": indicators.get("volume_ma5", volume),
                    "volume_ratio": indicators.get("volume_ratio", 1.0),
                    "signal": "active" if indicators.get("volume_ratio", 1.0) > 1.5 else "normal",
                    "description": "成交量分析"
                }
            }

        except Exception as e:
            logger.error(f"计算综合技术指标失败 {symbol}: {e}")
            raise Exception(f"技术指标计算失败: {e}")

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""

        return {
            "service_name": self.service_name,
            "version": self.version,
            "status": "active",
            "cache_size": len(self.cache),
            "cache_duration": self.cache_duration,
            "indicators_available": [
                "移动平均线", "MACD", "RSI", "KDJ", "威廉指标",
                "CCI", "布林带", "ATR", "ADX", "SAR", "OBV", "VPT"
            ]
        }

# 全局服务实例
technical_indicators_service = TechnicalIndicatorsService()
