#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天玑星具体错误诊断
"""

import sys
import os
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tianji_imports():
    """测试天玑星的具体导入问题"""
    print("🔍 开始诊断天玑星自动化系统导入问题...")
    
    try:
        print("1. 测试风险评估服务...")
        from roles.tianji_star.services.risk_assessment_service import risk_assessment_service
        print("✅ 风险评估服务导入成功")
    except Exception as e:
        print(f"❌ 风险评估服务导入失败: {e}")
        traceback.print_exc()
    
    try:
        print("2. 测试风险知识库服务...")
        from roles.tianji_star.services.risk_knowledge_base_service import risk_knowledge_base_service
        print("✅ 风险知识库服务导入成功")
    except Exception as e:
        print(f"❌ 风险知识库服务导入失败: {e}")
        traceback.print_exc()
    
    try:
        print("3. 测试天玑星自动化系统...")
        from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
        print("✅ 天玑星自动化系统导入成功")
    except Exception as e:
        print(f"❌ 天玑星自动化系统导入失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_tianji_imports()
