#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试瑶光星修复效果
验证RD-Agent集成和多角色协作是否正常工作
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_rd_agent_integration():
    """测试RD-Agent集成服务"""
    try:
        logger.info("🔬 测试RD-Agent集成服务...")
        
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        # 测试服务状态
        status = rd_agent_integration_service.get_service_status()
        logger.info(f"RD-Agent服务状态: {status}")
        
        # 测试因子生成
        research_config = {
            "target_stocks": ["000001.XSHE", "000002.XSHE"],
            "factor_count": 5,
            "research_type": "factor_generation"
        }
        
        logger.info("开始测试因子生成...")
        factors = await rd_agent_integration_service.generate_new_factors(research_config)
        
        if factors:
            logger.info(f"✅ 因子生成成功: {len(factors)} 个因子")
            for i, factor in enumerate(factors[:3]):  # 显示前3个因子
                logger.info(f"  因子 {i+1}: {factor.get('factor_name', 'Unknown')} - {factor.get('factor_category', 'Unknown')}")
        else:
            logger.warning("⚠️ 因子生成返回空结果")
        
        # 测试Alpha158因子
        logger.info("测试Alpha158因子获取...")
        alpha158_factors = await rd_agent_integration_service.get_alpha158_factors()
        
        if alpha158_factors:
            logger.info(f"✅ Alpha158因子获取成功: {len(alpha158_factors)} 个因子")
        else:
            logger.warning("⚠️ Alpha158因子获取失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ RD-Agent集成测试失败: {e}")
        return False

async def test_unified_yaoguang_system():
    """测试瑶光星统一系统"""
    try:
        logger.info("🌟 测试瑶光星统一系统...")
        
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 测试系统初始化
        logger.info("初始化瑶光星统一系统...")
        init_result = await unified_yaoguang_system.initialize_system()
        
        if init_result.get("success"):
            logger.info("✅ 瑶光星统一系统初始化成功")
        else:
            logger.warning(f"⚠️ 瑶光星统一系统初始化失败: {init_result.get('error')}")
        
        # 测试系统状态
        status = await unified_yaoguang_system.get_system_status()
        if status.get("success"):
            logger.info("✅ 系统状态获取成功")
            logger.info(f"  系统版本: {status['system_info']['version']}")
            logger.info(f"  系统激活: {status['system_info']['is_active']}")
        else:
            logger.warning("⚠️ 系统状态获取失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 瑶光星统一系统测试失败: {e}")
        return False

async def test_learning_session():
    """测试学习会话"""
    try:
        logger.info("🎓 测试瑶光星学习会话...")
        
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 启动学习会话
        session_config = {
            "stocks_per_session": 2,
            "data_years": 1,  # 减少测试时间
            "strategy_testing_enabled": True
        }
        
        logger.info("启动学习会话...")
        session_result = await unified_yaoguang_system.start_learning_session(session_config)
        
        if session_result.get("success"):
            session_id = session_result["session_id"]
            logger.info(f"✅ 学习会话启动成功: {session_id}")
            
            # 等待一段时间让学习流程开始
            await asyncio.sleep(3)
            
            # 检查会话状态
            status = await unified_yaoguang_system.get_system_status()
            if status.get("success"):
                current_session = status["system_info"].get("current_session")
                if current_session:
                    logger.info(f"✅ 当前活跃会话: {current_session}")
                else:
                    logger.warning("⚠️ 没有检测到活跃会话")
            
            # 停止会话
            logger.info("停止学习会话...")
            stop_result = await unified_yaoguang_system.stop_current_session()
            if stop_result.get("success"):
                logger.info("✅ 学习会话停止成功")
            else:
                logger.warning("⚠️ 学习会话停止失败")
        else:
            logger.warning(f"⚠️ 学习会话启动失败: {session_result.get('error')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 学习会话测试失败: {e}")
        return False

async def test_multi_role_collaboration():
    """测试多角色协作"""
    try:
        logger.info("⭐ 测试多角色协作...")
        
        # 测试开阳星选股服务
        try:
            from roles.kaiyang_star.services.stock_selection_service import stock_selection_service
            logger.info("✅ 开阳星选股服务可用")
        except ImportError as e:
            logger.warning(f"⚠️ 开阳星选股服务不可用: {e}")
        
        # 测试天权星战法服务
        try:
            from roles.tianquan_star.services.advanced_strategy_adjustment_system import advanced_strategy_adjustment_system
            logger.info("✅ 天权星战法服务可用")
        except ImportError as e:
            logger.warning(f"⚠️ 天权星战法服务不可用: {e}")
        
        # 测试四星辩论系统
        try:
            from roles.yaoguang_star.services.real_debate_system import RealDebateSystem
            debate_system = RealDebateSystem()
            logger.info("✅ 四星辩论系统可用")
        except ImportError as e:
            logger.warning(f"⚠️ 四星辩论系统不可用: {e}")
        
        # 测试天枢星自动化系统
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            logger.info("✅ 天枢星自动化系统可用")
        except ImportError as e:
            logger.warning(f"⚠️ 天枢星自动化系统不可用: {e}")
        
        # 测试天玑星自动化系统
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            logger.info("✅ 天玑星自动化系统可用")
        except ImportError as e:
            logger.warning(f"⚠️ 天玑星自动化系统不可用: {e}")
        
        # 测试天璇星自动化系统
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            logger.info("✅ 天璇星自动化系统可用")
        except ImportError as e:
            logger.warning(f"⚠️ 天璇星自动化系统不可用: {e}")
        
        # 测试玉衡星自动化系统
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            logger.info("✅ 玉衡星自动化系统可用")
        except ImportError as e:
            logger.warning(f"⚠️ 玉衡星自动化系统不可用: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多角色协作测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始瑶光星修复效果测试")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 测试1：RD-Agent集成
    test_results["rd_agent_integration"] = await test_rd_agent_integration()
    
    # 测试2：统一系统
    test_results["unified_system"] = await test_unified_yaoguang_system()
    
    # 测试3：学习会话
    test_results["learning_session"] = await test_learning_session()
    
    # 测试4：多角色协作
    test_results["multi_role_collaboration"] = await test_multi_role_collaboration()
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 测试结果汇总:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"📈 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        logger.info("🎉 瑶光星修复效果良好！")
    elif success_rate >= 50:
        logger.info("⚠️ 瑶光星部分功能正常，需要进一步优化")
    else:
        logger.info("❌ 瑶光星需要更多修复工作")
    
    return success_rate

if __name__ == "__main__":
    asyncio.run(main())
