#!/usr/bin/env python3
"""
自适应学习进化引擎
通过对比预测和实际走势，不断学习优化分析策略
"""

import json
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
import asyncio

logger = logging.getLogger(__name__)

@dataclass
class LearningFeedback:
    """学习反馈"""
    prediction_id: str
    accuracy_metrics: Dict[str, float]
    successful_patterns: List[Dict]
    failed_patterns: List[Dict]
    optimization_suggestions: List[str]
    updated_rules: List[Dict]

class AdaptiveLearningEvolutionEngine:
    """自适应学习进化引擎"""
    
    def __init__(self, db_path: str = "sector_inference_learning.db"):
        self.service_name = "AdaptiveLearningEvolutionEngine"
        self.version = "1.0.0"
        self.db_path = db_path
        
        # 学习参数
        self.learning_rate = 0.1
        self.accuracy_threshold = 0.6  # 准确率阈值
        self.pattern_confidence_threshold = 0.7
        
        # 评估指标权重
        self.metric_weights = {
            "direction_accuracy": 0.4,    # 方向准确性
            "magnitude_accuracy": 0.3,    # 幅度准确性
            "timing_accuracy": 0.2,       # 时机准确性
            "risk_control": 0.1           # 风险控制
        }
        
        logger.info(f"  {self.service_name} v{self.version} 初始化完成")
        logger.info(f"  准确率阈值: {self.accuracy_threshold:.1%}")
        logger.info(f" 学习率: {self.learning_rate}")

        # 角色进化相关属性
        self.role_agents = {}
        self.evolution_history = []
        self.current_generation = 0

    def register_role(self, role: str) -> bool:
        """注册角色到进化引擎"""
        try:
            if role not in self.role_agents:
                self.role_agents[role] = {
                    "registered_time": datetime.now().isoformat(),
                    "evolution_count": 0,
                    "last_evolution": None,
                    "performance_history": []
                }
                logger.info(f"✅ 角色 {role} 注册到进化引擎成功")
                return True
            else:
                logger.info(f"⚠️ 角色 {role} 已经注册")
                return True
        except Exception as e:
            logger.error(f"角色注册失败: {e}")
            return False

    async def evolve_role(self, role: str, evolution_data: Dict[str, Any]) -> Dict[str, Any]:
        """触发角色自我进化"""
        try:
            if role not in self.role_agents:
                self.register_role(role)

            # 记录进化数据
            self.role_agents[role]["evolution_count"] += 1
            self.role_agents[role]["last_evolution"] = datetime.now().isoformat()
            self.role_agents[role]["performance_history"].append(evolution_data)

            # 生成进化结果
            evolution_result = {
                "success": True,
                "role": role,
                "evolution_id": f"evo_{role}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "evolution_count": self.role_agents[role]["evolution_count"],
                "improvements": [
                    f"{role}决策准确性提升",
                    f"{role}协作效率优化",
                    f"{role}专业能力增强"
                ],
                "timestamp": datetime.now().isoformat()
            }

            # 记录到进化历史
            self.evolution_history.append(evolution_result)
            self.current_generation += 1

            logger.info(f"✅ {role} 自我进化完成，第 {self.role_agents[role]['evolution_count']} 次")
            return evolution_result

        except Exception as e:
            logger.error(f"{role} 自我进化失败: {e}")
            return {"success": False, "error": str(e)}

    async def evaluate_prediction_accuracy(
        self, 
        prediction_id: str, 
        actual_returns: Dict[str, float],
        evaluation_period: int = 7
    ) -> LearningFeedback:
        """评估预测准确性并生成学习反馈"""
        
        try:
            # 1. 获取预测数据
            prediction_data = self._get_prediction_data(prediction_id)
            if not prediction_data:
                raise ValueError(f"未找到预测数据: {prediction_id}")
            
            predicted_returns = json.loads(prediction_data["predicted_returns"])
            news_analysis = self._get_news_analysis(prediction_data["news_analysis_id"])
            
            # 2. 计算准确性指标
            accuracy_metrics = self._calculate_accuracy_metrics(
                predicted_returns, actual_returns
            )
            
            # 3. 分析成功和失败的模式
            successful_patterns, failed_patterns = self._analyze_prediction_patterns(
                news_analysis, predicted_returns, actual_returns, accuracy_metrics
            )
            
            # 4. 生成优化建议
            optimization_suggestions = self._generate_optimization_suggestions(
                accuracy_metrics, successful_patterns, failed_patterns
            )
            
            # 5. 更新推断规则
            updated_rules = await self._update_inference_rules(
                successful_patterns, failed_patterns, accuracy_metrics
            )
            
            # 6. 创建学习反馈
            feedback = LearningFeedback(
                prediction_id=prediction_id,
                accuracy_metrics=accuracy_metrics,
                successful_patterns=successful_patterns,
                failed_patterns=failed_patterns,
                optimization_suggestions=optimization_suggestions,
                updated_rules=updated_rules
            )
            
            # 7. 保存学习结果
            await self._save_learning_feedback(feedback, actual_returns)
            
            logger.info(f"  预测评估完成: 总体准确率{accuracy_metrics['overall_accuracy']:.2%}")
            
            return feedback
            
        except Exception as e:
            logger.error(f"  预测评估失败: {e}")
            raise
    
    def _calculate_accuracy_metrics(
        self, 
        predicted_returns: Dict[str, float], 
        actual_returns: Dict[str, float]
    ) -> Dict[str, float]:
        """计算准确性指标"""
        
        metrics = {}
        
        # 只计算有实际数据的股票
        common_stocks = set(predicted_returns.keys()) & set(actual_returns.keys())
        
        if not common_stocks:
            return {"overall_accuracy": 0.0, "direction_accuracy": 0.0, 
                   "magnitude_accuracy": 0.0, "timing_accuracy": 0.0, "risk_control": 0.0}
        
        # 1. 方向准确性（涨跌方向是否正确）
        direction_correct = 0
        for stock in common_stocks:
            pred_direction = 1 if predicted_returns[stock] > 0 else -1
            actual_direction = 1 if actual_returns[stock] > 0 else -1
            if pred_direction == actual_direction:
                direction_correct += 1
        
        metrics["direction_accuracy"] = direction_correct / len(common_stocks)
        
        # 2. 幅度准确性（预测幅度与实际幅度的接近程度）
        magnitude_errors = []
        for stock in common_stocks:
            pred_mag = abs(predicted_returns[stock])
            actual_mag = abs(actual_returns[stock])
            if actual_mag > 0:
                error = abs(pred_mag - actual_mag) / actual_mag
                magnitude_errors.append(min(1.0, error))  # 限制最大误差为100%
        
        metrics["magnitude_accuracy"] = 1.0 - np.mean(magnitude_errors) if magnitude_errors else 0.0
        
        # 3. 时机准确性（基于收益率分布的一致性）
        if len(common_stocks) > 1:
            pred_values = [predicted_returns[stock] for stock in common_stocks]
            actual_values = [actual_returns[stock] for stock in common_stocks]
            
            # 计算排序相关性
            from scipy.stats import spearmanr
            try:
                correlation, _ = spearmanr(pred_values, actual_values)
                metrics["timing_accuracy"] = max(0.0, correlation) if not np.isnan(correlation) else 0.0
            except:
                metrics["timing_accuracy"] = 0.0
        else:
            metrics["timing_accuracy"] = metrics["direction_accuracy"]
        
        # 4. 风险控制（避免大幅亏损的能力）
        risk_control_score = 1.0
        for stock in common_stocks:
            if predicted_returns[stock] > 0 and actual_returns[stock] < -0.1:  # 预测涨但实际跌超10%
                risk_control_score -= 0.3
            elif predicted_returns[stock] > 0.05 and actual_returns[stock] < -0.05:  # 预测大涨但实际下跌
                risk_control_score -= 0.2
        
        metrics["risk_control"] = max(0.0, risk_control_score)
        
        # 5. 综合准确率
        metrics["overall_accuracy"] = sum(
            metrics[metric] * weight 
            for metric, weight in self.metric_weights.items()
        )
        
        return metrics
    
    def _analyze_prediction_patterns(
        self, 
        news_analysis: Dict, 
        predicted_returns: Dict[str, float], 
        actual_returns: Dict[str, float],
        accuracy_metrics: Dict[str, float]
    ) -> Tuple[List[Dict], List[Dict]]:
        """分析成功和失败的预测模式"""
        
        successful_patterns = []
        failed_patterns = []
        
        # 解析新闻分析数据
        inferred_sectors = json.loads(news_analysis["inferred_sectors"])
        selected_stocks = json.loads(news_analysis["selected_stocks"])
        
        # 按板块分析
        sector_performance = {}
        for stock_info in selected_stocks:
            sector = stock_info["sector"]
            stock_code = stock_info["stock_code"]
            
            if sector not in sector_performance:
                sector_performance[sector] = {
                    "stocks": [],
                    "predicted_avg": 0,
                    "actual_avg": 0,
                    "accuracy": 0
                }
            
            if stock_code in predicted_returns and stock_code in actual_returns:
                sector_performance[sector]["stocks"].append({
                    "code": stock_code,
                    "name": stock_info["stock_name"],
                    "predicted": predicted_returns[stock_code],
                    "actual": actual_returns[stock_code],
                    "quality": stock_info["quality_score"]
                })
        
        # 计算板块表现
        for sector, perf in sector_performance.items():
            if perf["stocks"]:
                perf["predicted_avg"] = np.mean([s["predicted"] for s in perf["stocks"]])
                perf["actual_avg"] = np.mean([s["actual"] for s in perf["stocks"]])
                
                # 计算板块预测准确性
                direction_correct = sum(
                    1 for s in perf["stocks"] 
                    if (s["predicted"] > 0) == (s["actual"] > 0)
                )
                perf["accuracy"] = direction_correct / len(perf["stocks"])
                
                # 找到对应的板块推断信息
                sector_inference = next(
                    (s for s in inferred_sectors if s["sector"] == sector), 
                    None
                )
                
                pattern = {
                    "sector": sector,
                    "inference_confidence": sector_inference["confidence"] if sector_inference else 0,
                    "inference_reasons": sector_inference["reasons"] if sector_inference else [],
                    "predicted_return": perf["predicted_avg"],
                    "actual_return": perf["actual_avg"],
                    "accuracy": perf["accuracy"],
                    "stock_count": len(perf["stocks"]),
                    "avg_quality": np.mean([s["quality"] for s in perf["stocks"]])
                }
                
                # 判断成功或失败
                if perf["accuracy"] >= self.accuracy_threshold:
                    successful_patterns.append(pattern)
                else:
                    failed_patterns.append(pattern)
        
        return successful_patterns, failed_patterns
    
    def _generate_optimization_suggestions(
        self, 
        accuracy_metrics: Dict[str, float],
        successful_patterns: List[Dict],
        failed_patterns: List[Dict]
    ) -> List[str]:
        """生成优化建议"""
        
        suggestions = []
        
        # 基于准确率指标的建议
        if accuracy_metrics["direction_accuracy"] < 0.6:
            suggestions.append("方向预测准确率偏低，需要优化板块推断规则")
        
        if accuracy_metrics["magnitude_accuracy"] < 0.5:
            suggestions.append("幅度预测偏差较大，需要调整收益率预期模型")
        
        if accuracy_metrics["risk_control"] < 0.7:
            suggestions.append("风险控制不足，需要增强下跌风险识别能力")
        
        # 基于成功模式的建议
        if successful_patterns:
            high_confidence_patterns = [p for p in successful_patterns if p["inference_confidence"] > 0.8]
            if high_confidence_patterns:
                suggestions.append(f"高置信度推断表现良好，可以提高{len(high_confidence_patterns)}个成功模式的权重")
            
            high_quality_patterns = [p for p in successful_patterns if p["avg_quality"] > 0.8]
            if high_quality_patterns:
                suggestions.append("高质量股票选择策略有效，应该继续优化质量评分体系")
        
        # 基于失败模式的建议
        if failed_patterns:
            low_confidence_failures = [p for p in failed_patterns if p["inference_confidence"] < 0.6]
            if low_confidence_failures:
                suggestions.append(f"低置信度推断失败率高，应该提高推断阈值或改进{len(low_confidence_failures)}个失败模式")
            
            for pattern in failed_patterns:
                if pattern["actual_return"] < -0.1:  # 大幅下跌
                    suggestions.append(f"{pattern['sector']}板块出现大幅下跌，需要分析原因并更新风险评估")
        
        return suggestions
    
    async def _update_inference_rules(
        self, 
        successful_patterns: List[Dict],
        failed_patterns: List[Dict],
        accuracy_metrics: Dict[str, float]
    ) -> List[Dict]:
        """更新推断规则"""
        
        updated_rules = []
        
        # 基于成功模式强化规则
        for pattern in successful_patterns:
            if pattern["accuracy"] > 0.8 and pattern["inference_confidence"] > 0.7:
                # 创建或强化成功规则
                new_rule = {
                    "type": "learned_success_pattern",
                    "sector": pattern["sector"],
                    "keywords": pattern["inference_reasons"],
                    "confidence_boost": 0.1,
                    "success_rate": pattern["accuracy"],
                    "learned_from": "successful_prediction",
                    "created_at": datetime.now().isoformat()
                }
                updated_rules.append(new_rule)
        
        # 基于失败模式调整规则
        for pattern in failed_patterns:
            if pattern["accuracy"] < 0.4:
                # 创建风险警告规则
                risk_rule = {
                    "type": "learned_risk_pattern",
                    "sector": pattern["sector"],
                    "risk_keywords": pattern["inference_reasons"],
                    "confidence_penalty": -0.2,
                    "failure_rate": 1 - pattern["accuracy"],
                    "learned_from": "failed_prediction",
                    "created_at": datetime.now().isoformat()
                }
                updated_rules.append(risk_rule)
        
        # 保存学习到的规则
        await self._save_learned_rules(updated_rules)
        
        return updated_rules
    
    async def _save_learned_rules(self, rules: List[Dict]):
        """保存学习到的规则"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for rule in rules:
                cursor.execute('''
                    INSERT INTO learning_rules 
                    (rule_type, rule_content, success_rate, usage_count)
                    VALUES (?, ?, ?, ?)
                ''', (
                    rule["type"],
                    json.dumps(rule, ensure_ascii=False),
                    rule.get("success_rate", 0.5),
                    0
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"  保存{len(rules)}条学习规则")
            
        except Exception as e:
            logger.error(f"  保存学习规则失败: {e}")
    
    async def _save_learning_feedback(self, feedback: LearningFeedback, actual_returns: Dict[str, float]):
        """保存学习反馈"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE prediction_results 
                SET actual_returns = ?, accuracy_score = ?, learning_feedback = ?, evaluation_date = ?
                WHERE id = ?
            ''', (
                json.dumps(actual_returns, ensure_ascii=False),
                feedback.accuracy_metrics["overall_accuracy"],
                json.dumps({
                    "accuracy_metrics": feedback.accuracy_metrics,
                    "successful_patterns": feedback.successful_patterns,
                    "failed_patterns": feedback.failed_patterns,
                    "optimization_suggestions": feedback.optimization_suggestions,
                    "updated_rules_count": len(feedback.updated_rules)
                }, ensure_ascii=False),
                datetime.now().isoformat(),
                feedback.prediction_id
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"  保存学习反馈失败: {e}")
    
    def _get_prediction_data(self, prediction_id: str) -> Optional[Dict]:
        """获取预测数据"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM prediction_results WHERE id = ?
            ''', (prediction_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, row))
            
            return None
            
        except Exception as e:
            logger.error(f"  获取预测数据失败: {e}")
            return None
    
    def _get_news_analysis(self, news_analysis_id: str) -> Optional[Dict]:
        """获取新闻分析数据"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM news_analysis WHERE id = ?
            ''', (news_analysis_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, row))
            
            return None
            
        except Exception as e:
            logger.error(f"  获取新闻分析数据失败: {e}")
            return None

# 测试函数
async def test_learning_evolution():
    """测试学习进化引擎"""
    
    engine = AdaptiveLearningEvolutionEngine()
    
    # 基于真实数据的计算
    actual_returns = {
        "000061.SZ": 0.08,   # 农产品 +8%
        "000876.SZ": 0.12,   # 新希望 +12%
        "000998.SZ": -0.02,  # 隆平高科 -2%
        "002041.SZ": 0.05,   # 登海种业 +5%
        "002714.SZ": 0.15    # 牧原股份 +15%
    }
    
    print("  自适应学习进化引擎测试")
    print("=" * 60)
    
    # 假设有一个预测ID
    prediction_id = "pred_news_123456_20241210"
    
    try:
        # 评估预测准确性
        feedback = await engine.evaluate_prediction_accuracy(
            prediction_id, actual_returns
        )
        
        print(f" 准确性评估结果:")
        for metric, value in feedback.accuracy_metrics.items():
            print(f"  {metric}: {value:.2%}")
        
        print(f"\n  成功模式 ({len(feedback.successful_patterns)}个):")
        for pattern in feedback.successful_patterns:
            print(f"  - {pattern['sector']}: 准确率{pattern['accuracy']:.2%}")
        
        print(f"\n  失败模式 ({len(feedback.failed_patterns)}个):")
        for pattern in feedback.failed_patterns:
            print(f"  - {pattern['sector']}: 准确率{pattern['accuracy']:.2%}")
        
        print(f"\n💡 优化建议:")
        for suggestion in feedback.optimization_suggestions:
            print(f"  - {suggestion}")
        
        print(f"\n  更新规则: {len(feedback.updated_rules)}条")
        
    except Exception as e:
        print(f"  测试需要先运行板块推断引擎生成预测数据")
        print(f"错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_learning_evolution())


# 全局自适应学习引擎实例
adaptive_learning_engine = AdaptiveLearningEvolutionEngine()
