#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天枢星新闻收集服务 - 精简版真实服务
专注核心功能，移除重复代码和模拟API
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class NewsCollectionService:
    """天枢星新闻收集服务 - 精简版"""
    
    def __init__(self):
        self.service_name = "NewsCollectionService"
        self.version = "3.0.0"
        
        # 初始化新闻收集器
        self.news_crawler = None
        self._initialize_crawler()
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def _initialize_crawler(self):
        """初始化新闻爬虫"""
        try:
            # 修复导入路径问题
            import sys
            import os

            # 确保backend目录在Python路径中
            backend_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            if backend_path not in sys.path:
                sys.path.insert(0, backend_path)

            from shared.news_sources.professional_news_crawler import professional_news_crawler
            self.news_crawler = professional_news_crawler
            logger.info("✅ 专业新闻爬虫初始化成功")
        except ImportError as e:
            logger.error(f"❌ 专业新闻爬虫初始化失败: {e}")
            self.news_crawler = None
    
    async def collect_stock_news(self, symbol: str, limit: int = 10) -> Dict[str, Any]:
        """收集股票新闻"""
        try:
            if not self.news_crawler:
                return {
                    "success": False,
                    "error": "新闻爬虫不可用",
                    "symbol": symbol,
                    "news_count": 0,
                    "news_data": []
                }

            logger.info(f"🔍 开始收集股票 {symbol} 的新闻")

            # 获取股票信息
            try:
                from shared.utils.stock_code_converter import stock_converter
                stock_info = stock_converter.get_stock_info(symbol)
                stock_name = stock_info.get('name', f'股票{symbol}')
            except ImportError:
                # 备用方案
                stock_name = f'股票{symbol}'

            # 使用专业新闻爬虫收集新闻
            async with self.news_crawler as crawler:
                result = await crawler.crawl_stock_news(symbol, stock_name, limit)

                if result.get("success"):
                    news_data = result.get("news_data", [])
                    logger.info(f"✅ 成功收集股票 {symbol} 新闻: {len(news_data)}条")

                    # 🔥 新增：调用DeepSeek进行情感分析
                    analyzed_news = await self._analyze_news_sentiment(news_data, symbol)

                    return {
                        "success": True,
                        "symbol": symbol,
                        "stock_name": stock_name,
                        "news_count": len(analyzed_news),
                        "news_data": analyzed_news,
                        "sentiment_analysis": await self._generate_overall_sentiment(analyzed_news),
                        "collection_time": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"⚠️ 股票 {symbol} 新闻收集失败: {result.get('error', '未知错误')}")
                    return {
                        "success": False,
                        "error": result.get('error', '新闻收集失败'),
                        "symbol": symbol,
                        "news_count": 0,
                        "news_data": []
                    }

        except Exception as e:
            logger.error(f"❌ 收集股票 {symbol} 新闻异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "symbol": symbol,
                "news_count": 0,
                "news_data": []
            }
    
    async def collect_market_news(self, limit: int = 20) -> Dict[str, Any]:
        """收集市场新闻"""
        try:
            if not self.news_crawler:
                return {
                    "success": False,
                    "error": "新闻爬虫不可用",
                    "news_count": 0,
                    "market_news": []
                }
            
            logger.info(f"🔍 开始收集市场新闻")
            
            # 使用专业新闻爬虫收集市场新闻
            async with self.news_crawler as crawler:
                result = await crawler.crawl_market_news(limit)
                
                if result.get("success"):
                    news_data = result.get("news_data", [])
                    logger.info(f"✅ 成功收集市场新闻: {len(news_data)}条")
                    
                    return {
                        "success": True,
                        "news_count": len(news_data),
                        "market_news": news_data,
                        "collection_time": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"⚠️ 市场新闻收集失败: {result.get('error', '未知错误')}")
                    return {
                        "success": False,
                        "error": result.get('error', '市场新闻收集失败'),
                        "news_count": 0,
                        "market_news": []
                    }
                    
        except Exception as e:
            logger.error(f"❌ 收集市场新闻异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "news_count": 0,
                "market_news": []
            }
    
    async def get_latest_news(self, symbols: List[str] = None, limit: int = 10) -> Dict[str, Any]:
        """获取最新新闻"""
        try:
            all_news = []
            
            if symbols:
                # 收集指定股票的新闻
                for symbol in symbols[:3]:  # 限制股票数量
                    result = await self.collect_stock_news(symbol, limit//len(symbols))
                    if result.get("success"):
                        news_data = result.get("news_data", [])
                        # 添加股票标识 - 避免dictionary changed size during iteration
                        news_list = list(news_data)  # 创建副本
                        for news in news_list:
                            news["related_symbol"] = symbol
                        all_news.extend(news_list)
            else:
                # 收集市场新闻
                result = await self.collect_market_news(limit)
                if result.get("success"):
                    all_news = result.get("market_news", [])
            
            # 按时间排序
            sorted_news = sorted(all_news, key=lambda x: x.get('timestamp', ''), reverse=True)
            final_news = sorted_news[:limit]
            
            return {
                "success": True,
                "news_count": len(final_news),
                "news": final_news,
                "symbols": symbols or [],
                "collection_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取最新新闻失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "news_count": 0,
                "news": []
            }

    async def _analyze_news_sentiment(self, news_data: List[Dict], symbol: str) -> List[Dict]:
        """使用DeepSeek分析新闻情感"""
        try:
            from shared.infrastructure.deepseek_service import deepseek_service

            analyzed_news = []

            # 避免dictionary changed size during iteration错误
            news_list = list(news_data)  # 创建副本

            for news_item in news_list:
                try:
                    # 构建分析提示
                    news_text = f"标题: {news_item.get('title', '')}\n内容: {news_item.get('content', news_item.get('summary', ''))}"

                    prompt = f"""
                    请分析以下关于股票{symbol}的新闻情感倾向：

                    {news_text}

                    请给出：
                    1. 情感倾向（正面/中性/负面）
                    2. 情感强度（1-10分）
                    3. 对股价影响预测（上涨/持平/下跌）
                    4. 简要分析原因

                    请用JSON格式回答：
                    {{"sentiment": "正面/中性/负面", "intensity": 数字, "price_impact": "上涨/持平/下跌", "reason": "分析原因"}}
                    """

                    # 调用DeepSeek分析
                    analysis_result = await deepseek_service.analyze_with_context(
                        prompt,
                        context_type="market_analysis",
                        max_tokens=300
                    )

                    if analysis_result.get("success"):
                        # 解析分析结果
                        analysis_content = analysis_result.get("analysis", "")
                        sentiment_info = self._parse_sentiment_analysis(analysis_content)

                        # 添加情感分析到新闻项
                        news_item["sentiment_analysis"] = sentiment_info
                        news_item["analyzed_by"] = "deepseek_ai"
                        news_item["analysis_time"] = datetime.now().isoformat()

                        logger.info(f"✅ 新闻情感分析完成: {sentiment_info.get('sentiment', '未知')} ({sentiment_info.get('intensity', 0)}/10)")
                    else:
                        # 分析失败，使用默认值
                        news_item["sentiment_analysis"] = {
                            "sentiment": "中性",
                            "intensity": 5,
                            "price_impact": "持平",
                            "reason": "AI分析暂时不可用"
                        }
                        news_item["analyzed_by"] = "fallback"

                except Exception as e:
                    logger.warning(f"单条新闻情感分析失败: {e}")
                    news_item["sentiment_analysis"] = {
                        "sentiment": "中性",
                        "intensity": 5,
                        "price_impact": "持平",
                        "reason": f"分析异常: {str(e)}"
                    }
                    news_item["analyzed_by"] = "error_fallback"

                analyzed_news.append(news_item)

            logger.info(f"🧠 完成{len(analyzed_news)}条新闻的DeepSeek情感分析")
            return analyzed_news

        except Exception as e:
            logger.error(f"❌ 新闻情感分析异常: {e}")
            # 返回原始新闻数据，避免影响主流程
            return news_data

    def _parse_sentiment_analysis(self, analysis_content: str) -> Dict[str, Any]:
        """解析情感分析结果"""
        try:
            import json
            import re

            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', analysis_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                sentiment_data = json.loads(json_str)
                return sentiment_data
            else:
                # 如果没有JSON，尝试文本解析
                sentiment = "中性"
                intensity = 5
                price_impact = "持平"
                reason = analysis_content[:100]

                if "正面" in analysis_content or "积极" in analysis_content:
                    sentiment = "正面"
                    price_impact = "上涨"
                    intensity = 7
                elif "负面" in analysis_content or "消极" in analysis_content:
                    sentiment = "负面"
                    price_impact = "下跌"
                    intensity = 3

                return {
                    "sentiment": sentiment,
                    "intensity": intensity,
                    "price_impact": price_impact,
                    "reason": reason
                }

        except Exception as e:
            logger.warning(f"解析情感分析结果失败: {e}")
            return {
                "sentiment": "中性",
                "intensity": 5,
                "price_impact": "持平",
                "reason": "解析失败"
            }

    async def _generate_overall_sentiment(self, analyzed_news: List[Dict]) -> Dict[str, Any]:
        """生成整体情感分析"""
        try:
            if not analyzed_news:
                return {"overall_sentiment": "中性", "confidence": 0.5}

            positive_count = 0
            negative_count = 0
            neutral_count = 0
            total_intensity = 0

            for news in analyzed_news:
                sentiment_info = news.get("sentiment_analysis", {})
                sentiment = sentiment_info.get("sentiment", "中性")
                intensity = sentiment_info.get("intensity", 5)

                if sentiment == "正面":
                    positive_count += 1
                elif sentiment == "负面":
                    negative_count += 1
                else:
                    neutral_count += 1

                total_intensity += intensity

            total_news = len(analyzed_news)
            avg_intensity = total_intensity / total_news if total_news > 0 else 5

            # 判断整体情感
            if positive_count > negative_count:
                overall_sentiment = "正面"
                confidence = positive_count / total_news
            elif negative_count > positive_count:
                overall_sentiment = "负面"
                confidence = negative_count / total_news
            else:
                overall_sentiment = "中性"
                confidence = neutral_count / total_news

            return {
                "overall_sentiment": overall_sentiment,
                "confidence": round(confidence, 2),
                "average_intensity": round(avg_intensity, 1),
                "sentiment_distribution": {
                    "positive": positive_count,
                    "negative": negative_count,
                    "neutral": neutral_count
                },
                "total_analyzed": total_news
            }

        except Exception as e:
            logger.error(f"生成整体情感分析失败: {e}")
            return {"overall_sentiment": "中性", "confidence": 0.5}

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": self.service_name,
            "version": self.version,
            "news_crawler_available": self.news_crawler is not None,
            "capabilities": [
                "股票新闻收集",
                "市场新闻收集",
                "最新新闻获取"
            ],
            "timestamp": datetime.now().isoformat()
        }

# 全局实例
news_collection_service = NewsCollectionService()

# 向后兼容
professional_news_collection_service = news_collection_service
ProfessionalNewsCollectionService = NewsCollectionService

__all__ = [
    "NewsCollectionService", 
    "news_collection_service", 
    "professional_news_collection_service",
    "ProfessionalNewsCollectionService"
]
