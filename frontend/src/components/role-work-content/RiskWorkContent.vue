<template>
  <div class="risk-work-content work-content-base">
    <!-- 风险监控仪表板 -->
    <div class="risk-dashboard">
      <div class="dashboard-header">
        <h2>风险监控仪表板</h2>
        <div class="alert-level" :class="currentAlertLevel">
          <span class="alert-icon">{{ alertIcons[currentAlertLevel] }}</span>
          <span class="alert-text">{{ alertTexts[currentAlertLevel] }}</span>
        </div>
      </div>

      <!-- 关键风险指标 -->
      <div class="key-metrics">
        <div class="metric-card" v-for="metric in keyMetrics" :key="metric.key">
          <div class="metric-header">
            <span class="metric-icon">{{ metric.icon }}</span>
            <span class="metric-name">{{ metric.name }}</span>
          </div>
          <div class="metric-value" :class="metric.status">
            {{ metric.value }}
          </div>
          <div class="metric-change" :class="metric.changeType">
            {{ metric.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 风险控制节点 -->
    <div class="risk-nodes">
      <div class="node-card" v-for="node in riskNodes" :key="node.id">
        <div class="node-header">
          <div class="node-icon">{{ node.icon }}</div>
          <h3 class="node-title">{{ node.name }}</h3>
          <div class="node-status" :class="node.status">
            <span class="status-indicator"></span>
          </div>
        </div>
        <div class="node-content">
          <p class="node-description">{{ node.description }}</p>
          <div class="node-metrics">
            <div class="metric" v-for="metric in node.metrics" :key="metric.key">
              <span class="metric-label">{{ metric.label }}</span>
              <span class="metric-value" :class="metric.status">{{ metric.value }}</span>
            </div>
          </div>
          <div class="risk-gauge">
            <div class="gauge-container">
              <div class="gauge-fill" :style="{ 
                width: node.riskLevel + '%',
                backgroundColor: getRiskColor(node.riskLevel)
              }"></div>
            </div>
            <span class="gauge-text">风险等级: {{ node.riskLevel }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 风控API功能面板 -->
    <div class="risk-api-panel">
      <div class="panel-header">
        <h3>🛡️ 风控总监AI功能</h3>
        <div class="api-status">
          <span class="status-indicator" :class="{ active: apiStatus.connected }"></span>
          <span>{{ apiStatus.connected ? '已连接' : '未连接' }}</span>
        </div>
      </div>

      <div class="api-functions">
        <div class="function-group">
          <h4>风险评估</h4>
          <div class="function-buttons">
            <button class="api-btn" @click="triggerPortfolioAssessment" :disabled="loading.portfolio">
              <span class="btn-icon">📊</span>
              <span class="btn-text">投资组合风险评估</span>
              <span v-if="loading.portfolio" class="loading">⏳</span>
            </button>
            <button class="api-btn" @click="triggerComplianceCheck" :disabled="loading.compliance">
              <span class="btn-icon">📋</span>
              <span class="btn-text">合规性检查</span>
              <span v-if="loading.compliance" class="loading">⏳</span>
            </button>
          </div>
        </div>

        <div class="function-group">
          <h4>实时监控</h4>
          <div class="function-buttons">
            <button class="api-btn" @click="triggerRealTimeMonitoring" :disabled="loading.monitoring">
              <span class="btn-icon">📡</span>
              <span class="btn-text">实时风险监控</span>
              <span v-if="loading.monitoring" class="loading">⏳</span>
            </button>
            <button class="api-btn" @click="triggerAnomalyDetection" :disabled="loading.anomaly">
              <span class="btn-icon">🔍</span>
              <span class="btn-text">市场异常检测</span>
              <span v-if="loading.anomaly" class="loading">⏳</span>
            </button>
          </div>
        </div>

        <div class="function-group">
          <h4>高级分析</h4>
          <div class="function-buttons">
            <button class="api-btn" @click="triggerModelDriftDetection" :disabled="loading.drift">
              <span class="btn-icon">🌊</span>
              <span class="btn-text">模型漂移检测</span>
              <span v-if="loading.drift" class="loading">⏳</span>
            </button>
            <button class="api-btn" @click="getRiskDashboard" :disabled="loading.dashboard">
              <span class="btn-icon">📈</span>
              <span class="btn-text">风险控制台</span>
              <span v-if="loading.dashboard" class="loading">⏳</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能预警系统 -->
    <div class="alert-system">
      <div class="alert-header">
        <h3>智能预警系统</h3>
        <div class="alert-summary">
          <span class="alert-count" v-for="(count, level) in alertCounts" :key="level"
                :class="level">
            {{ alertIcons[level] }} {{ count }}
          </span>
        </div>
      </div>
      <div class="alert-list">
        <div class="alert-item" v-for="alert in recentAlerts" :key="alert.id"
             :class="alert.level">
          <div class="alert-icon">{{ alertIcons[alert.level] }}</div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-description">{{ alert.description }}</div>
            <div class="alert-time">{{ alert.time }}</div>
          </div>
          <div class="alert-actions">
            <button class="action-btn" @click="handleAlert(alert)">处理</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import TianjiStarApi from '@/api/tianji-star'

// Props
const props = defineProps<{
  roleData?: any
  roleApiData?: any
}>()

// 当前预警级别
const currentAlertLevel = ref('normal')

// 预警图标和文本
const alertIcons = {
  normal: '🟢',
  attention: '🟡',
  warning: '🟠',
  danger: '🔴'
}

const alertTexts = {
  normal: '系统正常',
  attention: '需要注意',
  warning: '风险警告',
  danger: '高危状态'
}

// 关键风险指标 - 从API数据动态生成
const keyMetrics = reactive([])

// 风险控制节点 - 从API数据动态生成
const riskNodes = reactive([])

// 预警统计 - 从API数据动态生成
const alertCounts = reactive({
  normal: 0,
  attention: 0,
  warning: 0,
  danger: 0
})

// 最近预警 - 从API数据动态生成
const recentAlerts = reactive([])

// API状态和加载状态
const apiStatus = reactive({
  connected: true
})

const loading = reactive({
  portfolio: false,
  compliance: false,
  monitoring: false,
  anomaly: false,
  drift: false,
  dashboard: false
})

// 获取风险颜色
const getRiskColor = (level: number) => {
  if (level < 20) return '#10b981'
  if (level < 40) return '#f59e0b'
  if (level < 70) return '#f97316'
  return '#ef4444'
}

// 处理预警
const handleAlert = (alert: any) => {
  console.log('处理预警:', alert)
  ElMessage.info(`正在处理预警: ${alert.title}`)
}

// API功能调用方法 - 使用真实的天玑星API
const triggerPortfolioAssessment = async () => {
  loading.portfolio = true
  try {
    // 使用示例投资组合数据
    const samplePortfolio = [
      {
        stock_code: '000059',
        market_value: 100000,
        shares: 1000,
        sector: '化工',
        market_cap: '大盘'
      },
      {
        stock_code: '000159',
        market_value: 80000,
        shares: 800,
        sector: '综合',
        market_cap: '中盘'
      }
    ]

    const result = await TianjiStarApi.executePortfolioAnalysis(samplePortfolio)
    if (result.success) {
      ElMessage.success('投资组合风险评估完成')
      console.log('投资组合风险评估结果:', result.data)

      // 更新本地数据显示
      updatePortfolioMetrics(result.data)
    } else {
      ElMessage.error('投资组合风险评估失败')
    }
  } catch (error) {
    console.error('投资组合风险评估失败:', error)
    ElMessage.error('投资组合风险评估失败')
  } finally {
    loading.portfolio = false
  }
}

const triggerComplianceCheck = async () => {
  loading.compliance = true
  try {
    // 使用天玑星API获取风险预警作为合规检查
    const result = await TianjiStarApi.getRiskAlerts('high', 20)
    if (result.success) {
      ElMessage.success('合规性检查完成')
      console.log('合规性检查结果:', result.data)

      // 更新合规状态显示
      updateComplianceStatus(result.data)
    } else {
      ElMessage.error('合规性检查失败')
    }
  } catch (error) {
    console.error('合规性检查失败:', error)
    ElMessage.error('合规性检查失败')
  } finally {
    loading.compliance = false
  }
}

const triggerRealTimeMonitoring = async () => {
  loading.monitoring = true
  try {
    const result = await TianjiStarApi.getRealTimeRiskData()
    if (result.success) {
      ElMessage.success('实时监控数据已更新')
      console.log('实时监控数据:', result.data)

      // 更新实时监控显示
      updateRealTimeData(result.data)
    } else {
      ElMessage.error('获取实时监控数据失败')
    }
  } catch (error) {
    console.error('实时监控失败:', error)
    ElMessage.error('实时监控失败')
  } finally {
    loading.monitoring = false
  }
}

const triggerAnomalyDetection = async () => {
  loading.anomaly = true
  try {
    // 使用压力测试作为异常检测
    const samplePortfolio = [
      { stock_code: '000059', market_value: 100000, shares: 1000, sector: '化工' },
      { stock_code: '000159', market_value: 80000, shares: 800, sector: '综合' }
    ]

    const result = await TianjiStarApi.executeStressTest(samplePortfolio, ['volatility_spike', 'market_crash'])
    if (result.success) {
      ElMessage.success('市场异常检测完成')
      console.log('异常检测结果:', result.data)

      // 更新异常检测结果显示
      updateAnomalyDetection(result.data)
    } else {
      ElMessage.error('市场异常检测失败')
    }
  } catch (error) {
    console.error('异常检测失败:', error)
    ElMessage.error('异常检测失败')
  } finally {
    loading.anomaly = false
  }
}

const triggerModelDriftDetection = async () => {
  loading.drift = true
  try {
    const response = await fetch('/api/risk-manager/detect-model-drift', { method: 'POST' })
    const result = await response.json()
    if (result.success) {
      ElMessage.success('模型漂移检测完成')
      console.log('模型漂移检测结果:', result.data)
    } else {
      ElMessage.error('模型漂移检测失败')
    }
  } catch (error) {
    console.error('模型漂移检测失败:', error)
    ElMessage.error('模型漂移检测失败')
  } finally {
    loading.drift = false
  }
}

const getRiskDashboard = async () => {
  loading.dashboard = true
  try {
    const result = await TianjiStarApi.getRiskDashboardData()
    if (result.success) {
      ElMessage.success('风险控制台数据已更新')
      console.log('风险控制台数据:', result.data)

      // 更新仪表板数据显示
      updateDashboardData(result.data)
    } else {
      ElMessage.error('获取风险控制台数据失败')
    }
  } catch (error) {
    console.error('获取风险控制台数据失败:', error)
    ElMessage.error('获取风险控制台数据失败')
  } finally {
    loading.dashboard = false
  }
}

// 数据更新方法
const updatePortfolioMetrics = (data: any) => {
  if (data?.final_report?.risk_metrics) {
    const metrics = data.final_report.risk_metrics
    keyMetrics.splice(0, keyMetrics.length,
      {
        key: 'volatility',
        name: '投资组合波动率',
        icon: '📊',
        value: `${(metrics.portfolio_volatility * 100).toFixed(2)}%`,
        status: metrics.portfolio_volatility > 0.25 ? 'warning' : 'normal',
        change: '+2.1%',
        changeType: 'positive'
      },
      {
        key: 'sharpe',
        name: '夏普比率',
        icon: '⚡',
        value: metrics.sharpe_ratio?.toFixed(2) || '0.00',
        status: metrics.sharpe_ratio > 1.0 ? 'good' : 'normal',
        change: '+0.15',
        changeType: 'positive'
      },
      {
        key: 'beta',
        name: '投资组合Beta',
        icon: '📈',
        value: metrics.portfolio_beta?.toFixed(2) || '1.00',
        status: metrics.portfolio_beta > 1.2 ? 'attention' : 'normal',
        change: '-0.05',
        changeType: 'negative'
      }
    )
  }
}

const updateComplianceStatus = (data: any) => {
  if (data?.alerts) {
    const highRiskAlerts = data.alerts.filter((alert: any) => alert.risk_level === 'HIGH')
    riskNodes.splice(0, riskNodes.length, {
      id: 'compliance-check',
      name: '合规检查节点',
      icon: '📋',
      status: highRiskAlerts.length === 0 ? 'active' : 'warning',
      description: `风险预警监控状态，当前高风险预警数量`,
      riskLevel: highRiskAlerts.length > 0 ? 70 : 20,
      metrics: [
        { key: 'total_alerts', label: '总预警数', value: data.total.toString(), status: 'normal' },
        { key: 'high_risk', label: '高风险', value: highRiskAlerts.length.toString(), status: highRiskAlerts.length > 0 ? 'warning' : 'good' },
        { key: 'active_alerts', label: '活跃预警', value: data.alerts.filter((a: any) => a.is_active).length.toString(), status: 'normal' }
      ]
    })
  }
}

const updateRealTimeData = (data: any) => {
  if (data?.metrics) {
    // 更新当前预警级别
    const riskLevel = data.metrics.market_risk_level
    if (riskLevel === 'low') {
      currentAlertLevel.value = 'normal'
    } else if (riskLevel === 'medium') {
      currentAlertLevel.value = 'attention'
    } else if (riskLevel === 'high') {
      currentAlertLevel.value = 'warning'
    } else {
      currentAlertLevel.value = 'danger'
    }
  }
}

const updateAnomalyDetection = (data: any) => {
  if (data?.comprehensive_analysis) {
    const analysis = data.comprehensive_analysis
    ElMessage.info(`检测到异常: 最坏情况损失 ${(analysis.worst_case_loss * 100).toFixed(1)}%`)
  }
}

const updateDashboardData = (data: any) => {
  if (data?.risk_metrics) {
    // 更新关键指标
    const metrics = data.risk_metrics
    keyMetrics.splice(0, keyMetrics.length,
      {
        key: 'var',
        name: 'VaR (95%)',
        icon: '📊',
        value: `${(metrics.portfolio_var * 100).toFixed(2)}%`,
        status: metrics.portfolio_var > 0.05 ? 'warning' : 'normal',
        change: '+0.5%',
        changeType: 'positive'
      },
      {
        key: 'volatility',
        name: '市场波动率',
        icon: '📈',
        value: `${(metrics.volatility * 100).toFixed(1)}%`,
        status: metrics.volatility > 0.2 ? 'attention' : 'normal',
        change: '+2.1%',
        changeType: 'positive'
      },
      {
        key: 'alerts',
        name: '活跃预警',
        icon: '🚨',
        value: metrics.active_alerts?.toString() || '0',
        status: metrics.active_alerts > 5 ? 'warning' : 'normal',
        change: '+1',
        changeType: 'positive'
      }
    )
  }
}

// 监听API数据变化，更新本地数据
watch(() => props.roleApiData, (newData) => {
  if (newData && newData.success && newData.data) {
    const apiData = newData.data
    console.log('🛡️ 风控总监收到API数据:', apiData)

    // 更新风险指标 - 基于真实API响应格式
    if (apiData.risk_overview) {
      const overview = apiData.risk_overview
      keyMetrics.splice(0, keyMetrics.length,
        {
          key: 'var',
          name: 'VaR (95%)',
          icon: '📊',
          value: `${(overview.portfolio_var * 100).toFixed(2)}%`,
          status: overview.portfolio_var > 0.03 ? 'warning' : 'normal'
        },
        {
          key: 'maxdd',
          name: '最大回撤',
          icon: '⬇️',
          value: `${(overview.max_drawdown * 100).toFixed(1)}%`,
          status: overview.max_drawdown > 0.1 ? 'warning' : 'normal'
        },
        {
          key: 'risk_budget',
          name: '风险预算利用率',
          icon: '📈',
          value: `${(overview.risk_budget_utilization * 100).toFixed(1)}%`,
          status: overview.risk_budget_utilization > 0.8 ? 'attention' : 'good'
        }
      )
    }

    // 更新合规状态节点
    if (apiData.compliance_status) {
      const compliance = apiData.compliance_status
      riskNodes.splice(0, riskNodes.length, {
        id: 'compliance-check',
        name: '合规检查节点',
        icon: '📋',
        status: compliance.violations === 0 ? 'active' : 'warning',
        description: `监管要求检查状态，自动合规验证结果`,
        riskLevel: compliance.violations > 0 ? 60 : 15,
        metrics: [
          { key: 'total_checks', label: '总检查项', value: compliance.total_checks.toString(), status: 'normal' },
          { key: 'passed_checks', label: '通过检查', value: compliance.passed_checks.toString(), status: 'good' },
          { key: 'violations', label: '违规项', value: compliance.violations.toString(), status: compliance.violations > 0 ? 'warning' : 'good' },
          { key: 'warnings', label: '警告项', value: compliance.warnings.toString(), status: compliance.warnings > 0 ? 'attention' : 'good' }
        ]
      })
    }

    // 更新预警数据
    if (apiData.risk_alerts_today) {
      recentAlerts.splice(0, recentAlerts.length, ...apiData.risk_alerts_today.map((alert, index) => ({
        id: index + 1,
        level: 'warning',
        title: alert.type || '风险预警',
        description: alert.description || '无描述',
        time: alert.time || '刚刚'
      })))

      // 更新预警统计
      alertCounts.warning = apiData.risk_alerts_today.length
      alertCounts.normal = Math.max(0, 10 - apiData.risk_alerts_today.length)
    }

    // 更新整体风险级别
    if (apiData.risk_overview && apiData.risk_overview.overall_risk_level) {
      const riskLevel = apiData.risk_overview.overall_risk_level
      if (riskLevel === '低风险') {
        currentAlertLevel.value = 'normal'
      } else if (riskLevel === '中等') {
        currentAlertLevel.value = 'attention'
      } else if (riskLevel === '高风险') {
        currentAlertLevel.value = 'warning'
      } else {
        currentAlertLevel.value = 'danger'
      }
    }
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
@use '@/styles/star-strategy/work-content-common.scss';

.risk-work-content {
  
  .risk-dashboard {
    margin-bottom: 24px;
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      
      .alert-level {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 600;
        
        &.normal {
          background: rgba(16, 185, 129, 0.2);
          color: #10b981;
        }
        
        &.attention {
          background: rgba(245, 158, 11, 0.2);
          color: #f59e0b;
        }
        
        &.warning {
          background: rgba(249, 115, 22, 0.2);
          color: #f97316;
        }
        
        &.danger {
          background: rgba(239, 68, 68, 0.2);
          color: #ef4444;
        }
      }
    }
    
    .key-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      
      .metric-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        backdrop-filter: blur(10px);
        
        .metric-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          
          .metric-icon {
            font-size: 20px;
          }
          
          .metric-name {
            font-size: 14px;
            opacity: 0.8;
          }
        }
        
        .metric-value {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 4px;
          
          &.good {
            color: #10b981;
          }
          
          &.attention {
            color: #f59e0b;
          }
          
          &.warning {
            color: #f97316;
          }
        }
        
        .metric-change {
          font-size: 12px;
          
          &.positive {
            color: #10b981;
          }
          
          &.negative {
            color: #ef4444;
          }
        }
      }
    }
  }
  
  .risk-nodes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .node-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      backdrop-filter: blur(10px);
      
      .node-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .node-icon {
          font-size: 24px;
        }
        
        .node-title {
          flex: 1;
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }
        
        .node-status {
          .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
          }
          
          &.testing .status-indicator {
            background: #f59e0b;
          }
          
          &.monitoring .status-indicator {
            background: #3b82f6;
          }
        }
      }
      
      .node-content {
        .node-description {
          font-size: 14px;
          opacity: 0.8;
          margin-bottom: 16px;
        }
        
        .node-metrics {
          margin-bottom: 16px;
          
          .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            
            .metric-label {
              opacity: 0.8;
            }
            
            .metric-value {
              font-weight: 600;
              
              &.good {
                color: #10b981;
              }
              
              &.attention {
                color: #f59e0b;
              }
            }
          }
        }
        
        .risk-gauge {
          .gauge-container {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
            
            .gauge-fill {
              height: 100%;
              transition: width 0.3s ease;
            }
          }
          
          .gauge-text {
            font-size: 12px;
            opacity: 0.8;
          }
        }
      }
    }
  }

  .risk-api-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    margin-bottom: 24px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }

      .api-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #ef4444;

          &.active {
            background: #10b981;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .api-functions {
      .function-group {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 500;
          color: #f59e0b;
        }

        .function-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 12px;

          .api-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover:not(:disabled) {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.3);
              transform: translateY(-1px);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }

            .btn-icon {
              font-size: 18px;
            }

            .btn-text {
              flex: 1;
              text-align: left;
              font-size: 14px;
            }

            .loading {
              animation: spin 1s linear infinite;
            }
          }
        }
      }
    }
  }

  .alert-system {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    
    .alert-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }
      
      .alert-summary {
        display: flex;
        gap: 16px;
        
        .alert-count {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          
          &.normal {
            color: #10b981;
          }
          
          &.attention {
            color: #f59e0b;
          }
          
          &.warning {
            color: #f97316;
          }
          
          &.danger {
            color: #ef4444;
          }
        }
      }
    }
    
    .alert-list {
      .alert-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 8px;
        background: rgba(255, 255, 255, 0.05);
        
        &.warning {
          border-left: 4px solid #f97316;
        }
        
        &.attention {
          border-left: 4px solid #f59e0b;
        }
        
        .alert-icon {
          font-size: 20px;
        }
        
        .alert-content {
          flex: 1;
          
          .alert-title {
            font-weight: 600;
            margin-bottom: 4px;
          }
          
          .alert-description {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 4px;
          }
          
          .alert-time {
            font-size: 12px;
            opacity: 0.6;
          }
        }
        
        .alert-actions {
          .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            
            &:hover {
              background: rgba(255, 255, 255, 0.2);
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
