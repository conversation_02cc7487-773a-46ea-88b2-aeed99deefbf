#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实技术指标计算服务
基于真实股票数据计算各种技术指标
"""

import sqlite3
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class RealTechnicalCalculator:
    """真实技术指标计算器"""
    
    def __init__(self):
        self.service_name = "RealTechnicalCalculator"
        self.version = "1.0.0"
        self.db_path = "backend/data/stock_database.db"
        
        logger.info(f"✅ {self.service_name} v{self.version} 初始化完成")
    
    def ensure_technical_table(self):
        """确保技术指标表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建技术指标表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS technical_indicators (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    ma5 REAL,
                    ma10 REAL,
                    ma20 REAL,
                    ma60 REAL,
                    rsi REAL,
                    macd REAL,
                    macd_signal REAL,
                    macd_histogram REAL,
                    kdj_k REAL,
                    kdj_d REAL,
                    kdj_j REAL,
                    bollinger_upper REAL,
                    bollinger_middle REAL,
                    bollinger_lower REAL,
                    cci REAL,
                    williams_r REAL,
                    data_source TEXT DEFAULT 'real_calculation',
                    created_at TEXT,
                    updated_at TEXT,
                    UNIQUE(stock_code, trade_date)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tech_stock_date ON technical_indicators(stock_code, trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tech_date ON technical_indicators(trade_date)")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 技术指标表结构确认完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建技术指标表失败: {e}")
            return False
    
    def get_stock_data(self, stock_code: str, days: int = 300) -> pd.DataFrame:
        """获取股票历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取最近N天的数据，确保有足够数据计算技术指标
            query = """
                SELECT trade_date, open_price, close_price, high_price, low_price, volume
                FROM daily_data 
                WHERE stock_code = ? 
                ORDER BY trade_date DESC 
                LIMIT ?
            """
            
            df = pd.read_sql_query(query, conn, params=(stock_code, days))
            conn.close()
            
            if not df.empty:
                # 按日期正序排列（计算技术指标需要）
                df = df.sort_values('trade_date').reset_index(drop=True)
                
                # 确保数据类型正确
                df['open_price'] = pd.to_numeric(df['open_price'], errors='coerce')
                df['close_price'] = pd.to_numeric(df['close_price'], errors='coerce')
                df['high_price'] = pd.to_numeric(df['high_price'], errors='coerce')
                df['low_price'] = pd.to_numeric(df['low_price'], errors='coerce')
                df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
                
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ 获取股票数据失败 {stock_code}: {e}")
            return pd.DataFrame()
    
    def calculate_ma(self, df: pd.DataFrame) -> Dict[str, pd.Series]:
        """计算移动平均线"""
        try:
            ma_indicators = {}
            
            if len(df) >= 5:
                ma_indicators['ma5'] = df['close_price'].rolling(window=5).mean()
            if len(df) >= 10:
                ma_indicators['ma10'] = df['close_price'].rolling(window=10).mean()
            if len(df) >= 20:
                ma_indicators['ma20'] = df['close_price'].rolling(window=20).mean()
            if len(df) >= 60:
                ma_indicators['ma60'] = df['close_price'].rolling(window=60).mean()
            
            return ma_indicators
            
        except Exception as e:
            logger.error(f"❌ 计算移动平均线失败: {e}")
            return {}
    
    def calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算RSI相对强弱指标"""
        try:
            if len(df) < period + 1:
                return pd.Series([np.nan] * len(df))
            
            delta = df['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            logger.error(f"❌ 计算RSI失败: {e}")
            return pd.Series([np.nan] * len(df))
    
    def calculate_macd(self, df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD指标"""
        try:
            if len(df) < slow + signal:
                return {
                    'macd': pd.Series([np.nan] * len(df)),
                    'macd_signal': pd.Series([np.nan] * len(df)),
                    'macd_histogram': pd.Series([np.nan] * len(df))
                }
            
            # 计算EMA
            ema_fast = df['close_price'].ewm(span=fast).mean()
            ema_slow = df['close_price'].ewm(span=slow).mean()
            
            # MACD线
            macd = ema_fast - ema_slow
            
            # 信号线
            macd_signal = macd.ewm(span=signal).mean()
            
            # MACD柱状图
            macd_histogram = macd - macd_signal
            
            return {
                'macd': macd,
                'macd_signal': macd_signal,
                'macd_histogram': macd_histogram
            }
            
        except Exception as e:
            logger.error(f"❌ 计算MACD失败: {e}")
            return {
                'macd': pd.Series([np.nan] * len(df)),
                'macd_signal': pd.Series([np.nan] * len(df)),
                'macd_histogram': pd.Series([np.nan] * len(df))
            }
    
    def calculate_kdj(self, df: pd.DataFrame, period: int = 9) -> Dict[str, pd.Series]:
        """计算KDJ指标"""
        try:
            if len(df) < period:
                return {
                    'kdj_k': pd.Series([np.nan] * len(df)),
                    'kdj_d': pd.Series([np.nan] * len(df)),
                    'kdj_j': pd.Series([np.nan] * len(df))
                }
            
            # 计算RSV
            low_min = df['low_price'].rolling(window=period).min()
            high_max = df['high_price'].rolling(window=period).max()
            rsv = (df['close_price'] - low_min) / (high_max - low_min) * 100
            
            # 计算K、D、J
            k = rsv.ewm(com=2).mean()  # K = RSV的3日移动平均
            d = k.ewm(com=2).mean()    # D = K的3日移动平均
            j = 3 * k - 2 * d          # J = 3K - 2D
            
            return {
                'kdj_k': k,
                'kdj_d': d,
                'kdj_j': j
            }
            
        except Exception as e:
            logger.error(f"❌ 计算KDJ失败: {e}")
            return {
                'kdj_k': pd.Series([np.nan] * len(df)),
                'kdj_d': pd.Series([np.nan] * len(df)),
                'kdj_j': pd.Series([np.nan] * len(df))
            }
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """计算布林带"""
        try:
            if len(df) < period:
                return {
                    'bollinger_upper': pd.Series([np.nan] * len(df)),
                    'bollinger_middle': pd.Series([np.nan] * len(df)),
                    'bollinger_lower': pd.Series([np.nan] * len(df))
                }
            
            # 中轨（移动平均线）
            middle = df['close_price'].rolling(window=period).mean()
            
            # 标准差
            std = df['close_price'].rolling(window=period).std()
            
            # 上轨和下轨
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            
            return {
                'bollinger_upper': upper,
                'bollinger_middle': middle,
                'bollinger_lower': lower
            }
            
        except Exception as e:
            logger.error(f"❌ 计算布林带失败: {e}")
            return {
                'bollinger_upper': pd.Series([np.nan] * len(df)),
                'bollinger_middle': pd.Series([np.nan] * len(df)),
                'bollinger_lower': pd.Series([np.nan] * len(df))
            }
    
    def calculate_cci(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """计算CCI顺势指标"""
        try:
            if len(df) < period:
                return pd.Series([np.nan] * len(df))
            
            # 典型价格
            tp = (df['high_price'] + df['low_price'] + df['close_price']) / 3
            
            # 移动平均
            ma_tp = tp.rolling(window=period).mean()
            
            # 平均绝对偏差
            mad = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            
            # CCI
            cci = (tp - ma_tp) / (0.015 * mad)
            
            return cci
            
        except Exception as e:
            logger.error(f"❌ 计算CCI失败: {e}")
            return pd.Series([np.nan] * len(df))
    
    def calculate_williams_r(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算威廉指标"""
        try:
            if len(df) < period:
                return pd.Series([np.nan] * len(df))
            
            # 最高价和最低价
            high_max = df['high_price'].rolling(window=period).max()
            low_min = df['low_price'].rolling(window=period).min()
            
            # 威廉指标
            williams_r = (high_max - df['close_price']) / (high_max - low_min) * (-100)
            
            return williams_r
            
        except Exception as e:
            logger.error(f"❌ 计算威廉指标失败: {e}")
            return pd.Series([np.nan] * len(df))
    
    def calculate_stock_indicators(self, stock_code: str) -> bool:
        """计算单只股票的所有技术指标"""
        try:
            logger.debug(f"📊 计算 {stock_code} 技术指标...")
            
            # 获取股票数据
            df = self.get_stock_data(stock_code)
            
            if df.empty or len(df) < 20:
                logger.debug(f"⚠️ {stock_code} 数据不足，跳过")
                return False
            
            # 计算各种技术指标
            ma_indicators = self.calculate_ma(df)
            rsi = self.calculate_rsi(df)
            macd_indicators = self.calculate_macd(df)
            kdj_indicators = self.calculate_kdj(df)
            bollinger_indicators = self.calculate_bollinger_bands(df)
            cci = self.calculate_cci(df)
            williams_r = self.calculate_williams_r(df)
            
            # 保存到数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().isoformat()
            records_inserted = 0
            
            for i, row in df.iterrows():
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO technical_indicators 
                        (stock_code, trade_date, ma5, ma10, ma20, ma60, rsi, 
                         macd, macd_signal, macd_histogram, kdj_k, kdj_d, kdj_j,
                         bollinger_upper, bollinger_middle, bollinger_lower, 
                         cci, williams_r, data_source, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code,
                        row['trade_date'],
                        ma_indicators.get('ma5', pd.Series([np.nan])).iloc[i] if 'ma5' in ma_indicators else None,
                        ma_indicators.get('ma10', pd.Series([np.nan])).iloc[i] if 'ma10' in ma_indicators else None,
                        ma_indicators.get('ma20', pd.Series([np.nan])).iloc[i] if 'ma20' in ma_indicators else None,
                        ma_indicators.get('ma60', pd.Series([np.nan])).iloc[i] if 'ma60' in ma_indicators else None,
                        rsi.iloc[i] if not pd.isna(rsi.iloc[i]) else None,
                        macd_indicators['macd'].iloc[i] if not pd.isna(macd_indicators['macd'].iloc[i]) else None,
                        macd_indicators['macd_signal'].iloc[i] if not pd.isna(macd_indicators['macd_signal'].iloc[i]) else None,
                        macd_indicators['macd_histogram'].iloc[i] if not pd.isna(macd_indicators['macd_histogram'].iloc[i]) else None,
                        kdj_indicators['kdj_k'].iloc[i] if not pd.isna(kdj_indicators['kdj_k'].iloc[i]) else None,
                        kdj_indicators['kdj_d'].iloc[i] if not pd.isna(kdj_indicators['kdj_d'].iloc[i]) else None,
                        kdj_indicators['kdj_j'].iloc[i] if not pd.isna(kdj_indicators['kdj_j'].iloc[i]) else None,
                        bollinger_indicators['bollinger_upper'].iloc[i] if not pd.isna(bollinger_indicators['bollinger_upper'].iloc[i]) else None,
                        bollinger_indicators['bollinger_middle'].iloc[i] if not pd.isna(bollinger_indicators['bollinger_middle'].iloc[i]) else None,
                        bollinger_indicators['bollinger_lower'].iloc[i] if not pd.isna(bollinger_indicators['bollinger_lower'].iloc[i]) else None,
                        cci.iloc[i] if not pd.isna(cci.iloc[i]) else None,
                        williams_r.iloc[i] if not pd.isna(williams_r.iloc[i]) else None,
                        'real_calculation',
                        current_time,
                        current_time
                    ))
                    records_inserted += 1
                except Exception as e:
                    logger.debug(f"插入技术指标记录失败 {stock_code} {row['trade_date']}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.debug(f"✅ {stock_code} 技术指标计算完成: {records_inserted} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"❌ 计算 {stock_code} 技术指标失败: {e}")
            return False
    
    def batch_calculate_indicators(self, stock_codes: List[str] = None, limit: int = 100) -> Dict[str, Any]:
        """批量计算技术指标"""
        try:
            start_time = datetime.now()
            logger.info(f"🚀 开始批量计算技术指标...")
            
            # 确保技术指标表存在
            if not self.ensure_technical_table():
                return {"success": False, "error": "技术指标表创建失败"}
            
            # 获取需要计算的股票列表
            if stock_codes is None:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 获取有历史数据的股票
                cursor.execute("""
                    SELECT DISTINCT stock_code 
                    FROM daily_data 
                    WHERE data_source LIKE 'eastmoney%'
                    ORDER BY stock_code 
                    LIMIT ?
                """, (limit,))
                
                stock_codes = [row[0] for row in cursor.fetchall()]
                conn.close()
            
            if not stock_codes:
                return {"success": False, "error": "无可用股票数据"}
            
            logger.info(f"📊 开始计算 {len(stock_codes)} 只股票的技术指标")
            
            # 批量计算
            success_count = 0
            failed_count = 0
            
            for i, stock_code in enumerate(stock_codes):
                try:
                    if self.calculate_stock_indicators(stock_code):
                        success_count += 1
                    else:
                        failed_count += 1
                    
                    # 显示进度
                    if (i + 1) % 10 == 0:
                        logger.info(f"📈 进度: {i+1}/{len(stock_codes)}, 成功: {success_count}, 失败: {failed_count}")
                    
                except Exception as e:
                    logger.error(f"❌ 处理股票 {stock_code} 失败: {e}")
                    failed_count += 1
                    continue
            
            # 验证结果
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM technical_indicators WHERE data_source = 'real_calculation'")
            total_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM technical_indicators WHERE data_source = 'real_calculation'")
            stocks_with_indicators = cursor.fetchone()[0]
            
            conn.close()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "success": True,
                "total_stocks": len(stock_codes),
                "success_count": success_count,
                "failed_count": failed_count,
                "total_records": total_records,
                "stocks_with_indicators": stocks_with_indicators,
                "duration_seconds": duration,
                "timestamp": end_time.isoformat()
            }
            
            logger.info(f"✅ 技术指标计算完成: {success_count}/{len(stock_codes)} 只股票成功")
            logger.info(f"📊 生成技术指标记录: {total_records} 条")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 批量计算技术指标失败: {e}")
            return {"success": False, "error": str(e)}

# 全局服务实例
real_technical_calculator = RealTechnicalCalculator()
