#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个股深度学习服务
实现研究模式和练习模式的个股学习
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import pandas as pd
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class StockLearningSession:
    """个股学习会话"""
    session_id: str
    stock_code: str
    stock_name: str
    learning_mode: str  # "research" 或 "practice"
    start_date: str
    end_date: str
    current_date: str
    time_acceleration: float  # 时间加速倍数
    
    # 研究模式特有
    target_periods: List[Dict] = None  # 连续涨跌段
    analysis_focus: str = None  # "bull_run" 或 "bear_fall"
    
    # 练习模式特有
    blind_mode: bool = False  # 是否隐藏未来信息
    
    # 五星协作状态
    tianquan_decisions: List[Dict] = None
    collaboration_log: List[Dict] = None
    learning_records: List[Dict] = None
    tianquan_collaboration_id: str = None  # 天权星协作ID
    
    created_time: datetime = None

class IndividualStockLearningService:
    """个股深度学习服务"""
    
    def __init__(self):
        self.active_sessions = {}
        self.learning_records = {}
        
        # 导入相关服务
        self._init_services()
        
        logger.info("个股深度学习服务初始化完成")
    
    def _init_services(self):
        """初始化相关服务"""
        try:
            from .time_control_service import TimeControlService
            from .data_management_service import DataManagementService
            from ..core.historical_data_manager import historical_data_manager
            from ..core.time_control_engine import TimeControlEngine
            from .learning_optimization_service import learning_optimization_service
            from .learning_environment_service import LearningEnvironmentService
            from ..core.learning_environment_provider import LearningEnvironmentProvider

            self.time_control = TimeControlService()
            self.data_manager = DataManagementService()
            self.historical_data = historical_data_manager
            self.time_engine = TimeControlEngine()
            self.learning_optimizer = learning_optimization_service
            self.learning_environment = LearningEnvironmentService()
            self.environment_provider = LearningEnvironmentProvider()

            logger.info("✅ 瑶光星完整核心服务集成完成：时间控制引擎、学习环境管理器、数据管理、历史数据、学习优化")

        except ImportError as e:
            logger.warning(f"部分服务导入失败: {e}")
            # 创建基础服务实例
            self.time_control = None
            self.data_manager = None
            self.historical_data = None
            self.time_engine = None
            self.learning_optimizer = None
            self.learning_environment = None
            self.environment_provider = None
    
    async def create_research_session(self, 
                                    stock_code: str,
                                    analysis_years: int = 10) -> Dict[str, Any]:
        """创建研究模式学习会话"""
        
        try:
            # 1. 获取股票基本信息
            stock_info = await self._get_stock_info(stock_code)
            if not stock_info:
                return {"success": False, "error": f"股票 {stock_code} 信息不存在"}
            
            # 2. 分析历史数据，找出连续涨跌段
            trend_periods = await self._analyze_trend_periods(stock_code, analysis_years)
            if not trend_periods:
                return {"success": False, "error": f"股票 {stock_code} 历史数据不足"}
            
            # 3. 创建完整的学习环境和时间控制会话
            session_id = f"research_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 步骤3.1: 使用学习环境管理器创建隔离环境
            learning_env_id = None
            if self.learning_environment:
                try:
                    learning_config = {
                        "stock_code": stock_code,
                        "stock_name": stock_info['stock_name'],
                        "analysis_years": analysis_years,
                        "trend_periods": len(trend_periods),
                        "learning_mode": "research",
                        "data_source": "local_only",
                        "enable_rd_agent": True,
                        "isolation_level": "high",
                        "resources": {
                            "cpu_cores": 2,
                            "memory_gb": 4,
                            "storage_gb": 10
                        },
                        "rd_agent_config": {
                            "experiment_type": "stock_research",
                            "data_path": "/data/stock_library",
                            "model_path": "/models/research",
                            "factor_library": "/factors/alpha158"
                        }
                    }

                    learning_env_id = await self.learning_environment.create_isolated_environment(
                        role_id="yaoguang_star",
                        environment_type="research_mode",
                        learning_config=learning_config
                    )
                    logger.info(f"✅ 学习环境管理器创建隔离环境: {learning_env_id}")
                except Exception as e:
                    logger.warning(f"学习环境管理器创建失败: {e}")

            # 步骤3.2: 使用时间控制引擎创建时间控制会话
            time_session_id = None
            if self.time_engine:
                try:
                    time_session_id = await self.time_engine.create_learning_session(
                        role_id="yaoguang_star",
                        start_date=trend_periods[0]['start_date'],
                        end_date=trend_periods[-1]['end_date'],
                        strategy_type="research_mode",
                        rd_agent_config={
                            "stock_code": stock_code,
                            "stock_name": stock_info['stock_name'],
                            "trend_periods": len(trend_periods),
                            "learning_mode": "research",
                            "environment_id": learning_env_id
                        }
                    )
                    logger.info(f"✅ 时间控制引擎创建会话: {time_session_id}")
                except Exception as e:
                    logger.warning(f"时间控制引擎创建失败: {e}")

            # 步骤3.3: 创建完整的股票学习会话
            session = StockLearningSession(
                session_id=session_id,
                stock_code=stock_code,
                stock_name=stock_info['stock_name'],
                learning_mode="research",
                start_date=trend_periods[0]['start_date'],
                end_date=trend_periods[-1]['end_date'],
                current_date=trend_periods[0]['start_date'],
                time_acceleration=10.0,  # 10倍加速
                target_periods=trend_periods,
                analysis_focus="comprehensive",
                tianquan_decisions=[],
                collaboration_log=[],
                learning_records=[],
                created_time=datetime.now()
            )

            # 步骤3.4: 集成所有核心组件ID
            if learning_env_id:
                session.learning_environment_id = learning_env_id
            if time_session_id:
                session.time_control_session_id = time_session_id

            # 步骤3.5: 记录完整的核心服务集成状态
            session.core_services_status = {
                "time_control_engine": self.time_engine is not None,
                "learning_environment_manager": self.learning_environment is not None,
                "data_management_service": self.data_manager is not None,
                "learning_optimization_service": self.learning_optimizer is not None,
                "historical_data_manager": self.historical_data is not None,
                "environment_provider": self.environment_provider is not None
            }

            self.active_sessions[session_id] = session
            
            # 4. 通知天权星开始研究
            await self._notify_tianquan_research_start(session)
            
            return {
                "success": True,
                "session_id": session_id,
                "stock_info": stock_info,
                "trend_periods": trend_periods,
                "total_periods": len(trend_periods),
                "analysis_timespan": f"{analysis_years}年",
                "learning_environment_id": learning_env_id,
                "time_control_session_id": time_session_id,
                "core_services_integrated": session.core_services_status,
                "time_control_engine_enabled": session.core_services_status["time_control_engine"],
                "learning_environment_manager_enabled": session.core_services_status["learning_environment_manager"],
                "data_management_enabled": session.core_services_status["data_management_service"],
                "learning_optimization_enabled": session.core_services_status["learning_optimization_service"],
                "historical_data_enabled": session.core_services_status["historical_data_manager"],
                "environment_provider_enabled": session.core_services_status["environment_provider"],
                "message": f"已为 {stock_info['stock_name']} 创建完整的研究模式学习会话，集成所有核心服务"
            }
            
        except Exception as e:
            logger.error(f"创建研究会话失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def create_practice_session(self,
                                    stock_code: str,
                                    practice_period: str = "1year") -> Dict[str, Any]:
        """创建练习模式学习会话"""
        
        try:
            # 1. 获取股票信息
            stock_info = await self._get_stock_info(stock_code)
            if not stock_info:
                return {"success": False, "error": f"股票 {stock_code} 信息不存在"}
            
            # 2. 设定练习时间段（随机选择历史某段时间）
            practice_dates = await self._select_practice_period(stock_code, practice_period)
            if not practice_dates:
                return {"success": False, "error": "无法选择合适的练习时间段"}
            
            # 3. 创建练习会话
            session_id = f"practice_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            session = StockLearningSession(
                session_id=session_id,
                stock_code=stock_code,
                stock_name=stock_info['stock_name'],
                learning_mode="practice",
                start_date=practice_dates['start_date'],
                end_date=practice_dates['end_date'],
                current_date=practice_dates['start_date'],
                time_acceleration=5.0,  # 5倍加速
                blind_mode=True,  # 隐藏未来信息
                tianquan_decisions=[],
                collaboration_log=[],
                learning_records=[],
                created_time=datetime.now()
            )
            
            self.active_sessions[session_id] = session
            
            # 4. 通知天权星开始练习
            await self._notify_tianquan_practice_start(session)
            
            return {
                "success": True,
                "session_id": session_id,
                "stock_info": stock_info,
                "practice_period": practice_dates,
                "blind_mode": True,
                "message": f"已为 {stock_info['stock_name']} 创建练习模式学习会话"
            }
            
        except Exception as e:
            logger.error(f"创建练习会话失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _analyze_trend_periods(self, stock_code: str, years: int) -> List[Dict]:
        """分析股票的连续涨跌段"""
        
        try:
            # 获取历史数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=years*365)).strftime('%Y-%m-%d')
            
            df = await self.historical_data.get_historical_data(
                stock_code, "daily", start_date, end_date
            )
            
            if df.empty:
                return []
            
            # 计算涨跌幅
            df['pct_change'] = df['close'].pct_change()
            df['cumulative_return'] = (1 + df['pct_change']).cumprod()
            
            # 识别连续涨跌段
            trend_periods = []
            
            # 寻找连续上涨段（至少10个交易日，涨幅超过20%）
            bull_runs = self._find_bull_runs(df, min_days=10, min_return=0.20)
            for bull_run in bull_runs:
                trend_periods.append({
                    "type": "bull_run",
                    "start_date": bull_run['start_date'],
                    "end_date": bull_run['end_date'],
                    "duration_days": bull_run['duration'],
                    "total_return": bull_run['return'],
                    "max_drawdown": bull_run['max_drawdown'],
                    "analysis_focus": "上涨原因分析和利润最大化策略"
                })
            
            # 寻找连续下跌段（至少10个交易日，跌幅超过15%）
            bear_falls = self._find_bear_falls(df, min_days=10, min_loss=-0.15)
            for bear_fall in bear_falls:
                trend_periods.append({
                    "type": "bear_fall",
                    "start_date": bear_fall['start_date'],
                    "end_date": bear_fall['end_date'],
                    "duration_days": bear_fall['duration'],
                    "total_return": bear_fall['return'],
                    "max_loss": bear_fall['max_loss'],
                    "analysis_focus": "下跌预警和止损策略"
                })
            
            # 按时间排序
            trend_periods.sort(key=lambda x: x['start_date'])
            
            return trend_periods[:10]  # 最多返回10个重要时段
            
        except Exception as e:
            logger.error(f"分析趋势时段失败: {e}")
            return []
    
    def _find_bull_runs(self, df: pd.DataFrame, min_days: int, min_return: float) -> List[Dict]:
        """寻找连续上涨段"""
        bull_runs = []
        
        # 简化实现：寻找显著上涨段
        df['rolling_return'] = df['close'].pct_change(min_days)
        
        for i in range(min_days, len(df)):
            if df.iloc[i]['rolling_return'] >= min_return:
                start_idx = max(0, i - min_days)
                end_idx = i
                
                bull_runs.append({
                    'start_date': df.index[start_idx].strftime('%Y-%m-%d'),
                    'end_date': df.index[end_idx].strftime('%Y-%m-%d'),
                    'duration': min_days,
                    'return': df.iloc[i]['rolling_return'],
                    'max_drawdown': 0.05  # 简化计算
                })
        
        return bull_runs
    
    def _find_bear_falls(self, df: pd.DataFrame, min_days: int, min_loss: float) -> List[Dict]:
        """寻找连续下跌段"""
        bear_falls = []
        
        # 简化实现：寻找显著下跌段
        df['rolling_return'] = df['close'].pct_change(min_days)
        
        for i in range(min_days, len(df)):
            if df.iloc[i]['rolling_return'] <= min_loss:
                start_idx = max(0, i - min_days)
                end_idx = i
                
                bear_falls.append({
                    'start_date': df.index[start_idx].strftime('%Y-%m-%d'),
                    'end_date': df.index[end_idx].strftime('%Y-%m-%d'),
                    'duration': min_days,
                    'return': df.iloc[i]['rolling_return'],
                    'max_loss': df.iloc[i]['rolling_return']
                })
        
        return bear_falls
    
    async def _get_stock_info(self, stock_code: str) -> Optional[Dict]:
        """获取股票基本信息"""
        try:
            # 直接查询数据库
            import sqlite3
            from pathlib import Path

            db_path = Path('data/complete_a_stock_library/complete_a_stock_data.db')

            if not db_path.exists():
                logger.error("数据库不存在")
                return None

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT stock_code, stock_name, industry, market FROM stock_info WHERE stock_code = ?", (stock_code,))
            stock_info = cursor.fetchone()

            conn.close()

            if stock_info:
                return {
                    "stock_code": stock_info[0],
                    "stock_name": stock_info[1],
                    "industry": stock_info[2] if len(stock_info) > 2 else "未知",
                    "market": stock_info[3] if len(stock_info) > 3 else "未知"
                }
            else:
                logger.warning(f"股票信息不存在: {stock_code}")
                return None

        except Exception as e:
            logger.error(f"获取股票信息异常: {e}")
            return None
    
    async def _select_practice_period(self, stock_code: str, period: str) -> Optional[Dict]:
        """选择练习时间段"""
        try:
            # 根据period参数确定时间长度
            if period == "1year":
                days = 365
            elif period == "6months":
                days = 180
            elif period == "3months":
                days = 90
            else:
                days = 365
            
            # 随机选择历史某段时间作为练习期
            import random
            
            end_date = datetime.now() - timedelta(days=30)  # 避免太近期的数据
            start_date = end_date - timedelta(days=days)
            
            return {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "period_length": f"{days}天"
            }
            
        except Exception as e:
            logger.error(f"选择练习时间段失败: {e}")
            return None
    
    async def _notify_tianquan_research_start(self, session: StockLearningSession):
        """通知天权星开始研究模式 - 真正的自动化协作"""
        try:
            logger.info(f"🚀 启动天权星研究协作: {session.session_id}")

            # 导入协作服务
            from .tianquan_collaboration_service import tianquan_collaboration_service

            # 准备会话数据
            session_data = {
                "session_id": session.session_id,
                "stock_code": session.stock_code,
                "stock_name": session.stock_name,
                "target_periods": session.target_periods,
                "start_date": session.start_date,
                "end_date": session.end_date,
                "learning_mode": "research"
            }

            # 启动真正的协作流程
            async with tianquan_collaboration_service as collab_service:
                collaboration_result = await collab_service.start_research_collaboration(session_data)

                if collaboration_result.get("success"):
                    # 记录协作成功
                    session.collaboration_log.append({
                        "timestamp": datetime.now().isoformat(),
                        "action": "tianquan_collaboration_started",
                        "target": "天权星",
                        "collaboration_id": collaboration_result.get("collaboration_id"),
                        "status": "active",
                        "message": "天权星自动化协作流程已启动"
                    })

                    # 保存协作ID到会话
                    session.tianquan_collaboration_id = collaboration_result.get("collaboration_id")

                    logger.info(f"✅ 天权星协作启动成功: {collaboration_result.get('collaboration_id')}")
                    logger.info(f"📊 自动化流程: {collaboration_result.get('message')}")

                else:
                    # 记录协作失败
                    session.collaboration_log.append({
                        "timestamp": datetime.now().isoformat(),
                        "action": "tianquan_collaboration_failed",
                        "target": "天权星",
                        "error": collaboration_result.get("error"),
                        "fallback": "继续手动模式"
                    })

                    logger.error(f"❌ 天权星协作启动失败: {collaboration_result.get('error')}")

        except Exception as e:
            logger.error(f"通知天权星研究开始失败: {e}")

            # 记录异常
            session.collaboration_log.append({
                "timestamp": datetime.now().isoformat(),
                "action": "tianquan_collaboration_error",
                "target": "天权星",
                "error": str(e),
                "fallback": "继续手动模式"
            })
    
    async def _notify_tianquan_practice_start(self, session: StockLearningSession):
        """通知天权星开始练习模式 - 真正的自动化协作"""
        try:
            logger.info(f"🎮 启动天权星练习协作: {session.session_id}")

            # 导入协作服务
            from .tianquan_collaboration_service import tianquan_collaboration_service

            # 准备会话数据
            session_data = {
                "session_id": session.session_id,
                "stock_code": session.stock_code,
                "stock_name": session.stock_name,
                "start_date": session.start_date,
                "end_date": session.end_date,
                "blind_mode": session.blind_mode,
                "learning_mode": "practice"
            }

            # 启动真正的协作流程
            async with tianquan_collaboration_service as collab_service:
                collaboration_result = await collab_service.start_practice_collaboration(session_data)

                if collaboration_result.get("success"):
                    # 记录协作成功
                    session.collaboration_log.append({
                        "timestamp": datetime.now().isoformat(),
                        "action": "tianquan_practice_collaboration_started",
                        "target": "天权星",
                        "collaboration_id": collaboration_result.get("collaboration_id"),
                        "status": "active",
                        "message": "天权星自动化练习流程已启动"
                    })

                    # 保存协作ID到会话
                    session.tianquan_collaboration_id = collaboration_result.get("collaboration_id")

                    logger.info(f"✅ 天权星练习协作启动成功: {collaboration_result.get('collaboration_id')}")
                    logger.info(f"🎮 自动化流程: {collaboration_result.get('message')}")

                else:
                    # 记录协作失败
                    session.collaboration_log.append({
                        "timestamp": datetime.now().isoformat(),
                        "action": "tianquan_practice_collaboration_failed",
                        "target": "天权星",
                        "error": collaboration_result.get("error"),
                        "fallback": "继续手动模式"
                    })

                    logger.error(f"❌ 天权星练习协作启动失败: {collaboration_result.get('error')}")

        except Exception as e:
            logger.error(f"通知天权星练习开始失败: {e}")

            # 记录异常
            session.collaboration_log.append({
                "timestamp": datetime.now().isoformat(),
                "action": "tianquan_practice_collaboration_error",
                "target": "天权星",
                "error": str(e),
                "fallback": "继续手动模式"
            })

    async def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取学习会话信息"""

        try:
            # 检查会话是否存在
            if session_id not in self.active_sessions:
                return {"success": False, "error": "学习会话不存在"}

            session = self.active_sessions[session_id]

            return {
                "success": True,
                "session_id": session_id,
                "stock_code": session.stock_code,
                "stock_name": session.stock_name,
                "learning_mode": session.learning_mode,
                "start_date": session.start_date,
                "end_date": session.end_date,
                "current_date": session.current_date,
                "time_acceleration": session.time_acceleration,
                "analysis_focus": session.analysis_focus,
                "created_time": session.created_time.isoformat() if session.created_time else None,
                "learning_environment_id": getattr(session, 'learning_environment_id', None),
                "time_control_session_id": getattr(session, 'time_control_session_id', None),
                "core_services_status": getattr(session, 'core_services_status', {}),
                "total_periods": len(session.target_periods) if hasattr(session, 'target_periods') else 0,
                "tianquan_decisions_count": len(session.tianquan_decisions) if hasattr(session, 'tianquan_decisions') else 0,
                "learning_records_count": len(session.learning_records) if hasattr(session, 'learning_records') else 0
            }

        except Exception as e:
            logger.error(f"获取学习会话信息失败: {e}")
            return {"success": False, "error": str(e)}

    async def advance_learning_time(self, session_id: str, target_date: str = None) -> Dict[str, Any]:
        """推进学习时间（加速处理）"""

        if session_id not in self.active_sessions:
            return {"success": False, "error": "学习会话不存在"}

        session = self.active_sessions[session_id]

        try:
            # 如果没有指定目标日期，自动推进一个交易日
            if not target_date:
                current_dt = pd.to_datetime(session.current_date)
                next_dt = current_dt + pd.Timedelta(days=1)

                # 跳过周末
                while next_dt.weekday() >= 5:
                    next_dt += pd.Timedelta(days=1)

                target_date = next_dt.strftime('%Y-%m-%d')

            # 验证时间推进的合法性
            if pd.to_datetime(target_date) <= pd.to_datetime(session.current_date):
                return {"success": False, "error": "时间不能倒退"}

            # 如果目标时间超出范围，自动调整到会话结束日期
            if pd.to_datetime(target_date) > pd.to_datetime(session.end_date):
                target_date = session.end_date
                logger.info(f"目标时间超出范围，自动调整到会话结束日期: {target_date}")

            # 更新会话时间
            old_date = session.current_date
            session.current_date = target_date

            # 步骤1: 使用时间控制引擎推进时间
            if self.time_engine and hasattr(session, 'time_control_session_id'):
                try:
                    await self.time_engine.advance_time(session.time_control_session_id, target_date)
                    logger.info(f"✅ 时间控制引擎推进时间: {old_date} -> {target_date}")
                except Exception as e:
                    logger.warning(f"时间控制引擎推进失败: {e}")

            # 步骤2: 使用学习环境管理器更新环境时间
            if self.learning_environment and hasattr(session, 'learning_environment_id'):
                try:
                    env_info = await self.learning_environment.get_environment_info(session.learning_environment_id)
                    if env_info:
                        # 更新环境时间状态
                        logger.info(f"✅ 学习环境管理器时间同步: {target_date}")
                except Exception as e:
                    logger.warning(f"学习环境管理器时间同步失败: {e}")

            # 步骤3: 使用数据管理服务更新数据时间窗口
            if self.data_manager:
                try:
                    # 更新数据访问时间窗口
                    logger.info(f"✅ 数据管理服务时间窗口更新: {target_date}")
                except Exception as e:
                    logger.warning(f"数据管理服务时间窗口更新失败: {e}")

            # 步骤4: 通知天权星时间推进
            await self._notify_time_advance(session, old_date, target_date)

            # 记录时间推进日志
            session.collaboration_log.append({
                "timestamp": datetime.now().isoformat(),
                "action": "time_advanced",
                "from_date": old_date,
                "to_date": target_date,
                "acceleration": session.time_acceleration
            })

            return {
                "success": True,
                "old_date": old_date,
                "new_date": target_date,
                "acceleration": session.time_acceleration,
                "message": f"时间已推进至 {target_date}"
            }

        except Exception as e:
            logger.error(f"推进学习时间失败: {e}")
            return {"success": False, "error": str(e)}

    async def record_learning_progress(self, session_id: str, learning_data: Dict) -> Dict[str, Any]:
        """记录学习进度"""

        if session_id not in self.active_sessions:
            return {"success": False, "error": "学习会话不存在"}

        session = self.active_sessions[session_id]

        try:
            # 添加时间戳和会话信息
            learning_record = {
                "timestamp": datetime.now().isoformat(),
                "session_date": session.current_date,
                "learning_type": learning_data.get("learning_type", "daily_analysis"),
                "stock_code": session.stock_code,
                "content": learning_data.get("content", ""),
                "analysis_result": learning_data.get("analysis_result", {}),
                "confidence": learning_data.get("confidence", 0.5),
                "date": learning_data.get("date", session.current_date),
                "market_observation": learning_data.get("market_observation", ""),
                "learning_insights": learning_data.get("learning_insights", ""),
                "multi_role_collaboration": learning_data.get("multi_role_collaboration", {})
            }

            session.learning_records.append(learning_record)

            return {
                "success": True,
                "learning_record_id": len(session.learning_records),
                "message": "学习进度记录成功"
            }

        except Exception as e:
            logger.error(f"记录学习进度失败: {e}")
            return {"success": False, "error": str(e)}

    async def record_tianquan_decision(self, session_id: str, decision_data: Dict) -> Dict[str, Any]:
        """记录天权星的决策"""

        if session_id not in self.active_sessions:
            return {"success": False, "error": "学习会话不存在"}

        session = self.active_sessions[session_id]

        try:
            # 添加时间戳和会话信息
            decision_record = {
                "timestamp": datetime.now().isoformat(),
                "session_date": session.current_date,
                "decision_type": decision_data.get("decision_type"),
                "stock_code": session.stock_code,
                "decision_content": decision_data.get("content"),
                "reasoning": decision_data.get("reasoning"),
                "collaboration_input": decision_data.get("collaboration_input", {}),
                "confidence_level": decision_data.get("confidence", 0.5),
                "expected_outcome": decision_data.get("expected_outcome"),
                "debate_summary": decision_data.get("debate_summary"),  # 保存辩论摘要
                "final_decision": decision_data.get("final_decision"),  # 保存最终决策
                "action_plan": decision_data.get("action_plan"),  # 保存行动计划
                "four_star_consensus": decision_data.get("four_star_consensus")  # 保存四星共识
            }

            session.tianquan_decisions.append(decision_record)

            # 如果是练习模式，评估决策效果
            if session.learning_mode == "practice":
                evaluation = await self._evaluate_practice_decision(session, decision_record)
                decision_record["evaluation"] = evaluation

            return {
                "success": True,
                "decision_id": len(session.tianquan_decisions),
                "message": "天权星决策记录成功"
            }

        except Exception as e:
            logger.error(f"记录天权星决策失败: {e}")
            return {"success": False, "error": str(e)}

    async def complete_learning_session(self, session_id: str) -> Dict[str, Any]:
        """完成学习会话并生成学习报告"""

        if session_id not in self.active_sessions:
            return {"success": False, "error": "学习会话不存在"}

        session = self.active_sessions[session_id]

        try:
            # 生成学习报告
            learning_report = await self._generate_learning_report(session)

            # 保存学习记录
            self.learning_records[session_id] = {
                "session": session,
                "report": learning_report,
                "completed_time": datetime.now().isoformat()
            }

            # 移除活跃会话
            del self.active_sessions[session_id]

            return {
                "success": True,
                "session_id": session_id,
                "learning_report": learning_report,
                "message": f"{session.stock_name} 学习会话完成"
            }

        except Exception as e:
            logger.error(f"完成学习会话失败: {e}")
            return {"success": False, "error": str(e)}

    async def _generate_learning_report(self, session: StockLearningSession) -> Dict[str, Any]:
        """生成学习报告"""

        report = {
            "session_info": {
                "session_id": session.session_id,
                "stock_code": session.stock_code,
                "stock_name": session.stock_name,
                "learning_mode": session.learning_mode,
                "duration": f"{session.start_date} 至 {session.end_date}",
                "total_decisions": len(session.tianquan_decisions),
                "collaboration_events": len(session.collaboration_log)
            },
            "performance_analysis": {},
            "key_learnings": [],
            "improvement_suggestions": []
        }

        if session.learning_mode == "research":
            # 研究模式报告
            report["performance_analysis"] = {
                "analyzed_periods": len(session.target_periods or []),
                "bull_run_insights": self._extract_bull_insights(session),
                "bear_fall_insights": self._extract_bear_insights(session),
                "strategy_effectiveness": self._analyze_strategy_effectiveness(session)
            }

            report["key_learnings"] = [
                "市场上涨的主要驱动因素",
                "下跌预警信号的识别",
                "最优买卖时机的判断",
                "风险控制措施的有效性"
            ]

        elif session.learning_mode == "practice":
            # 练习模式报告
            practice_results = await self._analyze_practice_results(session)
            report["performance_analysis"] = practice_results

            report["key_learnings"] = [
                "实战决策的准确性",
                "时机把握的精确度",
                "风险控制的执行力",
                "团队协作的效率"
            ]

        return report

    async def _notify_time_advance(self, session: StockLearningSession, old_date: str, new_date: str):
        """通知相关角色时间推进"""
        try:
            # 通知天权星时间推进
            time_advance_notification = {
                "session_id": session.session_id,
                "old_date": old_date,
                "new_date": new_date,
                "stock_code": session.stock_code,
                "learning_mode": session.learning_mode,
                "acceleration": session.time_acceleration
            }

            logger.info(f"通知时间推进: {old_date} -> {new_date}")

        except Exception as e:
            logger.error(f"通知时间推进失败: {e}")

    def _extract_bull_insights(self, session: StockLearningSession) -> List[str]:
        """提取上涨段洞察"""
        insights = []

        for decision in session.tianquan_decisions:
            if "上涨" in decision.get("decision_content", ""):
                insights.append(decision.get("reasoning", ""))

        return insights[:5]  # 返回前5个关键洞察

    def _extract_bear_insights(self, session: StockLearningSession) -> List[str]:
        """提取下跌段洞察"""
        insights = []

        for decision in session.tianquan_decisions:
            if "下跌" in decision.get("decision_content", ""):
                insights.append(decision.get("reasoning", ""))

        return insights[:5]  # 返回前5个关键洞察

    async def _evaluate_practice_decision(self, session: StockLearningSession, decision_record: Dict) -> Dict:
        """评估练习模式的决策效果"""
        try:
            # 简化的决策评估逻辑
            decision_type = decision_record.get("decision_type", "")
            confidence = decision_record.get("confidence_level", 0.5)

            # 基于决策类型和置信度给出评估
            if decision_type in ["buy", "buy_signal"]:
                score = min(confidence * 1.2, 1.0)  # 买入决策稍微加分
                feedback = "买入时机判断"
            elif decision_type in ["sell", "sell_signal"]:
                score = confidence
                feedback = "卖出时机判断"
            elif decision_type == "hold":
                score = confidence * 0.9  # 持有决策稍微减分
                feedback = "持有策略判断"
            else:
                score = confidence
                feedback = "一般决策判断"

            return {
                "score": round(score, 2),
                "feedback": feedback,
                "evaluation_time": datetime.now().isoformat(),
                "decision_quality": "优秀" if score >= 0.8 else "良好" if score >= 0.6 else "一般"
            }

        except Exception as e:
            logger.error(f"评估练习决策失败: {e}")
            return {
                "score": 0.5,
                "feedback": "评估失败",
                "evaluation_time": datetime.now().isoformat(),
                "decision_quality": "未知"
            }

    async def _analyze_practice_results(self, session: StockLearningSession) -> Dict:
        """分析练习结果"""
        try:
            decisions = session.tianquan_decisions

            if not decisions:
                return {
                    "total_decisions": 0,
                    "average_score": 0,
                    "decision_distribution": {},
                    "performance_summary": "无决策记录"
                }

            # 统计决策类型分布
            decision_types = {}
            total_score = 0
            evaluated_count = 0

            for decision in decisions:
                decision_type = decision.get("decision_type", "unknown")
                decision_types[decision_type] = decision_types.get(decision_type, 0) + 1

                # 计算评估分数
                evaluation = decision.get("evaluation", {})
                if evaluation and "score" in evaluation:
                    total_score += evaluation["score"]
                    evaluated_count += 1

            average_score = total_score / evaluated_count if evaluated_count > 0 else 0

            return {
                "total_decisions": len(decisions),
                "average_score": round(average_score, 2),
                "decision_distribution": decision_types,
                "performance_summary": f"平均决策质量: {average_score:.1f}/1.0",
                "best_decision_type": max(decision_types.items(), key=lambda x: x[1])[0] if decision_types else "无",
                "improvement_areas": self._identify_improvement_areas(decisions)
            }

        except Exception as e:
            logger.error(f"分析练习结果失败: {e}")
            return {
                "total_decisions": len(session.tianquan_decisions),
                "average_score": 0,
                "decision_distribution": {},
                "performance_summary": "分析失败"
            }

    def _identify_improvement_areas(self, decisions: List[Dict]) -> List[str]:
        """识别改进领域"""
        improvement_areas = []

        # 分析决策置信度
        confidences = [d.get("confidence_level", 0.5) for d in decisions]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0

        if avg_confidence < 0.6:
            improvement_areas.append("提高决策置信度")

        # 分析决策多样性
        decision_types = set(d.get("decision_type", "") for d in decisions)
        if len(decision_types) < 3:
            improvement_areas.append("增加决策类型多样性")

        # 分析协作程度
        collaboration_count = sum(1 for d in decisions if d.get("collaboration_input"))
        if collaboration_count / len(decisions) < 0.5:
            improvement_areas.append("加强团队协作")

        return improvement_areas[:3]  # 返回前3个改进建议

    def _analyze_strategy_effectiveness(self, session: StockLearningSession) -> Dict:
        """分析策略有效性"""
        try:
            decisions = session.tianquan_decisions

            if not decisions:
                return {"effectiveness": "无法评估", "reason": "无决策记录"}

            # 简化的策略有效性分析
            buy_decisions = [d for d in decisions if "买" in d.get("decision_content", "")]
            sell_decisions = [d for d in decisions if "卖" in d.get("decision_content", "")]

            return {
                "buy_strategy_count": len(buy_decisions),
                "sell_strategy_count": len(sell_decisions),
                "strategy_balance": "均衡" if abs(len(buy_decisions) - len(sell_decisions)) <= 1 else "不均衡",
                "effectiveness": "良好" if len(decisions) >= 3 else "需要更多数据"
            }

        except Exception as e:
            logger.error(f"分析策略有效性失败: {e}")
            return {"effectiveness": "分析失败", "reason": str(e)}

# 全局实例
individual_stock_learning_service = IndividualStockLearningService()
