# -*- coding: utf-8 -*-
"""
因子提取服务主类 - 真正的核心实现
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from .factor_extractor import FactorExtractor, FactorExtractionResult
from .technical_factors import TechnicalFactorExtractor
from .fundamental_factors import FundamentalFactorExtractor
from .market_factors import MarketFactorExtractor
from .sentiment_factors import SentimentFactorExtractor

logger = logging.getLogger(__name__)

class FactorExtractionService:
    """因子提取服务 - 整合所有因子提取器"""
    
    def __init__(self):
        # 初始化各类因子提取器
        self.technical_extractor = TechnicalFactorExtractor()
        self.fundamental_extractor = FundamentalFactorExtractor()
        self.market_extractor = MarketFactorExtractor()
        self.sentiment_extractor = SentimentFactorExtractor()
        
        # 服务统计
        self.service_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "average_processing_time": 0.0,
            "factor_type_usage": {
                "technical": 0,
                "fundamental": 0,
                "market": 0,
                "sentiment": 0
            }
        }
        
        logger.info("因子提取服务初始化完成")
    
    async def extract_factors(self, data: Dict, factor_types: List[str] = None) -> FactorExtractionResult:
        """提取因子 - 主入口方法"""
        start_time = datetime.now()
        self.service_stats["total_extractions"] += 1
        
        try:
            if factor_types is None:
                factor_types = ["technical", "fundamental", "market", "sentiment"]
            
            # 更新使用统计
            for factor_type in factor_types:
                if factor_type in self.service_stats["factor_type_usage"]:
                    self.service_stats["factor_type_usage"][factor_type] += 1
            
            # 创建结果对象
            result = FactorExtractionResult(
                stock_code=data.get("stock_code"),
                extraction_type="comprehensive" if len(factor_types) > 1 else factor_types[0]
            )
            
            # 并发提取各类因子
            tasks = []
            
            if "technical" in factor_types:
                tasks.append(("technical", self.technical_extractor._extract_technical_factors(data)))
            
            if "fundamental" in factor_types:
                tasks.append(("fundamental", self.fundamental_extractor._extract_fundamental_factors(data)))
            
            if "market" in factor_types:
                tasks.append(("market", self.market_extractor._extract_market_factors(data)))
            
            if "sentiment" in factor_types:
                tasks.append(("sentiment", self.sentiment_extractor._extract_sentiment_factors(data)))
            
            # 等待所有任务完成
            if tasks:
                task_results = await asyncio.gather(*[task[1] for task in tasks], return_exceptions=True)
                
                for i, (factor_type, factors) in enumerate(zip([task[0] for task in tasks], task_results)):
                    if isinstance(factors, Exception):
                        logger.error(f"{factor_type}因子提取失败: {factors}")
                        continue
                    
                    if factor_type == "technical":
                        result.technical_factors = factors
                    elif factor_type == "fundamental":
                        result.fundamental_factors = factors
                    elif factor_type == "market":
                        result.market_factors = factors
                    elif factor_type == "sentiment":
                        result.sentiment_factors = factors
            
            # 计算汇总信息
            result.total_factors = (len(result.technical_factors) + 
                                  len(result.fundamental_factors) + 
                                  len(result.market_factors) + 
                                  len(result.sentiment_factors))
            
            result.overall_score = self._calculate_overall_score(result)
            result.confidence = self._calculate_confidence(result)
            result.processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新统计
            self.service_stats["successful_extractions"] += 1
            self._update_average_processing_time(result.processing_time)
            
            logger.info(f"因子提取完成: {result.total_factors}个因子, 总分: {result.overall_score:.2f}")
            
            return result
            
        except Exception as e:
            self.service_stats["failed_extractions"] += 1
            logger.error(f"因子提取失败: {e}")
            
            return FactorExtractionResult(
                stock_code=data.get("stock_code"),
                extraction_type="error",
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def extract_technical_factors(self, data: Dict) -> FactorExtractionResult:
        """仅提取技术因子"""
        return await self.extract_factors(data, ["technical"])
    
    async def extract_fundamental_factors(self, data: Dict) -> FactorExtractionResult:
        """仅提取基本面因子"""
        return await self.extract_factors(data, ["fundamental"])
    
    async def extract_market_factors(self, data: Dict) -> FactorExtractionResult:
        """仅提取市场因子"""
        return await self.extract_factors(data, ["market"])
    
    async def extract_sentiment_factors(self, data: Dict) -> FactorExtractionResult:
        """仅提取情感因子"""
        return await self.extract_factors(data, ["sentiment"])
    
    async def batch_extract_factors(self, data_list: List[Dict], factor_types: List[str] = None) -> List[FactorExtractionResult]:
        """批量提取因子"""
        logger.info(f"开始批量因子提取: {len(data_list)} 个股票")
        
        # 创建批量任务
        tasks = [self.extract_factors(data, factor_types) for data in data_list]
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"股票 {i} 因子提取失败: {result}")
                processed_results.append(FactorExtractionResult(
                    stock_code=data_list[i].get("stock_code", f"unknown_{i}"),
                    extraction_type="error"
                ))
            else:
                processed_results.append(result)
        
        successful_count = sum(1 for r in processed_results if r.extraction_type != "error")
        logger.info(f"批量因子提取完成: {successful_count}/{len(data_list)} 成功")
        
        return processed_results
    
    def analyze_factor_importance(self, results: List[FactorExtractionResult]) -> Dict:
        """分析因子重要性"""
        factor_stats = {}
        
        for result in results:
            all_factors = (result.technical_factors + 
                          result.fundamental_factors + 
                          result.market_factors + 
                          result.sentiment_factors)
            
            for factor in all_factors:
                factor_name = factor.factor_name
                if factor_name not in factor_stats:
                    factor_stats[factor_name] = {
                        "count": 0,
                        "values": [],
                        "confidences": [],
                        "factor_type": factor.factor_type
                    }
                
                factor_stats[factor_name]["count"] += 1
                factor_stats[factor_name]["values"].append(factor.factor_value)
                factor_stats[factor_name]["confidences"].append(factor.confidence)
        
        # 计算统计指标
        importance_analysis = {}
        for factor_name, stats in factor_stats.items():
            if stats["count"] > 0:
                import numpy as np
                importance_analysis[factor_name] = {
                    "frequency": stats["count"],
                    "mean_value": np.mean(stats["values"]),
                    "std_value": np.std(stats["values"]),
                    "mean_confidence": np.mean(stats["confidences"]),
                    "factor_type": stats["factor_type"],
                    "importance_score": stats["count"] * np.mean(stats["confidences"])
                }
        
        # 按重要性排序
        sorted_factors = sorted(importance_analysis.items(), 
                              key=lambda x: x[1]["importance_score"], 
                              reverse=True)
        
        return {
            "factor_importance": dict(sorted_factors),
            "total_factors": len(importance_analysis),
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def get_factor_correlation_matrix(self, results: List[FactorExtractionResult]) -> Dict:
        """计算因子相关性矩阵"""
        import pandas as pd
        import numpy as np
        
        # 收集所有因子数据
        factor_data = {}
        
        for result in results:
            stock_code = result.stock_code or "unknown"
            all_factors = (result.technical_factors + 
                          result.fundamental_factors + 
                          result.market_factors + 
                          result.sentiment_factors)
            
            for factor in all_factors:
                factor_name = factor.factor_name
                if factor_name not in factor_data:
                    factor_data[factor_name] = {}
                factor_data[factor_name][stock_code] = factor.factor_value
        
        # 转换为DataFrame
        df = pd.DataFrame(factor_data)
        
        if df.empty:
            return await self._get_real_dict()
        
        # 计算相关性矩阵
        correlation_matrix = df.corr()
        
        # 转换为字典格式
        correlation_dict = correlation_matrix.to_dict()
        
        return {
            "correlation_matrix": correlation_dict,
            "factor_count": len(df.columns),
            "stock_count": len(df.index),
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def _calculate_overall_score(self, result: FactorExtractionResult) -> float:
        """计算总体评分"""
        import numpy as np
        
        category_scores = {}
        category_weights = {
            "technical": 0.3,
            "fundamental": 0.4,
            "market": 0.2,
            "sentiment": 0.1
        }
        
        # 计算各类因子的平均分
        if result.technical_factors:
            category_scores["technical"] = np.mean([f.factor_value for f in result.technical_factors])
        
        if result.fundamental_factors:
            category_scores["fundamental"] = np.mean([f.factor_value for f in result.fundamental_factors])
        
        if result.market_factors:
            category_scores["market"] = np.mean([f.factor_value for f in result.market_factors])
        
        if result.sentiment_factors:
            category_scores["sentiment"] = np.mean([f.factor_value for f in result.sentiment_factors])
        
        # 加权计算总分
        total_score = 0.0
        total_weight = 0.0
        
        for category, score in category_scores.items():
            weight = category_weights.get(category, 0.25)
            total_score += score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        return float(total_score / total_weight)
    
    def _calculate_confidence(self, result: FactorExtractionResult) -> float:
        """计算总体置信度"""
        import numpy as np
        
        all_factors = (result.technical_factors + 
                      result.fundamental_factors + 
                      result.market_factors + 
                      result.sentiment_factors)
        
        if not all_factors:
            return 0.0
        
        # 基于因子数量和置信度计算
        confidence_scores = [f.confidence for f in all_factors]
        avg_confidence = np.mean(confidence_scores)
        
        # 因子数量越多，置信度越高
        factor_count_bonus = min(0.2, len(all_factors) * 0.01)
        
        total_confidence = avg_confidence + factor_count_bonus
        return float(min(1.0, total_confidence))
    
    def _update_average_processing_time(self, processing_time: float):
        """更新平均处理时间"""
        current_avg = self.service_stats["average_processing_time"]
        total_successful = self.service_stats["successful_extractions"]
        
        if total_successful == 1:
            self.service_stats["average_processing_time"] = processing_time
        else:
            # 计算移动平均
            new_avg = (current_avg * (total_successful - 1) + processing_time) / total_successful
            self.service_stats["average_processing_time"] = new_avg
    
    def get_service_statistics(self) -> Dict:
        """获取服务统计信息"""
        success_rate = (self.service_stats["successful_extractions"] / 
                       max(1, self.service_stats["total_extractions"]))
        
        return {
            "total_extractions": self.service_stats["total_extractions"],
            "successful_extractions": self.service_stats["successful_extractions"],
            "failed_extractions": self.service_stats["failed_extractions"],
            "success_rate": success_rate,
            "average_processing_time": self.service_stats["average_processing_time"],
            "factor_type_usage": self.service_stats["factor_type_usage"],
            "service_uptime": datetime.now().isoformat()
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.service_stats = {
            "total_extractions": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "average_processing_time": 0.0,
            "factor_type_usage": {
                "technical": 0,
                "fundamental": 0,
                "market": 0,
                "sentiment": 0
            }
        }
        logger.info("服务统计信息已重置")

# 全局服务实例
factor_extraction_service = FactorExtractionService()
