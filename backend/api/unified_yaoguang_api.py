#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星统一API
整合所有瑶光星功能的唯一API接口，消除重复和模拟代码
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from datetime import datetime
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

# 创建统一路由器
unified_yaoguang_router = APIRouter(prefix="/api/yaoguang", tags=["瑶光星统一系统"])

def get_unified_yaoguang_system():
    """获取瑶光星统一系统"""
    try:
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        return unified_yaoguang_system
    except ImportError as e:
        logger.error(f"无法导入瑶光星统一系统: {e}")
        return None

# ==================== 系统管理API ====================

@unified_yaoguang_router.post("/system/initialize")
async def initialize_system(
    system = Depends(get_unified_yaoguang_system)
):
    """初始化瑶光星统一系统"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        result = await system.initialize_system()

        return {
            "success": result.get("success", False),
            "message": result.get("message", "系统初始化处理完成"),
            "data": {
                "system_id": result.get("system_id"),
                "version": result.get("version"),
                "health_status": result.get("health_status", {}),
                "available_modes": result.get("available_modes", [])
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"初始化瑶光星统一系统失败: {e}")
        raise HTTPException(status_code=500, detail=f"系统初始化失败: {str(e)}")

@unified_yaoguang_router.get("/system/status")
async def get_system_status(
    system = Depends(get_unified_yaoguang_system)
):
    """获取系统状态"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        status = await system.get_system_status()

        return {
            "success": status.get("success", False),
            "message": "系统状态获取成功",
            "data": status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")

@unified_yaoguang_router.get("/system/health")
async def system_health_check(
    system = Depends(get_unified_yaoguang_system)
):
    """系统健康检查"""
    try:
        if not system:
            return {
                "success": False,
                "message": "瑶光星统一系统不可用",
                "data": {
                    "overall_health": False,
                    "system_available": False
                },
                "timestamp": datetime.now().isoformat()
            }

        # 执行健康检查
        health_status = await system._verify_system_health()

        return {
            "success": True,
            "message": "健康检查完成",
            "data": {
                "overall_health": health_status.get("overall_health", False),
                "system_available": True,
                "health_details": health_status
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

# ==================== 学习模式API ====================

@unified_yaoguang_router.post("/learning/start")
async def start_learning_session(
    session_config: Optional[Dict[str, Any]] = None,
    system = Depends(get_unified_yaoguang_system)
):
    """启动学习模式会话"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        result = await system.start_learning_session(session_config)

        return {
            "success": result.get("success", False),
            "message": result.get("message", "学习会话启动处理完成"),
            "data": {
                "session_id": result.get("session_id"),
                "mode": result.get("mode"),
                "config": result.get("config", {}),
                "status": "started" if result.get("success") else "failed"
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"启动学习会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动学习会话失败: {str(e)}")

@unified_yaoguang_router.get("/learning/status")
async def get_learning_status(
    system = Depends(get_unified_yaoguang_system)
):
    """获取学习状态"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        status = await system.get_system_status()

        # 提取学习相关状态
        learning_status = {
            "is_active": status.get("system_info", {}).get("current_mode") == "learning",
            "current_session": status.get("system_info", {}).get("current_session"),
            "performance_metrics": status.get("performance_metrics", {}),
            "session_history_count": status.get("session_history_count", 0)
        }

        return {
            "success": True,
            "message": "学习状态获取成功",
            "data": learning_status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取学习状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取学习状态失败: {str(e)}")

# ==================== 实盘交易模式API ====================

@unified_yaoguang_router.post("/trading/start")
async def start_trading_session(
    session_config: Optional[Dict[str, Any]] = None,
    system = Depends(get_unified_yaoguang_system)
):
    """启动实盘交易会话"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        result = await system.start_live_trading_session(session_config)

        return {
            "success": result.get("success", False),
            "message": result.get("message", "实盘交易会话启动处理完成"),
            "data": {
                "session_id": result.get("session_id"),
                "mode": result.get("mode"),
                "config": result.get("config", {}),
                "status": "started" if result.get("success") else "failed"
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"启动实盘交易会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动实盘交易会话失败: {str(e)}")

@unified_yaoguang_router.get("/trading/status")
async def get_trading_status(
    system = Depends(get_unified_yaoguang_system)
):
    """获取实盘交易状态"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        status = await system.get_system_status()

        # 提取交易相关状态
        trading_status = {
            "is_active": status.get("system_info", {}).get("current_mode") == "live_trading",
            "current_session": status.get("system_info", {}).get("current_session"),
            "trading_enabled": status.get("system_config", {}).get("live_trading_mode", {}).get("enabled", False),
            "performance_metrics": status.get("performance_metrics", {}),
            "session_history_count": status.get("session_history_count", 0)
        }

        return {
            "success": True,
            "message": "实盘交易状态获取成功",
            "data": trading_status,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取实盘交易状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实盘交易状态失败: {str(e)}")

# ==================== 会话管理API ====================

@unified_yaoguang_router.post("/session/stop")
async def stop_current_session(
    system = Depends(get_unified_yaoguang_system)
):
    """停止当前会话"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        result = await system.stop_current_session()

        return {
            "success": result.get("success", False),
            "message": result.get("message", "会话停止处理完成"),
            "data": {
                "stopped_session": result.get("stopped_session"),
                "session_mode": result.get("session_mode")
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"停止会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止会话失败: {str(e)}")

@unified_yaoguang_router.get("/session/history")
async def get_session_history(
    limit: int = Query(10, ge=1, le=50),
    mode: Optional[str] = Query(None, description="会话模式过滤: learning 或 live_trading"),
    system = Depends(get_unified_yaoguang_system)
):
    """获取会话历史"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        # 获取会话历史
        all_history = system.session_history

        # 按模式过滤
        if mode:
            filtered_history = [session for session in all_history if session.get("mode") == mode]
        else:
            filtered_history = all_history

        # 限制数量
        limited_history = filtered_history[-limit:] if len(filtered_history) > limit else filtered_history

        return {
            "success": True,
            "message": "会话历史获取成功",
            "data": {
                "sessions": limited_history,
                "total_sessions": len(filtered_history),
                "returned_sessions": len(limited_history),
                "filter_mode": mode
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取会话历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")

# ==================== 配置管理API ====================

@unified_yaoguang_router.get("/config")
async def get_system_config(
    system = Depends(get_unified_yaoguang_system)
):
    """获取系统配置"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        return {
            "success": True,
            "message": "系统配置获取成功",
            "data": system.system_config,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")

@unified_yaoguang_router.put("/config")
async def update_system_config(
    config_updates: Dict[str, Any],
    system = Depends(get_unified_yaoguang_system)
):
    """更新系统配置"""
    try:
        if not system:
            raise HTTPException(status_code=503, detail="瑶光星统一系统不可用")

        # 验证配置更新
        valid_sections = {"learning_mode", "live_trading_mode", "data_sources"}
        
        for section in config_updates.keys():
            if section not in valid_sections:
                raise HTTPException(status_code=400, detail=f"无效的配置节: {section}")

        # 更新配置
        for section, updates in config_updates.items():
            if section in system.system_config:
                system.system_config[section].update(updates)

        return {
            "success": True,
            "message": "系统配置更新成功",
            "data": system.system_config,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新系统配置失败: {str(e)}")
