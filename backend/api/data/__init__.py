#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据API模块
"""

try:
    from .market_data import router as market_data_router
    from .distribution import router as distribution_router
except ImportError as e:
    import logging
    logging.warning(f"数据API导入失败: {e}")
    from fastapi import APIRouter
    market_data_router = APIRouter()
    distribution_router = APIRouter()

__all__ = [
    "market_data_router",
    "distribution_router"
]
