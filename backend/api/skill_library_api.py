#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技能库API接口
提供技能库的HTTP API服务
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

# 技能库系统导入
try:
    from rd_agent_integration.core.skill_library_system import (
        skill_library_manager, SkillUsageRequest, SkillCategory, SkillAccessLevel
    )
    SKILL_LIBRARY_AVAILABLE = True
except ImportError as e:
    SKILL_LIBRARY_AVAILABLE = False
    skill_library_manager = None

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/skill-library", tags=["技能库"])

# Pydantic模型
class SkillUsageRequestModel(BaseModel):
    skill_id: str
    requesting_role: str
    parameters: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None

class SkillCreationRequestModel(BaseModel):
    name: str
    category: str
    access_level: str = "public"
    authorized_roles: List[str] = []
    alpha158_factors: List[str] = []
    talib_indicators: List[str] = []
    custom_logic: Dict[str, Any] = {}
    complexity_level: int = 3
    market_conditions: List[str] = ["all"]
    data_requirements: Dict[str, Any] = {}
    computational_cost: int = 50

class SkillResponse(BaseModel):
    skill_id: str
    name: str
    category: str
    access_level: str
    owner_role: str
    confidence_score: float
    complexity_level: int
    description: str

class SkillUsageResponse(BaseModel):
    success: bool
    result_data: Optional[Dict[str, Any]] = None
    performance_metrics: Dict[str, Any] = {}
    execution_time: float = 0.0
    error: Optional[str] = None

# 依赖函数
def get_skill_library_manager():
    """获取技能库管理器"""
    if not SKILL_LIBRARY_AVAILABLE:
        raise HTTPException(status_code=503, detail="技能库系统不可用")
    return skill_library_manager

@router.get("/status")
async def get_skill_library_status():
    """获取技能库状态"""
    return {
        "available": SKILL_LIBRARY_AVAILABLE,
        "timestamp": datetime.now().isoformat(),
        "message": "技能库系统正常" if SKILL_LIBRARY_AVAILABLE else "技能库系统不可用"
    }

@router.get("/skills", response_model=List[SkillResponse])
async def get_all_skills(
    category: Optional[str] = None,
    role: Optional[str] = None,
    skill_manager = Depends(get_skill_library_manager)
):
    """获取所有技能或按条件筛选"""
    
    try:
        if role:
            # 获取特定角色的技能
            skills = skill_manager.get_role_skills(role)
        else:
            # 获取所有技能
            all_skills = skill_manager.skill_library
            skills = list(all_skills.values())
        
        # 按类别筛选
        if category:
            skills = [skill for skill in skills if skill.category.value == category]
        
        return [
            SkillResponse(
                skill_id=skill.skill_id,
                name=skill.name,
                category=skill.category.value,
                access_level=skill.access_level.value,
                owner_role=skill.owner_role,
                confidence_score=skill.confidence_score,
                complexity_level=skill.complexity_level,
                description=f"{skill.name} - {skill.category.value}类技能"
            )
            for skill in skills
        ]
        
    except Exception as e:
        logger.error(f"  获取技能列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/skills/{skill_id}")
async def get_skill_detail(
    skill_id: str,
    skill_manager = Depends(get_skill_library_manager)
):
    """获取技能详细信息"""
    
    try:
        if skill_id not in skill_manager.skill_library:
            raise HTTPException(status_code=404, detail="技能不存在")
        
        skill = skill_manager.skill_library[skill_id]
        
        return {
            "skill_id": skill.skill_id,
            "name": skill.name,
            "category": skill.category.value,
            "access_level": skill.access_level.value,
            "owner_role": skill.owner_role,
            "authorized_roles": skill.authorized_roles,
            "alpha158_factors": skill.alpha158_factors,
            "talib_indicators": skill.talib_indicators,
            "custom_logic": skill.custom_logic,
            "historical_performance": skill.historical_performance,
            "confidence_score": skill.confidence_score,
            "complexity_level": skill.complexity_level,
            "market_conditions": skill.market_conditions,
            "data_requirements": skill.data_requirements,
            "computational_cost": skill.computational_cost
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"  获取技能详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/skills/use", response_model=SkillUsageResponse)
async def use_skill(
    request: SkillUsageRequestModel,
    skill_manager = Depends(get_skill_library_manager)
):
    """使用技能"""
    
    try:
        # 创建技能使用请求
        usage_request = SkillUsageRequest(
            skill_id=request.skill_id,
            requesting_role=request.requesting_role,
            parameters=request.parameters,
            context=request.context or {"timestamp": datetime.now().isoformat()}
        )
        
        # 执行技能
        result = await skill_manager.request_skill_usage(usage_request)
        
        return SkillUsageResponse(
            success=result.success,
            result_data=result.result_data,
            performance_metrics=result.performance_metrics,
            execution_time=result.execution_time,
            error=result.result_data.get("error") if not result.success else None
        )
        
    except Exception as e:
        logger.error(f"  技能使用失败: {e}")
        return SkillUsageResponse(
            success=False,
            error=str(e)
        )

@router.post("/skills/create")
async def create_skill(
    request: SkillCreationRequestModel,
    creator_role: str,
    skill_manager = Depends(get_skill_library_manager)
):
    """创建新技能"""
    
    try:
        skill_definition = {
            "name": request.name,
            "category": request.category,
            "access_level": request.access_level,
            "authorized_roles": request.authorized_roles,
            "alpha158_factors": request.alpha158_factors,
            "talib_indicators": request.talib_indicators,
            "custom_logic": request.custom_logic,
            "complexity_level": request.complexity_level,
            "market_conditions": request.market_conditions,
            "data_requirements": request.data_requirements,
            "computational_cost": request.computational_cost
        }
        
        skill_id = await skill_manager.create_new_skill(
            creator_role=creator_role,
            skill_definition=skill_definition
        )
        
        if skill_id:
            return {
                "success": True,
                "skill_id": skill_id,
                "message": "技能创建成功"
            }
        else:
            raise HTTPException(status_code=400, detail="技能创建失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"  技能创建失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/roles/{role_name}/skills")
async def get_role_skills(
    role_name: str,
    skill_manager = Depends(get_skill_library_manager)
):
    """获取角色可用技能"""
    
    try:
        skills = skill_manager.get_role_skills(role_name)
        
        return [
            {
                "skill_id": skill.skill_id,
                "name": skill.name,
                "category": skill.category.value,
                "access_level": skill.access_level.value,
                "confidence_score": skill.confidence_score,
                "complexity_level": skill.complexity_level
            }
            for skill in skills
        ]
        
    except Exception as e:
        logger.error(f"  获取角色技能失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statistics")
async def get_skill_statistics(
    skill_manager = Depends(get_skill_library_manager)
):
    """获取技能库统计信息"""
    
    try:
        stats = skill_manager.get_skill_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"  获取技能统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/categories")
async def get_skill_categories():
    """获取技能类别列表"""
    
    try:
        categories = [
            {
                "value": category.value,
                "name": category.name,
                "description": f"{category.value}类技能"
            }
            for category in SkillCategory
        ]
        
        return categories
        
    except Exception as e:
        logger.error(f"  获取技能类别失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/access-levels")
async def get_access_levels():
    """获取访问级别列表"""
    
    try:
        levels = [
            {
                "value": level.value,
                "name": level.name,
                "description": f"{level.value}级别访问"
            }
            for level in SkillAccessLevel
        ]
        
        return levels
        
    except Exception as e:
        logger.error(f"  获取访问级别失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
