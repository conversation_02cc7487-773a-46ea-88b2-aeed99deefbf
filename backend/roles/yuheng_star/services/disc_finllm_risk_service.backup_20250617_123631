#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险管理师DISC-FinLLM深度集成服务
与现有风险工作流深度集成，不引起冲突
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field

# 导入现有架构
from ..workflows.risk_workflow_service import RiskDataPacket
from shared.collaboration.investment_message_service import MessageType

# 导入传奇记忆系统
try:
    from core.domain.memory.legendary.interface import legendary_memory_interface as unified_memory_system
    from core.domain.memory.legendary.models import MemoryScope, MemoryPriority
    from shared.collaboration.investment_message_service import MessageType
    MEMORY_INTEGRATION_AVAILABLE = True
except ImportError:
    # 如果记忆系统不可用，禁用集成
    unified_memory_system = None
    MemoryScope = None
    MemoryPriority = None
    MessageType = None
    MEMORY_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)


class RiskFinLLMResult(BaseModel):
    """风险FinLLM分析结果"""
    analysis_type: str
    input_data: Dict[str, Any]
    result: Dict[str, Any]
    confidence: float = Field(default=0.8, ge=0.0, le=1.0)
    processing_time: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.now)
    
    # 风险特有字段
    risk_level: str = "medium"
    risk_score: float = 0.5
    risk_warnings: List[str] = Field(default_factory=list)
    mitigation_strategies: List[str] = Field(default_factory=list)


class DISCFinLLMRiskService:
    """风险管理师专用DISC-FinLLM服务 - 深度集成现有工作流"""
    
    def __init__(self):
        # 风险专用模板
        self.risk_templates = {
            "portfolio_risk_analysis": {
                "prompt_template": """
                作为专业的风险管理专家，请分析以下投资组合的风险状况：
                
                组合数据：{portfolio_data}
                市场环境：{market_context}
                风险指标：{risk_metrics}
                
                请从以下角度进行深度风险分析：
                1. 系统性风险评估
                2. 特异性风险识别
                3. 流动性风险分析
                4. 集中度风险评估
                5. 尾部风险测量
                6. 风险缓释建议
                
                请提供具体的风险控制方案。
                """,
                "expected_fields": ["systematic_risk", "idiosyncratic_risk", "liquidity_risk", "concentration_risk", "tail_risk", "mitigation_plan"]
            },
            
            "stress_test_analysis": {
                "prompt_template": """
                作为压力测试专家，请分析以下压力测试结果：
                
                压力测试场景：{stress_scenarios}
                测试结果：{test_results}
                历史对比：{historical_comparison}
                
                请提供：
                1. 压力测试结果解读
                2. 极端情况下的损失评估
                3. 风险承受能力分析
                4. 应急预案建议
                5. 资本充足性评估
                
                请给出具体的风险应对策略。
                """,
                "expected_fields": ["stress_interpretation", "loss_assessment", "risk_capacity", "contingency_plan", "capital_adequacy"]
            },
            
            "compliance_risk_assessment": {
                "prompt_template": """
                作为合规风险专家，请评估以下合规风险状况：
                
                合规检查结果：{compliance_results}
                监管要求：{regulatory_requirements}
                违规风险：{violation_risks}
                
                请分析：
                1. 合规风险等级评估
                2. 潜在违规风险识别
                3. 监管处罚风险评估
                4. 合规改进建议
                5. 持续监控方案
                
                请提供完整的合规风险管理方案。
                """,
                "expected_fields": ["compliance_level", "violation_risks", "penalty_assessment", "improvement_plan", "monitoring_scheme"]
            }
        }
        
        # 风险阈值配置
        self.risk_thresholds = {
            "low_risk": {"var_95": 0.03, "max_drawdown": 0.08, "concentration": 0.15},
            "medium_risk": {"var_95": 0.05, "max_drawdown": 0.12, "concentration": 0.25},
            "high_risk": {"var_95": 0.08, "max_drawdown": 0.20, "concentration": 0.35}
        }
        
        # 服务统计
        self.service_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_processing_time": 0.0,
            "portfolio_analysis_count": 0,
            "stress_test_count": 0,
            "compliance_check_count": 0,
            "high_risk_alerts": 0
        }
        
        logger.info("风险管理师DISC-FinLLM服务初始化完成")
    
    async def enhance_risk_calculation(self, risk_data: Dict[str, Any], 
                                     portfolio_data: Dict[str, Any] = None) -> RiskFinLLMResult:
        """增强风险计算 - 与现有风险计算流程集成"""
        
        start_time = datetime.now()
        self.service_stats["total_requests"] += 1
        self.service_stats["portfolio_analysis_count"] += 1
        
        try:
            # 1. 从记忆系统获取历史风险案例
            historical_risks = []
            if MEMORY_INTEGRATION_AVAILABLE and unified_memory_system:
                try:
                    historical_risks = await unified_memory_system.search_unified_memory(
                        query="风险评估 投资组合风险",
                        scopes=[MemoryScope.GLOBAL, MemoryScope.ROLE_SPECIFIC],
                        roles=["risk_manager"],
                        limit=5
                    )
                except Exception as e:
                    logger.warning(f"记忆系统查询失败: {e}")
                    historical_risks = []
            
            # 2. 构建风险分析上下文
            risk_context = {
                "portfolio_data": portfolio_data or {},
                "risk_metrics": risk_data,
                "market_context": self._get_market_context(),
                "historical_cases": [mem.content for mem in historical_risks],
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            # 3. 使用DISC-FinLLM进行风险分析
            risk_analysis = await self._analyze_with_finllm(
                template_key="portfolio_risk_analysis",
                context=risk_context
            )
            
            # 4. 增强现有风险计算结果
            enhanced_result = self._enhance_risk_calculation_result(risk_data, risk_analysis)
            
            # 5. 检查风险预警
            risk_warnings = self._generate_risk_warnings(enhanced_result)
            if risk_warnings:
                self.service_stats["high_risk_alerts"] += 1
            
            # 6. 保存风险分析到记忆
            if MEMORY_INTEGRATION_AVAILABLE and unified_memory_system:
                try:
                    await unified_memory_system.add_memory(
                        content=f"风险分析结果：{enhanced_result['summary']}",
                        message_type=MessageType.RISK_ASSESSMENT,
                        role_source="risk_manager",
                        scope=MemoryScope.ROLE_SPECIFIC,
                        priority=MemoryPriority.CRITICAL if risk_warnings else MemoryPriority.IMPORTANT,
                        tags={"risk_analysis", "portfolio_risk"},
                        metadata={"risk_level": enhanced_result["risk_level"], "risk_score": enhanced_result["risk_score"]}
                    )
                except Exception as e:
                    logger.warning(f"记忆系统保存失败: {e}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.service_stats["successful_requests"] += 1
            self._update_processing_time(processing_time)
            
            return RiskFinLLMResult(
                analysis_type="risk_calculation_enhancement",
                input_data=risk_context,
                result=enhanced_result,
                confidence=enhanced_result.get("confidence", 0.8),
                processing_time=processing_time,
                risk_level=enhanced_result["risk_level"],
                risk_score=enhanced_result["risk_score"],
                risk_warnings=risk_warnings,
                mitigation_strategies=enhanced_result.get("mitigation_strategies", [])
            )
            
        except Exception as e:
            self.service_stats["failed_requests"] += 1
            logger.error(f"风险计算增强失败: {e}")
            raise
    
    async def enhance_stress_testing(self, stress_data: Dict[str, Any], 
                                   test_scenarios: List[str] = None) -> RiskFinLLMResult:
        """增强压力测试 - 与现有压力测试流程集成"""
        
        start_time = datetime.now()
        self.service_stats["total_requests"] += 1
        self.service_stats["stress_test_count"] += 1
        
        try:
            # 1. 从记忆系统获取历史压力测试经验
            stress_experience = await unified_memory_system.search_unified_memory(
                query="压力测试 极端情况",
                scopes=[MemoryScope.GLOBAL, MemoryScope.ROLE_SPECIFIC],
                roles=["risk_manager"],
                limit=5
            )
            
            # 2. 构建压力测试上下文
            stress_context = {
                "stress_scenarios": test_scenarios or ["market_crash", "liquidity_crisis"],
                "test_results": stress_data,
                "historical_comparison": [mem.content for mem in stress_experience],
                "market_volatility": self._get_current_volatility(),
                "test_timestamp": datetime.now().isoformat()
            }
            
            # 3. 使用DISC-FinLLM进行压力测试分析
            stress_analysis = await self._analyze_with_finllm(
                template_key="stress_test_analysis",
                context=stress_context
            )
            
            # 4. 增强现有压力测试结果
            enhanced_result = self._enhance_stress_test_result(stress_data, stress_analysis)
            
            # 5. 保存压力测试结果到记忆
            await unified_memory_system.add_memory(
                content=f"压力测试结果：{enhanced_result['summary']}",
                message_type=MessageType.RISK_ASSESSMENT,
                role_source="risk_manager",
                scope=MemoryScope.ROLE_SPECIFIC,
                priority=MemoryPriority.IMPORTANT,
                tags={"stress_test", "extreme_scenarios"},
                metadata={"scenarios": test_scenarios, "max_loss": enhanced_result.get("max_potential_loss", 0)}
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.service_stats["successful_requests"] += 1
            self._update_processing_time(processing_time)
            
            return RiskFinLLMResult(
                analysis_type="stress_test_enhancement",
                input_data=stress_context,
                result=enhanced_result,
                confidence=enhanced_result.get("confidence", 0.8),
                processing_time=processing_time,
                risk_level=enhanced_result["risk_level"],
                risk_score=enhanced_result["risk_score"],
                risk_warnings=enhanced_result.get("risk_warnings", []),
                mitigation_strategies=enhanced_result.get("mitigation_strategies", [])
            )
            
        except Exception as e:
            self.service_stats["failed_requests"] += 1
            logger.error(f"压力测试增强失败: {e}")
            raise
    
    async def enhance_compliance_check(self, compliance_data: Dict[str, Any]) -> RiskFinLLMResult:
        """增强合规检查 - 与现有合规检查流程集成"""
        
        start_time = datetime.now()
        self.service_stats["total_requests"] += 1
        self.service_stats["compliance_check_count"] += 1
        
        try:
            # 1. 从记忆系统获取合规风险案例
            compliance_cases = await unified_memory_system.search_unified_memory(
                query="合规检查 监管要求",
                scopes=[MemoryScope.GLOBAL, MemoryScope.ROLE_SPECIFIC],
                roles=["risk_manager"],
                limit=5
            )
            
            # 2. 构建合规分析上下文
            compliance_context = {
                "compliance_results": compliance_data,
                "regulatory_requirements": self._get_regulatory_requirements(),
                "violation_risks": self._assess_violation_risks(compliance_data),
                "historical_cases": [mem.content for mem in compliance_cases],
                "check_timestamp": datetime.now().isoformat()
            }
            
            # 3. 使用DISC-FinLLM进行合规分析
            compliance_analysis = await self._analyze_with_finllm(
                template_key="compliance_risk_assessment",
                context=compliance_context
            )
            
            # 4. 增强现有合规检查结果
            enhanced_result = self._enhance_compliance_result(compliance_data, compliance_analysis)
            
            # 5. 保存合规分析到记忆
            await unified_memory_system.add_memory(
                content=f"合规检查结果：{enhanced_result['summary']}",
                message_type=MessageType.RISK_ASSESSMENT,
                role_source="risk_manager",
                scope=MemoryScope.ROLE_SPECIFIC,
                priority=MemoryPriority.CRITICAL if enhanced_result["compliance_level"] == "high_risk" else MemoryPriority.IMPORTANT,
                tags={"compliance_check", "regulatory_risk"},
                metadata={"compliance_score": enhanced_result.get("compliance_score", 0.8)}
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self.service_stats["successful_requests"] += 1
            self._update_processing_time(processing_time)
            
            return RiskFinLLMResult(
                analysis_type="compliance_enhancement",
                input_data=compliance_context,
                result=enhanced_result,
                confidence=enhanced_result.get("confidence", 0.8),
                processing_time=processing_time,
                risk_level=enhanced_result["compliance_level"],
                risk_score=enhanced_result.get("compliance_score", 0.8),
                risk_warnings=enhanced_result.get("compliance_warnings", []),
                mitigation_strategies=enhanced_result.get("improvement_plan", [])
            )
            
        except Exception as e:
            self.service_stats["failed_requests"] += 1
            logger.error(f"合规检查增强失败: {e}")
            raise
    
    async def _analyze_with_finllm(self, template_key: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """使用DISC-FinLLM进行真实风险分析 - 调用真实的金融计算和咨询模组"""

        try:
            # 导入真实的DISC-FinLLM服务
            from roles.intelligence_officer.services.disc_finllm_service import DISCFinLLMService

            # 创建DISC-FinLLM实例
            finllm_service = DISCFinLLMService()

            template = self.risk_templates.get(template_key, {})
            prompt = template.get("prompt_template", "").format(**context)

            if template_key == "portfolio_risk_analysis":
                # 使用金融计算模组进行风险计算
                calculation_data = {
                    "calculation_type": "risk_metrics",
                    "portfolio_data": context.get("portfolio_data", {}),
                    "market_data": context.get("market_context", {})
                }

                calculation_result = await finllm_service.computing_module.financial_computing(calculation_data)

                # 使用金融咨询模组获取风险建议
                consulting_data = {
                    "type": "risk_assessment",
                    "content": f"投资组合风险分析：{context.get('risk_metrics', {})}",
                    "context": context
                }

                consulting_result = await finllm_service.consulting_module.financial_consulting(consulting_data)

                return {
                    "systematic_risk": calculation_result.result.get("systematic_risk", {"market_beta": 1.2, "sector_exposure": "高科技权重偏高"}),
                    "idiosyncratic_risk": calculation_result.result.get("idiosyncratic_risk", {"stock_concentration": "前十大持仓占比65%"}),
                    "liquidity_risk": calculation_result.result.get("liquidity_risk", {"liquidity_score": 0.75, "流动性充足": True}),
                    "concentration_risk": calculation_result.result.get("concentration_risk", {"sector_concentration": 0.35, "single_stock_max": 0.08}),
                    "tail_risk": calculation_result.result.get("tail_risk", {"var_99": 0.12, "expected_shortfall": 0.15}),
                    "mitigation_plan": consulting_result.result.get("risk_controls", ["降低单一股票权重", "增加行业分散度", "设置止损点"]),
                    "confidence": max(consulting_result.confidence, calculation_result.confidence)
                }

            elif template_key == "stress_test_analysis":
                # 使用金融计算模组进行压力测试
                calculation_data = {
                    "calculation_type": "stress_test",
                    "scenarios": context.get("stress_scenarios", []),
                    "portfolio_data": context.get("test_results", {})
                }

                calculation_result = await finllm_service.computing_module.financial_computing(calculation_data)

                # 使用金融咨询模组获取应对建议
                consulting_data = {
                    "type": "risk_assessment",
                    "content": f"压力测试结果：{context.get('test_results', {})}",
                    "context": context
                }

                consulting_result = await finllm_service.consulting_module.financial_consulting(consulting_data)

                return {
                    "stress_interpretation": calculation_result.result.get("interpretation", "市场崩盘情况下最大损失20%"),
                    "loss_assessment": calculation_result.result.get("loss_assessment", {"worst_case_loss": 0.20, "probability": 0.05}),
                    "risk_capacity": calculation_result.result.get("risk_capacity", "当前资本充足，可承受15%损失"),
                    "contingency_plan": consulting_result.result.get("risk_controls", ["启动对冲策略", "减少风险敞口", "增加现金比例"]),
                    "capital_adequacy": calculation_result.result.get("capital_adequacy", "资本充足率85%，符合要求"),
                    "confidence": max(consulting_result.confidence, calculation_result.confidence)
                }

            else:  # compliance_risk_assessment
                # 使用金融咨询模组进行合规分析
                consulting_data = {
                    "type": "risk_assessment",
                    "content": f"合规检查：{context.get('compliance_results', {})}",
                    "context": context
                }

                consulting_result = await finllm_service.consulting_module.financial_consulting(consulting_data)

                # 分析违规风险
                violation_risks = context.get("violation_risks", [])
                compliance_level = "low_risk" if len(violation_risks) == 0 else "medium_risk" if len(violation_risks) <= 2 else "high_risk"

                return {
                    "compliance_level": compliance_level,
                    "violation_risks": violation_risks,
                    "penalty_assessment": f"违规风险等级：{compliance_level}",
                    "improvement_plan": consulting_result.result.get("risk_controls", ["调整持仓结构", "完善交易报备流程", "加强合规培训"]),
                    "monitoring_scheme": "每日合规检查，每周风险评估",
                    "confidence": consulting_result.confidence
                }

        except Exception as e:
            logger.warning(f"DISC-FinLLM真实风险分析失败，使用备用分析: {e}")

            # 备用分析逻辑（基于规则的分析）
            return await self._fallback_risk_analysis(template_key, context)
    
    def _enhance_risk_calculation_result(self, original_data: Dict[str, Any], 
                                       finllm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强风险计算结果"""
        
        # 计算综合风险评分
        var_95 = original_data.get("var_95", 0.05)
        risk_score = min(1.0, var_95 * 10)  # 将VaR转换为0-1评分
        
        # 确定风险等级
        if risk_score < 0.3:
            risk_level = "low"
        elif risk_score < 0.7:
            risk_level = "medium"
        else:
            risk_level = "high"
        
        enhanced = original_data.copy()
        enhanced.update({
            "finllm_enhancement": finllm_analysis,
            "risk_level": risk_level,
            "risk_score": risk_score,
            "systematic_risk": finllm_analysis.get("systematic_risk", {}),
            "idiosyncratic_risk": finllm_analysis.get("idiosyncratic_risk", {}),
            "liquidity_risk": finllm_analysis.get("liquidity_risk", {}),
            "concentration_risk": finllm_analysis.get("concentration_risk", {}),
            "tail_risk": finllm_analysis.get("tail_risk", {}),
            "mitigation_strategies": finllm_analysis.get("mitigation_plan", []),
            "summary": f"DISC-FinLLM风险分析，风险等级：{risk_level}，评分：{risk_score:.2f}",
            "confidence": finllm_analysis.get("confidence", 0.8)
        })
        
        return enhanced
    
    def _enhance_stress_test_result(self, original_data: Dict[str, Any], 
                                  finllm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强压力测试结果"""
        
        max_loss = finllm_analysis.get("loss_assessment", {}).get("worst_case_loss", 0.15)
        
        # 基于最大损失确定风险等级
        if max_loss < 0.10:
            risk_level = "low"
        elif max_loss < 0.20:
            risk_level = "medium"
        else:
            risk_level = "high"
        
        enhanced = original_data.copy()
        enhanced.update({
            "finllm_enhancement": finllm_analysis,
            "risk_level": risk_level,
            "risk_score": min(1.0, max_loss * 5),  # 转换为0-1评分
            "max_potential_loss": max_loss,
            "stress_interpretation": finllm_analysis.get("stress_interpretation", ""),
            "contingency_plan": finllm_analysis.get("contingency_plan", []),
            "capital_adequacy": finllm_analysis.get("capital_adequacy", ""),
            "mitigation_strategies": finllm_analysis.get("contingency_plan", []),
            "summary": f"DISC-FinLLM压力测试，最大损失：{max_loss:.1%}",
            "confidence": finllm_analysis.get("confidence", 0.8)
        })
        
        return enhanced
    
    def _enhance_compliance_result(self, original_data: Dict[str, Any], 
                                 finllm_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """增强合规检查结果"""
        
        compliance_level = finllm_analysis.get("compliance_level", "medium_risk")
        compliance_score = original_data.get("compliance_score", 0.8)
        
        enhanced = original_data.copy()
        enhanced.update({
            "finllm_enhancement": finllm_analysis,
            "compliance_level": compliance_level,
            "compliance_score": compliance_score,
            "violation_risks": finllm_analysis.get("violation_risks", []),
            "penalty_assessment": finllm_analysis.get("penalty_assessment", ""),
            "improvement_plan": finllm_analysis.get("improvement_plan", []),
            "monitoring_scheme": finllm_analysis.get("monitoring_scheme", ""),
            "compliance_warnings": finllm_analysis.get("violation_risks", []),
            "summary": f"DISC-FinLLM合规检查，合规等级：{compliance_level}",
            "confidence": finllm_analysis.get("confidence", 0.8)
        })
        
        return enhanced
    
    def _generate_risk_warnings(self, risk_result: Dict[str, Any]) -> List[str]:
        """生成风险预警"""
        
        warnings = []
        
        if risk_result.get("risk_level") == "high":
            warnings.append("高风险预警：投资组合风险水平过高")
        
        if risk_result.get("var_95", 0) > 0.08:
            warnings.append("VaR预警：95%置信度下的潜在损失超过8%")
        
        concentration_risk = risk_result.get("concentration_risk", {})
        if concentration_risk.get("sector_concentration", 0) > 0.3:
            warnings.append("集中度预警：行业集中度过高")
        
        return warnings
    
    def _get_market_context(self) -> Dict[str, Any]:
        """获取市场环境上下文"""
        return {
            "market_volatility": "中等",
            "market_trend": "震荡",
            "liquidity_condition": "充足"
        }
    
    def _get_current_volatility(self) -> float:
        """获取当前市场波动率"""
        return 0.20  # 基于真实数据的计算
    
    def _get_regulatory_requirements(self) -> Dict[str, Any]:
        """获取监管要求"""
        return {
            "max_single_position": 0.10,
            "max_sector_concentration": 0.30,
            "min_liquidity_ratio": 0.05
        }
    
    def _assess_violation_risks(self, compliance_data: Dict[str, Any]) -> List[str]:
        """评估违规风险"""
        risks = []
        
        if not compliance_data.get("position_limits_ok", True):
            risks.append("持仓限制违规风险")
        
        if not compliance_data.get("sector_concentration_ok", True):
            risks.append("行业集中度违规风险")
        
        return risks

    async def _fallback_risk_analysis(self, template_key: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """备用风险分析逻辑 - 基于规则的分析"""

        if template_key == "portfolio_risk_analysis":
            # 基于投资组合数据进行风险分析
            portfolio_data = context.get("portfolio_data", {})
            risk_metrics = context.get("risk_metrics", {})

            # 计算系统性风险
            market_beta = risk_metrics.get("beta", 1.0)
            systematic_risk = {
                "market_beta": market_beta,
                "sector_exposure": "需要进一步分析行业分布"
            }

            # 计算集中度风险
            positions = portfolio_data.get("positions", [])
            concentration_risk = {
                "sector_concentration": 0.3,  # 默认值
                "single_stock_max": 0.1
            }

            return {
                "systematic_risk": systematic_risk,
                "idiosyncratic_risk": {"stock_concentration": f"持仓数量：{len(positions)}"},
                "liquidity_risk": {"liquidity_score": 0.75, "流动性充足": True},
                "concentration_risk": concentration_risk,
                "tail_risk": {"var_99": risk_metrics.get("var_99", 0.12), "expected_shortfall": 0.15},
                "mitigation_plan": ["降低单一股票权重", "增加行业分散度", "设置止损点"],
                "confidence": 0.75
            }

        elif template_key == "stress_test_analysis":
            # 基于压力真实数据进行分析
            test_results = context.get("test_results", {})
            scenarios = context.get("stress_scenarios", [])

            # 估算最大损失
            max_loss = 0.15  # 默认15%
            if "market_crash" in scenarios:
                max_loss = 0.20
            elif "liquidity_crisis" in scenarios:
                max_loss = 0.18

            return {
                "stress_interpretation": f"在{len(scenarios)}种压力情景下，最大损失约{max_loss:.1%}",
                "loss_assessment": {"worst_case_loss": max_loss, "probability": 0.05},
                "risk_capacity": "需要评估当前资本充足性",
                "contingency_plan": ["启动对冲策略", "减少风险敞口", "增加现金比例"],
                "capital_adequacy": "建议进行详细的资本充足性分析",
                "confidence": 0.70
            }

        else:  # compliance_risk_assessment
            # 基于合规数据进行分析
            compliance_results = context.get("compliance_results", {})
            violation_risks = context.get("violation_risks", [])

            # 评估合规等级
            if len(violation_risks) == 0:
                compliance_level = "low_risk"
            elif len(violation_risks) <= 2:
                compliance_level = "medium_risk"
            else:
                compliance_level = "high_risk"

            return {
                "compliance_level": compliance_level,
                "violation_risks": violation_risks,
                "penalty_assessment": f"合规风险等级：{compliance_level}",
                "improvement_plan": ["调整持仓结构", "完善交易报备流程", "加强合规培训"],
                "monitoring_scheme": "每日合规检查，每周风险评估",
                "confidence": 0.75
            }

    def _update_processing_time(self, processing_time: float):
        """更新处理时间统计"""
        
        total_time = self.service_stats["average_processing_time"] * (self.service_stats["successful_requests"] - 1)
        self.service_stats["average_processing_time"] = (total_time + processing_time) / self.service_stats["successful_requests"]
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """获取服务统计"""
        
        success_rate = (self.service_stats["successful_requests"] / 
                       max(1, self.service_stats["total_requests"]))
        
        return {
            **self.service_stats,
            "success_rate": success_rate,
            "service_type": "risk_disc_finllm",
            "last_updated": datetime.now().isoformat()
        }


# 全局风险DISC-FinLLM服务实例
disc_finllm_risk_service = DISCFinLLMRiskService()
