#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整修复测试
验证所有修复是否成功，包括：
1. 本地化RD-Agent（无降级模式）
2. 各星座自动化系统
3. 技能库系统
4. 多角色协作
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_rd_agent_integration():
    """测试RD-Agent集成服务（无降级模式）"""
    try:
        logger.info("🔬 测试RD-Agent集成服务（无降级模式）...")
        
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        # 测试服务状态
        status = rd_agent_integration_service.get_service_status()
        logger.info(f"RD-Agent服务状态: {status}")
        
        # 验证只使用本地化RD-Agent
        if status.get("use_professional") and status.get("local_rd_agent_available"):
            logger.info("✅ 本地化RD-Agent正常工作")
        else:
            logger.error("❌ 本地化RD-Agent不可用")
            return False
        
        # 测试因子生成（必须成功）
        research_config = {
            "target_stocks": ["000001.XSHE", "000002.XSHE"],
            "factor_count": 5,
            "research_type": "factor_generation"
        }
        
        logger.info("开始测试因子生成（必须成功）...")
        factors = await rd_agent_integration_service.generate_new_factors(research_config)
        
        if factors and len(factors) > 0:
            logger.info(f"✅ 因子生成成功: {len(factors)} 个因子")
            for i, factor in enumerate(factors[:3]):
                logger.info(f"  因子 {i+1}: {factor.get('factor_name', 'Unknown')} - {factor.get('factor_category', 'Unknown')}")
        else:
            logger.error("❌ 因子生成失败或返回空结果")
            return False
        
        # 测试Alpha158因子
        logger.info("测试Alpha158因子获取...")
        alpha158_factors = await rd_agent_integration_service.get_alpha158_factors()
        
        if alpha158_factors and len(alpha158_factors) == 158:
            logger.info(f"✅ Alpha158因子获取成功: {len(alpha158_factors)} 个因子")
        else:
            logger.error("❌ Alpha158因子获取失败")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ RD-Agent集成测试失败: {e}")
        return False

async def test_automation_systems():
    """测试各星座自动化系统"""
    try:
        logger.info("⭐ 测试各星座自动化系统...")
        
        automation_systems = []
        
        # 测试天枢星自动化系统
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            status = tianshu_automation_system.get_automation_status()
            logger.info(f"✅ 天枢星自动化系统可用: {status['system_name']}")
            automation_systems.append("tianshu")
        except ImportError as e:
            logger.error(f"❌ 天枢星自动化系统不可用: {e}")
        
        # 测试天玑星自动化系统
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            status = tianji_automation_system.get_automation_status()
            logger.info(f"✅ 天玑星自动化系统可用: {status['system_name']}")
            automation_systems.append("tianji")
        except ImportError as e:
            logger.error(f"❌ 天玑星自动化系统不可用: {e}")
        
        # 测试天璇星自动化系统
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            status = tianxuan_automation_system.get_automation_status()
            logger.info(f"✅ 天璇星自动化系统可用: {status['system_name']}")
            automation_systems.append("tianxuan")
        except ImportError as e:
            logger.error(f"❌ 天璇星自动化系统不可用: {e}")
        
        # 测试玉衡星自动化系统
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            status = yuheng_automation_system.get_automation_status()
            logger.info(f"✅ 玉衡星自动化系统可用: {status['system_name']}")
            automation_systems.append("yuheng")
        except ImportError as e:
            logger.error(f"❌ 玉衡星自动化系统不可用: {e}")
        
        # 测试自动化系统功能
        if len(automation_systems) >= 3:  # 至少3个系统可用
            logger.info("测试自动化系统功能...")
            
            # 测试天枢星市场分析
            if "tianshu" in automation_systems:
                context = {
                    "stock_code": "000001.XSHE",
                    "task_type": "market_analysis",
                    "session_id": "test_session"
                }
                result = await tianshu_automation_system.execute_market_analysis(context)
                if result.get("success"):
                    logger.info("✅ 天枢星市场分析功能正常")
                else:
                    logger.warning("⚠️ 天枢星市场分析功能异常")
            
            # 测试天玑星风险分析
            if "tianji" in automation_systems:
                context = {
                    "stock_code": "000001.XSHE",
                    "task_type": "risk_analysis",
                    "session_id": "test_session"
                }
                result = await tianji_automation_system.execute_risk_analysis(context)
                if result.get("success"):
                    logger.info("✅ 天玑星风险分析功能正常")
                else:
                    logger.warning("⚠️ 天玑星风险分析功能异常")
            
            # 测试天璇星技术分析
            if "tianxuan" in automation_systems:
                context = {
                    "stock_code": "000001.XSHE",
                    "task_type": "technical_analysis",
                    "session_id": "test_session"
                }
                result = await tianxuan_automation_system.execute_technical_analysis(context)
                if result.get("success"):
                    logger.info("✅ 天璇星技术分析功能正常")
                else:
                    logger.warning("⚠️ 天璇星技术分析功能异常")
            
            # 测试玉衡星交易自动化
            if "yuheng" in automation_systems:
                context = {
                    "stock_code": "000001.XSHE",
                    "task_type": "trading_automation",
                    "session_id": "test_session",
                    "trading_decision": {
                        "action": "buy",
                        "quantity": 1000,
                        "price": 10.0
                    },
                    "mode": "learning"
                }
                result = await yuheng_automation_system.execute_trading_automation(context)
                if result.get("success"):
                    logger.info("✅ 玉衡星交易自动化功能正常")
                else:
                    logger.warning("⚠️ 玉衡星交易自动化功能异常")
            
            return True
        else:
            logger.error(f"❌ 自动化系统数量不足: {len(automation_systems)}/4")
            return False
        
    except Exception as e:
        logger.error(f"❌ 自动化系统测试失败: {e}")
        return False

async def test_skill_library():
    """测试技能库系统"""
    try:
        logger.info("📚 测试技能库系统...")
        
        # 测试技能库导入
        try:
            from rd_agent_integration.core.skill_library_system import skill_library_manager
            logger.info("✅ 技能库系统导入成功")
            
            # 测试技能库状态
            if hasattr(skill_library_manager, 'skill_library'):
                skill_count = len(skill_library_manager.skill_library)
                logger.info(f"✅ 技能库包含 {skill_count} 个技能")
                return True
            else:
                logger.warning("⚠️ 技能库为空")
                return False
                
        except ImportError as e:
            logger.error(f"❌ 技能库系统导入失败: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 技能库系统测试失败: {e}")
        return False

async def test_yaoguang_unified_system():
    """测试瑶光星统一系统"""
    try:
        logger.info("🌟 测试瑶光星统一系统...")
        
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 测试系统初始化
        logger.info("初始化瑶光星统一系统...")
        init_result = await unified_yaoguang_system.initialize_system()
        
        if init_result.get("success"):
            logger.info("✅ 瑶光星统一系统初始化成功")
        else:
            logger.warning(f"⚠️ 瑶光星统一系统初始化失败: {init_result.get('error')}")
        
        # 测试学习会话（简化版）
        session_config = {
            "stocks_per_session": 1,
            "data_years": 1,
            "strategy_testing_enabled": True
        }
        
        logger.info("启动学习会话...")
        session_result = await unified_yaoguang_system.start_learning_session(session_config)
        
        if session_result.get("success"):
            session_id = session_result["session_id"]
            logger.info(f"✅ 学习会话启动成功: {session_id}")
            
            # 等待一段时间让学习流程开始
            await asyncio.sleep(2)
            
            # 停止会话
            logger.info("停止学习会话...")
            stop_result = await unified_yaoguang_system.stop_current_session()
            if stop_result.get("success"):
                logger.info("✅ 学习会话停止成功")
            else:
                logger.warning("⚠️ 学习会话停止失败")
            
            return True
        else:
            logger.error(f"❌ 学习会话启动失败: {session_result.get('error')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 瑶光星统一系统测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始完整修复测试")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 测试1：RD-Agent集成（无降级模式）
    test_results["rd_agent_integration"] = await test_rd_agent_integration()
    
    # 测试2：各星座自动化系统
    test_results["automation_systems"] = await test_automation_systems()
    
    # 测试3：技能库系统
    test_results["skill_library"] = await test_skill_library()
    
    # 测试4：瑶光星统一系统
    test_results["yaoguang_unified_system"] = await test_yaoguang_unified_system()
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 完整修复测试结果:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"📈 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        logger.info("🎉 所有修复完成，系统完全正常！")
    elif success_rate >= 75:
        logger.info("✅ 主要修复完成，系统基本正常！")
    elif success_rate >= 50:
        logger.info("⚠️ 部分修复完成，需要进一步优化")
    else:
        logger.info("❌ 修复不完整，需要更多工作")
    
    return success_rate

if __name__ == "__main__":
    asyncio.run(main())
