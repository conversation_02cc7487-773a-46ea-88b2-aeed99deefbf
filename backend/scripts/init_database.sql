-- Seven Stars Quantitative Trading System Database Schema
-- 七星量化交易系统数据库表结构

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS seven_stars_prod 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE seven_stars_prod;

-- 1. 股票基础信息表
CREATE TABLE IF NOT EXISTS stocks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stock_code VARCHAR(20) NOT NULL UNIQUE COMMENT '股票代码',
    stock_name VARCHAR(100) NOT NULL COMMENT '股票名称',
    market VARCHAR(10) NOT NULL COMMENT '市场(SZ/SH/BJ)',
    industry VARCHAR(50) COMMENT '行业',
    sector VARCHAR(50) COMMENT '板块',
    list_date DATE COMMENT '上市日期',
    delist_date DATE COMMENT '退市日期',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stock_code (stock_code),
    INDEX idx_market (market),
    INDEX idx_industry (industry)
) COMMENT '股票基础信息表';

-- 2. 股票价格数据表
CREATE TABLE IF NOT EXISTS stock_prices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3) NOT NULL COMMENT '开盘价',
    high_price DECIMAL(10,3) NOT NULL COMMENT '最高价',
    low_price DECIMAL(10,3) NOT NULL COMMENT '最低价',
    close_price DECIMAL(10,3) NOT NULL COMMENT '收盘价',
    volume BIGINT NOT NULL COMMENT '成交量',
    amount DECIMAL(20,2) NOT NULL COMMENT '成交额',
    turnover_rate DECIMAL(8,4) COMMENT '换手率',
    pe_ratio DECIMAL(10,2) COMMENT '市盈率',
    pb_ratio DECIMAL(10,2) COMMENT '市净率',
    data_source VARCHAR(20) DEFAULT 'jqdata' COMMENT '数据源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date),
    INDEX idx_trade_date (trade_date),
    INDEX idx_stock_code (stock_code)
) COMMENT '股票价格数据表';

-- 3. 技术指标表
CREATE TABLE IF NOT EXISTS technical_indicators (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    ma5 DECIMAL(10,3) COMMENT '5日均线',
    ma10 DECIMAL(10,3) COMMENT '10日均线',
    ma20 DECIMAL(10,3) COMMENT '20日均线',
    ma60 DECIMAL(10,3) COMMENT '60日均线',
    rsi DECIMAL(8,4) COMMENT 'RSI指标',
    macd_dif DECIMAL(10,6) COMMENT 'MACD DIF',
    macd_dea DECIMAL(10,6) COMMENT 'MACD DEA',
    macd_histogram DECIMAL(10,6) COMMENT 'MACD柱状图',
    kdj_k DECIMAL(8,4) COMMENT 'KDJ K值',
    kdj_d DECIMAL(8,4) COMMENT 'KDJ D值',
    kdj_j DECIMAL(8,4) COMMENT 'KDJ J值',
    boll_upper DECIMAL(10,3) COMMENT '布林带上轨',
    boll_middle DECIMAL(10,3) COMMENT '布林带中轨',
    boll_lower DECIMAL(10,3) COMMENT '布林带下轨',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stock_date (stock_code, trade_date),
    INDEX idx_trade_date (trade_date)
) COMMENT '技术指标表';

-- 4. 交易订单表
CREATE TABLE IF NOT EXISTS trading_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(50) NOT NULL UNIQUE COMMENT '订单ID',
    stock_code VARCHAR(20) NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    action ENUM('buy', 'sell') NOT NULL COMMENT '交易方向',
    order_type ENUM('market', 'limit', 'stop', 'stop_limit') DEFAULT 'market' COMMENT '订单类型',
    quantity INT NOT NULL COMMENT '数量',
    price DECIMAL(10,3) COMMENT '价格',
    executed_quantity INT DEFAULT 0 COMMENT '已成交数量',
    executed_price DECIMAL(10,3) COMMENT '成交价格',
    commission DECIMAL(10,2) DEFAULT 0 COMMENT '手续费',
    status ENUM('pending', 'submitted', 'partial_filled', 'filled', 'cancelled', 'rejected') DEFAULT 'pending' COMMENT '订单状态',
    strategy_id VARCHAR(50) COMMENT '策略ID',
    user_id VARCHAR(50) COMMENT '用户ID',
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    execute_time TIMESTAMP NULL COMMENT '执行时间',
    cancel_time TIMESTAMP NULL COMMENT '取消时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_stock_code (stock_code),
    INDEX idx_status (status),
    INDEX idx_submit_time (submit_time)
) COMMENT '交易订单表';

-- 5. 持仓表
CREATE TABLE IF NOT EXISTS positions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    quantity INT NOT NULL COMMENT '持仓数量',
    available_quantity INT NOT NULL COMMENT '可用数量',
    average_cost DECIMAL(10,3) NOT NULL COMMENT '平均成本',
    current_price DECIMAL(10,3) COMMENT '当前价格',
    market_value DECIMAL(15,2) COMMENT '市值',
    profit_loss DECIMAL(15,2) COMMENT '盈亏',
    profit_loss_ratio DECIMAL(8,4) COMMENT '盈亏比例',
    user_id VARCHAR(50) COMMENT '用户ID',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_stock (user_id, stock_code),
    INDEX idx_stock_code (stock_code)
) COMMENT '持仓表';

-- 6. 策略表
CREATE TABLE IF NOT EXISTS strategies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    strategy_id VARCHAR(50) NOT NULL UNIQUE COMMENT '策略ID',
    strategy_name VARCHAR(100) NOT NULL COMMENT '策略名称',
    strategy_type VARCHAR(50) NOT NULL COMMENT '策略类型',
    description TEXT COMMENT '策略描述',
    parameters JSON COMMENT '策略参数',
    status ENUM('active', 'inactive', 'testing') DEFAULT 'inactive' COMMENT '策略状态',
    creator_id VARCHAR(50) COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_strategy_id (strategy_id),
    INDEX idx_status (status)
) COMMENT '策略表';

-- 7. 策略执行记录表
CREATE TABLE IF NOT EXISTS strategy_executions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    execution_id VARCHAR(50) NOT NULL UNIQUE COMMENT '执行ID',
    strategy_id VARCHAR(50) NOT NULL,
    stock_code VARCHAR(20) NOT NULL,
    signal_type ENUM('buy', 'sell', 'hold') NOT NULL COMMENT '信号类型',
    signal_strength DECIMAL(4,3) COMMENT '信号强度',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    result JSON COMMENT '执行结果',
    performance_metrics JSON COMMENT '性能指标',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_strategy_id (strategy_id),
    INDEX idx_execution_time (execution_time)
) COMMENT '策略执行记录表';

-- 8. 风险评估记录表
CREATE TABLE IF NOT EXISTS risk_assessments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    assessment_id VARCHAR(50) NOT NULL UNIQUE COMMENT '评估ID',
    stock_code VARCHAR(20) NOT NULL,
    assessment_type VARCHAR(50) NOT NULL COMMENT '评估类型',
    risk_level ENUM('low', 'medium', 'high', 'critical') NOT NULL COMMENT '风险等级',
    risk_score DECIMAL(4,3) NOT NULL COMMENT '风险评分',
    risk_factors JSON COMMENT '风险因子',
    approved BOOLEAN NOT NULL COMMENT '是否通过',
    assessor VARCHAR(50) COMMENT '评估者',
    assessment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_stock_code (stock_code),
    INDEX idx_risk_level (risk_level),
    INDEX idx_assessment_time (assessment_time)
) COMMENT '风险评估记录表';

-- 9. 新闻数据表
CREATE TABLE IF NOT EXISTS news_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    news_id VARCHAR(50) NOT NULL UNIQUE COMMENT '新闻ID',
    title VARCHAR(500) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容',
    source VARCHAR(100) NOT NULL COMMENT '来源',
    author VARCHAR(100) COMMENT '作者',
    publish_time TIMESTAMP NOT NULL COMMENT '发布时间',
    related_stocks JSON COMMENT '相关股票',
    sentiment ENUM('positive', 'negative', 'neutral') COMMENT '情感倾向',
    importance_score DECIMAL(4,3) COMMENT '重要性评分',
    url VARCHAR(1000) COMMENT '原文链接',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_publish_time (publish_time),
    INDEX idx_source (source),
    INDEX idx_sentiment (sentiment)
) COMMENT '新闻数据表';

-- 10. 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(50) NOT NULL UNIQUE COMMENT '日志ID',
    log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL COMMENT '日志级别',
    component VARCHAR(100) NOT NULL COMMENT '组件名称',
    message TEXT NOT NULL COMMENT '日志消息',
    details JSON COMMENT '详细信息',
    user_id VARCHAR(50) COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_log_level (log_level),
    INDEX idx_component (component),
    INDEX idx_created_at (created_at)
) COMMENT '系统日志表';

-- 11. 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL UNIQUE COMMENT '用户ID',
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(200) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('admin', 'trader', 'analyst', 'viewer') DEFAULT 'viewer' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    last_login TIMESTAMP COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 12. 配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type VARCHAR(50) DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(500) COMMENT '描述',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key)
) COMMENT '系统配置表';

-- 插入默认配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('system.version', '1.0.0', 'string', '系统版本'),
('trading.commission_rate', '0.0003', 'float', '交易手续费率'),
('trading.max_position_ratio', '0.3', 'float', '最大单股仓位比例'),
('risk.max_drawdown', '0.1', 'float', '最大回撤限制'),
('data.primary_source', 'jqdata', 'string', '主要数据源'),
('ai.deepseek_enabled', 'true', 'boolean', 'DeepSeek AI服务启用状态'),
('monitoring.alert_enabled', 'true', 'boolean', '告警功能启用状态')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = CURRENT_TIMESTAMP;

-- 创建视图
CREATE OR REPLACE VIEW v_latest_prices AS
SELECT 
    sp.stock_code,
    s.stock_name,
    sp.close_price as latest_price,
    sp.trade_date as latest_date,
    sp.volume,
    sp.amount,
    sp.turnover_rate,
    sp.pe_ratio,
    sp.pb_ratio
FROM stock_prices sp
INNER JOIN (
    SELECT stock_code, MAX(trade_date) as max_date
    FROM stock_prices
    GROUP BY stock_code
) latest ON sp.stock_code = latest.stock_code AND sp.trade_date = latest.max_date
LEFT JOIN stocks s ON sp.stock_code = s.stock_code;

-- 创建存储过程：计算技术指标
DELIMITER //
CREATE PROCEDURE CalculateTechnicalIndicators(IN p_stock_code VARCHAR(20), IN p_days INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_date DATE;
    DECLARE v_close DECIMAL(10,3);
    
    -- 计算移动平均线等技术指标的存储过程
    -- 这里可以添加具体的计算逻辑
    
    SELECT CONCAT('技术指标计算完成: ', p_stock_code) as result;
END //
DELIMITER ;

-- 创建触发器：更新持仓信息
DELIMITER //
CREATE TRIGGER tr_update_position_after_order
AFTER UPDATE ON trading_orders
FOR EACH ROW
BEGIN
    IF NEW.status = 'filled' AND OLD.status != 'filled' THEN
        -- 更新持仓信息的逻辑
        INSERT INTO positions (stock_code, stock_name, quantity, available_quantity, average_cost, user_id)
        VALUES (NEW.stock_code, NEW.stock_name, 
                CASE WHEN NEW.action = 'buy' THEN NEW.executed_quantity ELSE -NEW.executed_quantity END,
                CASE WHEN NEW.action = 'buy' THEN NEW.executed_quantity ELSE -NEW.executed_quantity END,
                NEW.executed_price, NEW.user_id)
        ON DUPLICATE KEY UPDATE
        quantity = quantity + CASE WHEN NEW.action = 'buy' THEN NEW.executed_quantity ELSE -NEW.executed_quantity END,
        available_quantity = available_quantity + CASE WHEN NEW.action = 'buy' THEN NEW.executed_quantity ELSE -NEW.executed_quantity END,
        average_cost = (average_cost * quantity + NEW.executed_price * NEW.executed_quantity) / (quantity + NEW.executed_quantity);
    END IF;
END //
DELIMITER ;
