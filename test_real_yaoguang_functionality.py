#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星真实功能测试
验证所有功能都是真实实现，不是模拟
"""

import sys
sys.path.append('backend')

import asyncio
import requests
from datetime import datetime

async def test_real_yaoguang_functionality():
    """测试瑶光星真实功能"""
    
    print("🌟 瑶光星真实功能完整测试")
    print("="*80)
    print("目标：验证所有功能都是真实实现，不是模拟")
    print("="*80)
    
    test_results = {
        "unified_system_real": False,
        "kaiyang_integration_real": False,
        "tianquan_integration_real": False,
        "data_collection_real": False,
        "learning_optimization_real": False,
        "rd_agent_integration_real": False,
        "four_stars_debate_real": False,
        "api_functionality_real": False
    }
    
    # 第一步：测试统一系统真实性
    print("\n1️⃣ 测试统一系统真实性")
    try:
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 初始化系统
        init_result = await unified_yaoguang_system.initialize_system()
        
        if init_result.get("success"):
            print(f"   ✅ 统一系统初始化成功")
            
            # 检查自动化引擎是否集成了真实服务
            automation_engine = unified_yaoguang_system.automation_engine
            real_services = 0
            
            for service_name, service in automation_engine.items():
                if service is not None:
                    real_services += 1
                    print(f"   ✅ {service_name}: 真实服务已集成")
                else:
                    print(f"   ❌ {service_name}: 服务不可用")
            
            if real_services >= 2:
                test_results["unified_system_real"] = True
                print(f"   ✅ 统一系统真实性验证通过: {real_services}/5 真实服务")
            else:
                print(f"   ❌ 统一系统真实性不足: 仅 {real_services}/5 真实服务")
        else:
            print(f"   ❌ 统一系统初始化失败")
            
    except Exception as e:
        print(f"   ❌ 统一系统测试失败: {e}")
    
    # 第二步：测试开阳星真实集成
    print("\n2️⃣ 测试开阳星真实集成")
    try:
        if unified_yaoguang_system.automation_engine.get("kaiyang_selection"):
            kaiyang_service = unified_yaoguang_system.automation_engine["kaiyang_selection"]
            
            # 测试真实选股功能
            selection_context = {
                "selection_type": "test",
                "target_count": 3,
                "market_context": {"sentiment": 0.6},
                "requester": "瑶光星真实性测试"
            }
            
            result = await kaiyang_service.select_stocks(selection_context)
            
            if result.get("success"):
                selected_stocks = result.get("selection_result", {}).get("selected_stocks", [])
                print(f"   ✅ 开阳星真实选股成功: {len(selected_stocks)} 只股票")
                
                if selected_stocks:
                    first_stock = selected_stocks[0]
                    print(f"   📊 示例股票: {first_stock.get('stock_code')} - {first_stock.get('stock_name')}")
                    test_results["kaiyang_integration_real"] = True
            else:
                print(f"   ❌ 开阳星选股失败: {result.get('error')}")
        else:
            print(f"   ❌ 开阳星服务未集成")
            
    except Exception as e:
        print(f"   ❌ 开阳星集成测试失败: {e}")
    
    # 第三步：测试天权星真实集成
    print("\n3️⃣ 测试天权星真实集成")
    try:
        if unified_yaoguang_system.automation_engine.get("tianquan_strategies"):
            tianquan_service = unified_yaoguang_system.automation_engine["tianquan_strategies"]
            
            # 测试真实战法执行
            strategy_request = {
                "strategy_type": "trend_following",
                "stock_code": "000001.XSHE",
                "execution_mode": "test",
                "learning_context": True
            }
            
            result = await tianquan_service.execute_strategy_backtest(strategy_request)
            
            if result.get("success"):
                execution_result = result.get("execution_result", {})
                print(f"   ✅ 天权星真实战法执行成功")
                print(f"   📊 总交易次数: {execution_result.get('total_trades', 0)}")
                print(f"   📈 胜率: {execution_result.get('win_rate', 0):.2%}")
                print(f"   💰 总收益: {execution_result.get('total_return', 0):.2%}")
                test_results["tianquan_integration_real"] = True
            else:
                print(f"   ❌ 天权星战法执行失败: {result.get('error')}")
        else:
            print(f"   ❌ 天权星服务未集成")
            
    except Exception as e:
        print(f"   ❌ 天权星集成测试失败: {e}")
    
    # 第四步：测试数据收集真实性
    print("\n4️⃣ 测试数据收集真实性")
    try:
        from roles.yaoguang_star.services.data_management_service import data_management_service
        
        # 测试真实数据收集
        result = await data_management_service.get_available_stocks()
        
        if result.get("success"):
            total_stocks = result.get("total_stocks", 0)
            print(f"   ✅ 数据收集服务真实可用")
            print(f"   📊 可用股票数: {total_stocks}")
            
            if total_stocks > 1000:  # 真实数据库应该有大量股票
                test_results["data_collection_real"] = True
                print(f"   ✅ 数据收集真实性验证通过")
            else:
                print(f"   ⚠️ 数据收集真实性存疑: 股票数量较少")
        else:
            print(f"   ❌ 数据收集服务失败: {result.get('error')}")
            
    except Exception as e:
        print(f"   ❌ 数据收集测试失败: {e}")
    
    # 第五步：测试学习优化真实性
    print("\n5️⃣ 测试学习优化真实性")
    try:
        from roles.yaoguang_star.services.learning_optimization_service import learning_optimization_service
        
        # 测试真实学习优化
        result = await learning_optimization_service.optimize_stock_learning(
            stock_code="000001.XSHE",
            learning_context={
                "test_purpose": "真实性验证",
                "optimization_type": "strategy_improvement"
            }
        )
        
        if result.get("success"):
            learning_result = result.get("learning_result", {})
            print(f"   ✅ 学习优化服务真实可用")
            print(f"   📊 学习记录ID: {learning_result.get('record_id')}")
            print(f"   📈 性能影响: {learning_result.get('performance_impact', 0):.3f}")
            print(f"   🎯 置信度: {learning_result.get('confidence', 0):.3f}")
            test_results["learning_optimization_real"] = True
        else:
            print(f"   ❌ 学习优化失败: {result.get('error')}")
            
    except Exception as e:
        print(f"   ❌ 学习优化测试失败: {e}")
    
    # 第六步：测试RD-Agent真实集成
    print("\n6️⃣ 测试RD-Agent真实集成")
    try:
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        # 测试真实的Alpha158因子获取
        factors = await rd_agent_integration_service.get_alpha158_factors()
        
        if factors and len(factors) > 0:
            print(f"   ✅ RD-Agent集成服务真实可用")
            print(f"   📊 Alpha158因子数: {len(factors)}")
            
            # 显示几个因子示例
            for i, factor in enumerate(factors[:3]):
                print(f"   📈 因子{i+1}: {factor.get('name')} - {factor.get('description')}")
            
            test_results["rd_agent_integration_real"] = True
        else:
            print(f"   ❌ RD-Agent因子获取失败")
            
    except Exception as e:
        print(f"   ❌ RD-Agent集成测试失败: {e}")
    
    # 第七步：测试四星辩论真实性
    print("\n7️⃣ 测试四星辩论真实性")
    try:
        if unified_yaoguang_system.automation_engine.get("four_stars_debate"):
            debate_service = unified_yaoguang_system.automation_engine["four_stars_debate"]
            
            # 测试真实辩论功能
            debate_topic = {
                "topic_type": "test_debate",
                "subject": "瑶光星真实性验证辩论",
                "context": {"test_purpose": "验证辩论系统真实性"},
                "requester": "瑶光星真实性测试"
            }
            
            result = await debate_service.start_debate_session(debate_topic)
            
            if result.get("success"):
                print(f"   ✅ 四星辩论系统真实可用")
                print(f"   📊 辩论会话ID: {result.get('session_id')}")
                test_results["four_stars_debate_real"] = True
            else:
                print(f"   ❌ 四星辩论启动失败: {result.get('error')}")
        else:
            print(f"   ❌ 四星辩论服务未集成")
            
    except Exception as e:
        print(f"   ❌ 四星辩论测试失败: {e}")
    
    # 第八步：测试API功能真实性
    print("\n8️⃣ 测试API功能真实性")
    try:
        base_url = "http://127.0.0.1:8003"
        
        # 测试系统初始化API
        response = requests.post(f"{base_url}/api/yaoguang/system/initialize", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                system_data = data.get("data", {})
                print(f"   ✅ API系统初始化成功")
                print(f"   📊 系统版本: {system_data.get('version')}")
                print(f"   🏥 健康状态: {system_data.get('health_status', {})}")
                
                # 测试学习会话启动
                learning_config = {
                    "stocks_per_session": 2,
                    "data_years": 1,
                    "strategy_testing_enabled": True
                }
                
                response = requests.post(
                    f"{base_url}/api/yaoguang/learning/start",
                    json=learning_config,
                    timeout=30
                )
                
                if response.status_code == 200:
                    learning_data = response.json()
                    if learning_data.get("success"):
                        session_id = learning_data.get("data", {}).get("session_id")
                        print(f"   ✅ 学习会话启动成功: {session_id}")
                        test_results["api_functionality_real"] = True
                    else:
                        print(f"   ⚠️ 学习会话启动失败")
                else:
                    print(f"   ❌ 学习API请求失败: {response.status_code}")
            else:
                print(f"   ⚠️ API系统初始化返回失败")
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API功能测试失败: {e}")
    
    # 生成测试报告
    print("\n" + "="*80)
    print("📊 瑶光星真实功能测试报告")
    print("="*80)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"总测试项: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    print()
    
    for test_name, result in test_results.items():
        status = "✅ 真实" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print("\n" + "="*80)
    
    if success_rate >= 75:
        print("🎉 瑶光星真实功能测试通过！")
        print("✅ 所有核心功能都是真实实现，不是模拟")
        print("🚀 瑶光星已成为完全真实的量化研究自动化系统")
    else:
        print("❌ 瑶光星真实功能测试失败")
        print("⚠️ 部分功能仍然是模拟或不可用")
    
    return test_results

if __name__ == "__main__":
    asyncio.run(test_real_yaoguang_functionality())
