#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整修复验证测试
验证所有修复是否成功，包括天玑星自动化系统
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_all_automation_systems():
    """测试所有自动化系统"""
    try:
        logger.info("🤖 测试所有自动化系统...")
        
        success_count = 0
        total_count = 4
        
        # 测试天枢星自动化系统
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            status = tianshu_automation_system.get_automation_status()
            logger.info(f"✅ 天枢星自动化系统: {status['system_name']} v{status['version']}")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天枢星自动化系统失败: {e}")
        
        # 测试天玑星自动化系统
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            status = tianji_automation_system.get_automation_status()
            logger.info(f"✅ 天玑星自动化系统: {status['system_name']} v{status['version']}")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天玑星自动化系统失败: {e}")
        
        # 测试天璇星自动化系统
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            status = tianxuan_automation_system.get_automation_status()
            logger.info(f"✅ 天璇星自动化系统: {status['system_name']} v{status['version']}")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 天璇星自动化系统失败: {e}")
        
        # 测试玉衡星自动化系统
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            status = yuheng_automation_system.get_automation_status()
            logger.info(f"✅ 玉衡星自动化系统: {status['system_name']} v{status['version']}")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 玉衡星自动化系统失败: {e}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"📊 自动化系统成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"❌ 自动化系统测试失败: {e}")
        return False

async def test_yaoguang_unified_system():
    """测试瑶光星统一系统"""
    try:
        logger.info("🌟 测试瑶光星统一系统...")
        
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        # 测试系统初始化
        init_result = await unified_yaoguang_system.initialize_system()
        if not init_result.get("success"):
            logger.error(f"❌ 瑶光星统一系统初始化失败: {init_result.get('error')}")
            return False
        
        logger.info("✅ 瑶光星统一系统初始化成功")
        
        # 测试学习状态获取
        try:
            status = await unified_yaoguang_system.get_learning_status()
            logger.info(f"✅ 学习状态获取成功: {status.get('session_active', False)}")
        except Exception as e:
            logger.error(f"❌ 学习状态获取失败: {e}")
            return False
        
        # 测试系统状态获取
        try:
            system_status = await unified_yaoguang_system.get_system_status()
            logger.info(f"✅ 系统状态获取成功")
        except Exception as e:
            logger.error(f"❌ 系统状态获取失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 瑶光星统一系统测试失败: {e}")
        return False

async def test_rd_agent_integration():
    """测试RD-Agent集成"""
    try:
        logger.info("🔬 测试RD-Agent集成...")
        
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        # 测试服务状态
        status = rd_agent_integration_service.get_service_status()
        
        if status.get("use_professional") and status.get("local_rd_agent_available"):
            logger.info("✅ RD-Agent集成正常")
            logger.info(f"  本地化RD-Agent: {status.get('local_rd_agent_available')}")
            logger.info(f"  专业模式: {status.get('use_professional')}")
            logger.info(f"  分配因子数: {status.get('allocated_factors', 0)}")
            return True
        else:
            logger.error("❌ RD-Agent集成异常")
            return False
            
    except Exception as e:
        logger.error(f"❌ RD-Agent集成测试失败: {e}")
        return False

async def test_core_systems():
    """测试核心系统"""
    try:
        logger.info("🏗️ 测试核心系统...")
        
        success_count = 0
        total_count = 4
        
        # 测试传奇记忆系统
        try:
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            core_path = os.path.join(current_dir, "core")
            
            if core_path not in sys.path:
                sys.path.insert(0, core_path)
            
            from domain.memory.legendary.interface import legendary_memory_interface
            logger.info("✅ 传奇记忆系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 传奇记忆系统失败: {e}")
        
        # 测试绩效监控系统
        try:
            from performance.star_performance_monitor import star_performance_monitor
            logger.info("✅ 绩效监控系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 绩效监控系统失败: {e}")
        
        # 测试层级权限系统
        try:
            from enhanced_seven_stars_hierarchy import enhanced_seven_stars_hierarchy
            logger.info("✅ 层级权限系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 层级权限系统失败: {e}")
        
        # 测试因子分配系统
        try:
            from factor_allocation import factor_allocation_system
            logger.info("✅ 因子分配系统导入成功")
            success_count += 1
        except Exception as e:
            logger.error(f"❌ 因子分配系统失败: {e}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"📊 核心系统成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        return success_count >= 3  # 至少3个系统正常
        
    except Exception as e:
        logger.error(f"❌ 核心系统测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始最终完整修复验证测试")
    logger.info("=" * 80)
    
    test_results = {}
    
    # 测试1：核心系统
    test_results["core_systems"] = await test_core_systems()
    
    # 测试2：所有自动化系统
    test_results["automation_systems"] = await test_all_automation_systems()
    
    # 测试3：RD-Agent集成
    test_results["rd_agent_integration"] = await test_rd_agent_integration()
    
    # 测试4：瑶光星统一系统
    test_results["yaoguang_unified_system"] = await test_yaoguang_unified_system()
    
    # 汇总结果
    logger.info("=" * 80)
    logger.info("📊 最终完整修复验证结果:")
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"📈 最终修复成功率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        logger.info("🎉 所有修复完成，系统完全正常！")
        logger.info("💡 现在可以运行完整的学习模式")
    elif success_rate >= 75:
        logger.info("✅ 主要修复完成，系统基本正常！")
        logger.info("💡 可以进行基本的学习模式测试")
    else:
        logger.info("⚠️ 修复不完整，需要进一步处理")
    
    return success_rate

if __name__ == "__main__":
    asyncio.run(main())
