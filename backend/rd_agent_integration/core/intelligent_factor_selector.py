#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能因子选择器
根据市场环境、数据质量、计算资源等条件动态选择最优因子组合
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime
from .complete_alpha158_factors import CompleteAlpha158Factors

logger = logging.getLogger(__name__)

class IntelligentFactorSelector:
    """智能因子选择器"""
    
    def __init__(self):
        self.alpha158 = CompleteAlpha158Factors()
        self.factor_performance_cache = {}
        self.market_regime_cache = {}
        
        # 因子选择策略
        self.selection_strategies = {
            "conservative": self._conservative_selection,
            "aggressive": self._aggressive_selection,
            "balanced": self._balanced_selection,
            "momentum": self._momentum_focused_selection,
            "mean_reversion": self._mean_reversion_selection,
            "volatility": self._volatility_focused_selection,
            "volume": self._volume_focused_selection,
            "adaptive": self._adaptive_selection
        }
        
        logger.info("  智能因子选择器初始化完成")
    
    def select_optimal_factors(self, 
                             market_data: pd.DataFrame,
                             strategy: str = "adaptive",
                             max_factors: int = 50,
                             complexity_limit: int = 5,
                             performance_threshold: float = 0.02) -> List[str]:
        """选择最优因子组合"""
        
        logger.info(f"🔍 开始智能因子选择: 策略={strategy}, 最大因子数={max_factors}")
        
        # 分析市场环境
        market_regime = self._analyze_market_regime(market_data)
        
        # 评估数据质量
        data_quality = self._assess_data_quality(market_data)
        
        # 根据策略选择因子
        if strategy in self.selection_strategies:
            selected_factors = self.selection_strategies[strategy](
                market_data, market_regime, data_quality, max_factors, complexity_limit
            )
        else:
            logger.warning(f"未知策略 {strategy}，使用平衡策略")
            selected_factors = self._balanced_selection(
                market_data, market_regime, data_quality, max_factors, complexity_limit
            )
        
        # 过滤低性能因子
        filtered_factors = self._filter_by_performance(
            selected_factors, market_data, performance_threshold
        )
        
        logger.info(f"  因子选择完成: {len(filtered_factors)}个因子")
        return filtered_factors
    
    def _analyze_market_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析市场环境"""
        
        returns = data['close'].pct_change().dropna()
        
        # 计算市场特征
        volatility = returns.std() * np.sqrt(252)  # 年化波动率
        trend_strength = abs(returns.mean()) * 252  # 年化趋势强度
        volume_trend = data['volume'].pct_change().mean()
        
        # 判断市场状态
        if volatility > 0.3:
            volatility_regime = "high"
        elif volatility < 0.15:
            volatility_regime = "low"
        else:
            volatility_regime = "medium"
        
        if trend_strength > 0.1:
            trend_regime = "trending"
        else:
            trend_regime = "sideways"
        
        if volume_trend > 0.02:
            volume_regime = "expanding"
        elif volume_trend < -0.02:
            volume_regime = "contracting"
        else:
            volume_regime = "stable"
        
        regime = {
            "volatility": volatility_regime,
            "trend": trend_regime,
            "volume": volume_regime,
            "volatility_value": volatility,
            "trend_strength": trend_strength,
            "volume_trend": volume_trend
        }
        
        logger.info(f" 市场环境分析: {regime}")
        return regime
    
    def _assess_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """评估数据质量"""
        
        quality = {
            "length": len(data),
            "missing_rate": data.isnull().sum().sum() / (len(data) * len(data.columns)),
            "price_consistency": self._check_price_consistency(data),
            "volume_consistency": self._check_volume_consistency(data),
            "overall_score": 0.0
        }
        
        # 计算总体质量分数
        length_score = min(1.0, len(data) / 252)  # 一年数据为满分
        missing_score = 1.0 - quality["missing_rate"]
        consistency_score = (quality["price_consistency"] + quality["volume_consistency"]) / 2
        
        quality["overall_score"] = (length_score + missing_score + consistency_score) / 3
        
        logger.info(f"📈 数据质量评估: 总分={quality['overall_score']:.3f}")
        return quality
    
    def _check_price_consistency(self, data: pd.DataFrame) -> float:
        """检查价格数据一致性"""
        
        # 检查OHLC逻辑
        valid_ohlc = (
            (data['high'] >= data['low']) &
            (data['high'] >= data['open']) &
            (data['high'] >= data['close']) &
            (data['low'] <= data['open']) &
            (data['low'] <= data['close'])
        ).mean()
        
        return valid_ohlc
    
    def _check_volume_consistency(self, data: pd.DataFrame) -> float:
        """检查成交量数据一致性"""
        
        # 检查成交量为正数
        positive_volume = (data['volume'] > 0).mean()
        
        # 检查成交量异常值
        volume_zscore = np.abs((data['volume'] - data['volume'].mean()) / data['volume'].std())
        normal_volume = (volume_zscore < 3).mean()
        
        return (positive_volume + normal_volume) / 2
    
    def _conservative_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """保守选择策略：优先选择简单、稳定的因子"""
        
        # 基础价格因子
        factors = ["OPEN", "HIGH", "LOW", "CLOSE", "VWAP"]
        
        # 简单移动平均
        factors.extend(["SMA_5", "SMA_10", "SMA_20", "SMA_60"])
        
        # 基础动量因子
        factors.extend(["ROC_5", "ROC_10", "ROC_20"])
        
        # 基础波动率因子
        factors.extend(["STD_5", "STD_10", "STD_20"])
        
        # 基础成交量因子
        factors.extend(["VOL_MA_5", "VOL_MA_10", "VOL_RATIO_10"])
        
        # 基础技术指标
        factors.extend(["RSI_14", "MACD"])
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _aggressive_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """激进选择策略：包含复杂的高级因子"""
        
        factors = []
        
        # 包含所有类别的因子
        for category in ["price", "trend", "momentum", "volatility", "volume", "technical", "composite"]:
            category_factors = self.alpha158.get_factors_by_category(category)
            factors.extend(category_factors)
        
        # 优先选择高复杂度因子
        factors = sorted(factors, key=lambda f: self.alpha158.factor_complexity.get(f, 1), reverse=True)
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _balanced_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """平衡选择策略：各类因子均衡分配"""
        
        factors = []
        
        # 每个类别选择一定数量的因子
        category_allocation = {
            "price": 5,
            "trend": 8,
            "momentum": 10,
            "volatility": 8,
            "volume": 6,
            "technical": 8,
            "pattern": 5,
            "statistical": 3,
            "correlation": 2,
            "composite": 3
        }
        
        for category, count in category_allocation.items():
            category_factors = self.alpha158.get_factors_by_category(category)
            # 按复杂度排序，选择中等复杂度的因子
            category_factors = sorted(category_factors, 
                                    key=lambda f: abs(self.alpha158.factor_complexity.get(f, 3) - 3))
            factors.extend(category_factors[:count])
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _momentum_focused_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """动量策略因子选择"""
        
        factors = []
        
        # 动量因子为主
        momentum_factors = self.alpha158.get_factors_by_category("momentum")
        factors.extend(momentum_factors)
        
        # 趋势因子
        trend_factors = self.alpha158.get_factors_by_category("trend")
        factors.extend(trend_factors[:10])
        
        # 技术指标
        tech_factors = ["RSI_6", "RSI_12", "RSI_24", "MACD", "MACD_SIGNAL"]
        factors.extend(tech_factors)
        
        # 价格突破因子
        pattern_factors = ["HIGH_BREAK_5", "HIGH_BREAK_10", "HIGH_BREAK_20"]
        factors.extend(pattern_factors)
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _mean_reversion_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """均值回归策略因子选择"""
        
        factors = []
        
        # 价格位置因子
        factors.extend(["PRICE_POSITION_5", "PRICE_POSITION_10", "PRICE_POSITION_20"])
        
        # 布林带因子
        factors.extend(["BOLL_UPPER", "BOLL_LOWER"])
        
        # RSI超买超卖
        factors.extend(["RSI_6", "RSI_14"])
        
        # 威廉指标
        factors.extend(["WR_14", "WR_28"])
        
        # 统计因子
        stat_factors = self.alpha158.get_factors_by_category("statistical")
        factors.extend(stat_factors[:8])
        
        # 波动率因子
        vol_factors = self.alpha158.get_factors_by_category("volatility")
        factors.extend(vol_factors[:10])
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _volatility_focused_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """波动率策略因子选择"""
        
        factors = []
        
        # 波动率因子为主
        vol_factors = self.alpha158.get_factors_by_category("volatility")
        factors.extend(vol_factors)
        
        # ATR指标
        factors.extend(["ATR_5", "ATR_10", "ATR_20"])
        
        # 价格形态因子
        pattern_factors = self.alpha158.get_factors_by_category("pattern")
        factors.extend(pattern_factors[:8])
        
        # 统计因子
        stat_factors = ["SKEW_10", "SKEW_20", "KURT_10", "KURT_20"]
        factors.extend(stat_factors)
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _volume_focused_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """成交量策略因子选择"""
        
        factors = []
        
        # 成交量因子为主
        vol_factors = self.alpha158.get_factors_by_category("volume")
        factors.extend(vol_factors)
        
        # 价量相关性因子
        corr_factors = [f for f in self.alpha158.get_factors_by_category("correlation") 
                       if "VOL" in f]
        factors.extend(corr_factors)
        
        # 成交金额
        factors.append("TURNOVER")
        
        # 价量综合指标
        factors.append("PRICE_VOLUME_COMPOSITE")
        
        return self._filter_by_complexity(factors, complexity_limit)[:max_factors]
    
    def _adaptive_selection(self, data, regime, quality, max_factors, complexity_limit) -> List[str]:
        """自适应选择策略：根据市场环境动态调整"""
        
        # 根据市场环境选择策略
        if regime["trend"] == "trending":
            if regime["volatility"] == "high":
                return self._momentum_focused_selection(data, regime, quality, max_factors, complexity_limit)
            else:
                return self._balanced_selection(data, regime, quality, max_factors, complexity_limit)
        else:  # sideways market
            if regime["volatility"] == "high":
                return self._volatility_focused_selection(data, regime, quality, max_factors, complexity_limit)
            else:
                return self._mean_reversion_selection(data, regime, quality, max_factors, complexity_limit)
    
    def _filter_by_complexity(self, factors: List[str], max_complexity: int) -> List[str]:
        """按复杂度过滤因子"""
        return [f for f in factors if self.alpha158.factor_complexity.get(f, 1) <= max_complexity]
    
    def _filter_by_performance(self, factors: List[str], data: pd.DataFrame, threshold: float) -> List[str]:
        """按性能过滤因子（简化实现）"""
        
        # 这里可以实现更复杂的性能评估
        # 目前简单返回所有因子
        return factors
    
    def get_factor_recommendation(self, 
                                market_condition: str,
                                data_length: int,
                                computational_budget: str) -> Dict[str, Any]:
        """获取因子推荐"""
        
        recommendations = {
            "bull_market": {
                "strategy": "momentum",
                "max_factors": 40,
                "complexity_limit": 4,
                "description": "牛市推荐动量策略因子"
            },
            "bear_market": {
                "strategy": "mean_reversion", 
                "max_factors": 35,
                "complexity_limit": 4,
                "description": "熊市推荐均值回归因子"
            },
            "sideways_market": {
                "strategy": "volatility",
                "max_factors": 30,
                "complexity_limit": 3,
                "description": "震荡市推荐波动率因子"
            },
            "unknown": {
                "strategy": "adaptive",
                "max_factors": 50,
                "complexity_limit": 5,
                "description": "未知市场环境，使用自适应策略"
            }
        }
        
        # 根据计算预算调整
        budget_adjustments = {
            "low": {"max_factors": 20, "complexity_limit": 2},
            "medium": {"max_factors": 35, "complexity_limit": 3},
            "high": {"max_factors": 50, "complexity_limit": 5}
        }
        
        recommendation = recommendations.get(market_condition, recommendations["unknown"])
        
        if computational_budget in budget_adjustments:
            recommendation.update(budget_adjustments[computational_budget])
        
        return recommendation

# 全局实例
intelligent_factor_selector = IntelligentFactorSelector()
