#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星数据持久化系统
负责自动化状态、任务历史、统计数据的持久化存储
"""

import sqlite3
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncio
import threading

logger = logging.getLogger(__name__)

class YaoguangDataPersistence:
    """瑶光星数据持久化管理器"""
    
    def __init__(self):
        self.db_path = Path('data/yaoguang_star/yaoguang_persistence.db')
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._lock = threading.Lock()
        self._init_database()
        
        logger.info(f"瑶光星数据持久化系统初始化: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 自动化状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS automation_status (
                    id INTEGER PRIMARY KEY,
                    automation_id TEXT UNIQUE,
                    is_running BOOLEAN,
                    total_cycles INTEGER DEFAULT 0,
                    successful_cycles INTEGER DEFAULT 0,
                    failed_cycles INTEGER DEFAULT 0,
                    last_cycle_time TEXT,
                    last_success_time TEXT,
                    automation_start_time TEXT,
                    created_time TEXT,
                    updated_time TEXT
                )
            ''')
            
            # 任务历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE,
                    task_type TEXT,
                    status TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    duration_seconds REAL,
                    task_data TEXT,
                    error_message TEXT,
                    created_time TEXT
                )
            ''')
            
            # 统计数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stat_type TEXT,
                    stat_key TEXT,
                    stat_value INTEGER,
                    date TEXT,
                    created_time TEXT,
                    UNIQUE(stat_type, stat_key, date)
                )
            ''')
            
            # 因子研究记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS factor_research_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE,
                    stock_codes TEXT,
                    factor_count INTEGER,
                    total_factors INTEGER,
                    research_data TEXT,
                    created_time TEXT
                )
            ''')
            
            # 学习记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE,
                    learning_type TEXT,
                    completed_tasks INTEGER,
                    total_tasks INTEGER,
                    key_learnings TEXT,
                    learning_report TEXT,
                    created_time TEXT
                )
            ''')
            
            # 性能指标表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT,
                    metric_value REAL,
                    metric_unit TEXT,
                    context TEXT,
                    recorded_time TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("数据库表初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_automation_status(self, automation_id: str, status_data: Dict[str, Any]) -> bool:
        """保存自动化状态"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 插入或更新自动化状态
                cursor.execute('''
                    INSERT OR REPLACE INTO automation_status 
                    (automation_id, is_running, total_cycles, successful_cycles, failed_cycles,
                     last_cycle_time, last_success_time, automation_start_time, updated_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    automation_id,
                    status_data.get('is_running', False),
                    status_data.get('total_cycles', 0),
                    status_data.get('successful_cycles', 0),
                    status_data.get('failed_cycles', 0),
                    status_data.get('last_cycle_time'),
                    status_data.get('last_success_time'),
                    status_data.get('automation_start_time'),
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                conn.close()
                
                return True
                
        except Exception as e:
            logger.error(f"保存自动化状态失败: {e}")
            return False
    
    def load_automation_status(self, automation_id: str) -> Optional[Dict[str, Any]]:
        """加载自动化状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT is_running, total_cycles, successful_cycles, failed_cycles,
                       last_cycle_time, last_success_time, automation_start_time
                FROM automation_status 
                WHERE automation_id = ?
            ''', (automation_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'is_running': bool(result[0]),
                    'total_cycles': result[1],
                    'successful_cycles': result[2],
                    'failed_cycles': result[3],
                    'last_cycle_time': result[4],
                    'last_success_time': result[5],
                    'automation_start_time': result[6]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"加载自动化状态失败: {e}")
            return None
    
    def save_task_history(self, task_id: str, task_type: str, status: str, 
                         task_data: Dict[str, Any], start_time: str = None, 
                         end_time: str = None, error_message: str = None) -> bool:
        """保存任务历史"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # 计算执行时长
                duration_seconds = None
                if start_time and end_time:
                    try:
                        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                        duration_seconds = (end_dt - start_dt).total_seconds()
                    except:
                        pass
                
                cursor.execute('''
                    INSERT OR REPLACE INTO task_history 
                    (task_id, task_type, status, start_time, end_time, duration_seconds,
                     task_data, error_message, created_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task_id,
                    task_type,
                    status,
                    start_time,
                    end_time,
                    duration_seconds,
                    json.dumps(task_data, ensure_ascii=False),
                    error_message,
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                conn.close()
                
                return True
                
        except Exception as e:
            logger.error(f"保存任务历史失败: {e}")
            return False
    
    def get_task_history(self, task_type: str = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取任务历史"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if task_type:
                cursor.execute('''
                    SELECT task_id, task_type, status, start_time, end_time, duration_seconds,
                           task_data, error_message, created_time
                    FROM task_history 
                    WHERE task_type = ?
                    ORDER BY created_time DESC
                    LIMIT ? OFFSET ?
                ''', (task_type, limit, offset))
            else:
                cursor.execute('''
                    SELECT task_id, task_type, status, start_time, end_time, duration_seconds,
                           task_data, error_message, created_time
                    FROM task_history 
                    ORDER BY created_time DESC
                    LIMIT ? OFFSET ?
                ''', (limit, offset))
            
            results = cursor.fetchall()
            conn.close()
            
            tasks = []
            for row in results:
                task_data = {}
                try:
                    task_data = json.loads(row[6]) if row[6] else {}
                except:
                    pass
                
                tasks.append({
                    'task_id': row[0],
                    'task_type': row[1],
                    'status': row[2],
                    'start_time': row[3],
                    'end_time': row[4],
                    'duration_seconds': row[5],
                    'task_data': task_data,
                    'error_message': row[7],
                    'created_time': row[8]
                })
            
            return tasks
            
        except Exception as e:
            logger.error(f"获取任务历史失败: {e}")
            return []
    
    def save_statistics(self, stat_type: str, stat_key: str, stat_value: int, date: str = None) -> bool:
        """保存统计数据"""
        try:
            if not date:
                date = datetime.now().strftime('%Y-%m-%d')
            
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO statistics 
                    (stat_type, stat_key, stat_value, date, created_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    stat_type,
                    stat_key,
                    stat_value,
                    date,
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                conn.close()
                
                return True
                
        except Exception as e:
            logger.error(f"保存统计数据失败: {e}")
            return False
    
    def get_statistics(self, stat_type: str = None) -> Dict[str, Any]:
        """获取统计数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if stat_type:
                cursor.execute('''
                    SELECT stat_key, SUM(stat_value) as total_value
                    FROM statistics 
                    WHERE stat_type = ?
                    GROUP BY stat_key
                ''', (stat_type,))
            else:
                cursor.execute('''
                    SELECT stat_type, stat_key, SUM(stat_value) as total_value
                    FROM statistics 
                    GROUP BY stat_type, stat_key
                ''')
            
            results = cursor.fetchall()
            conn.close()
            
            if stat_type:
                return {row[0]: row[1] for row in results}
            else:
                stats = {}
                for row in results:
                    if row[0] not in stats:
                        stats[row[0]] = {}
                    stats[row[0]][row[1]] = row[2]
                return stats
                
        except Exception as e:
            logger.error(f"获取统计数据失败: {e}")
            return {}
    
    def save_factor_research_record(self, task_id: str, stock_codes: List[str], 
                                  factor_count: int, total_factors: int, 
                                  research_data: Dict[str, Any]) -> bool:
        """保存因子研究记录"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO factor_research_records 
                    (task_id, stock_codes, factor_count, total_factors, research_data, created_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    task_id,
                    json.dumps(stock_codes),
                    factor_count,
                    total_factors,
                    json.dumps(research_data, ensure_ascii=False),
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                conn.close()
                
                return True
                
        except Exception as e:
            logger.error(f"保存因子研究记录失败: {e}")
            return False
    
    def save_learning_record(self, task_id: str, learning_type: str, 
                           completed_tasks: int, total_tasks: int,
                           key_learnings: List[str], learning_report: Dict[str, Any]) -> bool:
        """保存学习记录"""
        try:
            with self._lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO learning_records 
                    (task_id, learning_type, completed_tasks, total_tasks, 
                     key_learnings, learning_report, created_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task_id,
                    learning_type,
                    completed_tasks,
                    total_tasks,
                    json.dumps(key_learnings, ensure_ascii=False),
                    json.dumps(learning_report, ensure_ascii=False),
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                conn.close()
                
                return True
                
        except Exception as e:
            logger.error(f"保存学习记录失败: {e}")
            return False
    
    def get_total_statistics(self) -> Dict[str, Any]:
        """获取总体统计"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取各类任务总数
            cursor.execute('''
                SELECT task_type, COUNT(*) as count
                FROM task_history 
                WHERE status = 'completed'
                GROUP BY task_type
            ''')
            task_counts = dict(cursor.fetchall())
            
            # 获取因子总数
            cursor.execute('''
                SELECT SUM(total_factors) as total_factors
                FROM factor_research_records
            ''')
            total_factors_result = cursor.fetchone()
            total_factors = total_factors_result[0] if total_factors_result[0] else 0
            
            # 获取今日统计
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute('''
                SELECT task_type, COUNT(*) as count
                FROM task_history 
                WHERE status = 'completed' AND DATE(created_time) = ?
                GROUP BY task_type
            ''', (today,))
            today_counts = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                'total_statistics': {
                    'learning_total': task_counts.get('learning_summary', 0),
                    'factor_research_total': task_counts.get('factor_research', 0),
                    'research_total': task_counts.get('data_quality', 0) + task_counts.get('model_training', 0),
                    'practice_total': task_counts.get('practice', 0),
                    'factor_count_total': total_factors,
                    'data_quality_total': task_counts.get('data_quality', 0)
                },
                'daily_statistics': {
                    'learning_today': today_counts.get('learning_summary', 0),
                    'factor_research_today': today_counts.get('factor_research', 0),
                    'research_today': today_counts.get('data_quality', 0) + today_counts.get('model_training', 0),
                    'practice_today': today_counts.get('practice', 0),
                    'data_quality_today': today_counts.get('data_quality', 0)
                }
            }
            
        except Exception as e:
            logger.error(f"获取总体统计失败: {e}")
            return {
                'total_statistics': {},
                'daily_statistics': {}
            }

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 检查数据库连接
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()

            database_connected = True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            database_connected = False

        # 获取统计信息
        total_stats = self.get_total_statistics()

        return {
            "database_connected": database_connected,
            "database_path": str(self.db_path),
            "database_exists": self.db_path.exists(),
            "total_tasks": sum(total_stats.get("total_statistics", {}).values()),
            "today_tasks": sum(total_stats.get("daily_statistics", {}).values()),
            "system_healthy": database_connected and self.db_path.exists()
        }

# 全局数据持久化实例
yaoguang_persistence = YaoguangDataPersistence()
