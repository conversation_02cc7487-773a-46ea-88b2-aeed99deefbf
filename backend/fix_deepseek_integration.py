#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek集成修复脚本
为所有角色创建专用DeepSeek配置并集成到自动化系统
"""

import asyncio
import os
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DeepSeekIntegrationFixer:
    """DeepSeek集成修复器"""
    
    def __init__(self):
        self.roles_config = {
            "tianshu_star": {
                "name": "天枢星",
                "role": "信息收集专家",
                "temperature": 0.3,
                "description": "专业的市场信息收集和新闻分析专家，擅长数据挖掘和情感分析"
            },
            "tianji_star": {
                "name": "天玑星",
                "role": "风险评估专家",
                "temperature": 0.2,
                "description": "专业的风险管理和评估专家，精通风险建模和控制策略"
            },
            "tianxuan_star": {
                "name": "天璇星",
                "role": "技术分析专家",
                "temperature": 0.4,
                "description": "专业的技术分析师和模式识别专家，精通图表分析和信号识别"
            },
            "yuheng_star": {
                "name": "玉衡星",
                "role": "交易执行专家",
                "temperature": 0.2,
                "description": "精准的交易执行和风险控制专家，专注于最优执行策略"
            },
            "kaiyang_star": {
                "name": "开阳星",
                "role": "股票检测专家",
                "temperature": 0.5,
                "description": "专业的股票筛选和评估专家，擅长发现投资机会"
            },
            "tianquan_star": {
                "name": "天权星",
                "role": "战略决策专家",
                "temperature": 0.6,
                "description": "高级战略规划和决策专家，统筹全局战略制定"
            },
            "yaoguang_star": {
                "name": "瑶光星",
                "role": "学习研究专家",
                "temperature": 0.7,
                "description": "智能学习和研究自动化专家，持续优化系统能力"
            }
        }
    
    async def fix_all_roles(self):
        """修复所有角色的DeepSeek集成"""
        print("🔧 开始修复所有角色的DeepSeek集成")
        print("=" * 60)
        
        for role_name, config in self.roles_config.items():
            await self.fix_role_deepseek(role_name, config)
        
        print("\n✅ 所有角色DeepSeek集成修复完成")
    
    async def fix_role_deepseek(self, role_name: str, config: dict):
        """修复单个角色的DeepSeek集成"""
        print(f"\n🤖 修复角色: {role_name} ({config['name']})")
        print("-" * 40)
        
        # 1. 创建DeepSeek配置文件
        await self.create_deepseek_config(role_name, config)
        
        # 2. 集成到自动化系统
        await self.integrate_to_automation(role_name, config)
        
        print(f"✅ {role_name} DeepSeek集成完成")
    
    async def create_deepseek_config(self, role_name: str, config: dict):
        """创建DeepSeek配置文件"""
        config_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{config["name"]}DeepSeek AI配置
{config["description"]}的AI角色定义和智能分析系统
"""

from typing import Dict, Any, List
import json

# DeepSeek API配置
DEEPSEEK_CONFIG = {{
    "api_key": "***********************************",
    "base_url": "https://api.deepseek.com",
    "model": "deepseek-chat",
    "max_tokens": 2000,
    "temperature": {config["temperature"]},
    "timeout": 30
}}

# {config["name"]}角色设定
{role_name.upper()}_ROLE_SETTING = """
你是{config["name"]}，七星量化交易系统中的{config["role"]}。

核心身份：
- {config["description"]}
- 拥有专业的分析能力和决策判断力
- 精通相关领域的理论知识和实践经验
- 具备深度的数据分析和模式识别能力
- 能够与其他星座协作，提供专业建议

专业能力：
- 深度分析和逻辑推理
- 数据驱动的决策制定
- 风险识别和机会发现
- 持续学习和自我优化

工作原则：
- 基于真实数据进行分析
- 提供客观专业的建议
- 注重风险控制和收益平衡
- 与团队协作达成最优结果

请始终以专业的{config["role"]}身份，为七星系统提供高质量的专业服务。
"""

# 专业提示词模板
PROFESSIONAL_PROMPTS = {{
    "analysis": f"作为{config['name']}，请从{config['role']}的角度分析以下内容：",
    "decision": f"作为{config['name']}，请基于专业判断提供决策建议：",
    "evaluation": f"作为{config['name']}，请评估以下情况并给出专业意见：",
    "collaboration": f"作为{config['name']}，请配合其他星座完成以下任务："
}}

def get_deepseek_config() -> Dict[str, Any]:
    """获取DeepSeek配置"""
    return DEEPSEEK_CONFIG.copy()

def get_role_setting() -> str:
    """获取{config["name"]}角色设定"""
    return {role_name.upper()}_ROLE_SETTING

def get_professional_prompt(prompt_type: str, content: str) -> str:
    """获取专业提示词"""
    template = PROFESSIONAL_PROMPTS.get(prompt_type, PROFESSIONAL_PROMPTS["analysis"])
    return f"{{template}}\\n\\n{{content}}"

def create_context_prompt(task_type: str, context_data: Dict[str, Any]) -> str:
    """创建上下文提示词"""
    role_context = f"{{get_role_setting()}}\\n\\n"
    
    if task_type == "market_analysis":
        return f"{{role_context}}请分析市场数据：{{json.dumps(context_data, ensure_ascii=False, indent=2)}}"
    elif task_type == "risk_assessment":
        return f"{{role_context}}请评估风险情况：{{json.dumps(context_data, ensure_ascii=False, indent=2)}}"
    elif task_type == "decision_making":
        return f"{{role_context}}请提供决策建议：{{json.dumps(context_data, ensure_ascii=False, indent=2)}}"
    else:
        return f"{{role_context}}请处理以下任务：{{json.dumps(context_data, ensure_ascii=False, indent=2)}}"
'''
        
        # 创建配置目录
        config_dir = f"backend/roles/{role_name}/config"
        os.makedirs(config_dir, exist_ok=True)
        
        # 写入配置文件
        config_path = f"{config_dir}/deepseek_config.py"
        with open(config_path, "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print(f"   ✅ 创建DeepSeek配置: {config_path}")
    
    async def integrate_to_automation(self, role_name: str, config: dict):
        """集成到自动化系统"""
        # 查找自动化系统文件
        automation_files = [
            f"backend/roles/{role_name}/services/{role_name}_automation_system.py",
            f"backend/roles/{role_name}/services/core_systems_integration.py",
            f"backend/roles/{role_name}/core/{role_name}_automation_system.py"
        ]
        
        integration_code = f'''
    async def _call_role_deepseek(self, prompt: str, context_type: str = "analysis", context_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用{config["name"]}专用DeepSeek分析"""
        try:
            from backend.roles.{role_name}.config.deepseek_config import (
                get_deepseek_config, get_role_setting, get_professional_prompt, create_context_prompt
            )
            from shared.infrastructure.deepseek_service import deepseek_service
            
            # 获取角色专用配置
            deepseek_config = get_deepseek_config()
            role_setting = get_role_setting()
            
            # 构建专业提示词
            if context_data:
                role_prompt = create_context_prompt(context_type, context_data)
            else:
                role_prompt = get_professional_prompt(context_type, prompt)
            
            # 调用DeepSeek服务
            response = await deepseek_service.analyze_with_context(
                role_prompt,
                context_type=context_type,
                max_tokens=deepseek_config.get("max_tokens", 2000),
                temperature=deepseek_config.get("temperature", 0.5)
            )
            
            if response.get("success"):
                logger.info(f"✅ {config['name']}DeepSeek分析完成")
                return {{
                    "success": True,
                    "analysis": response.get("analysis", ""),
                    "role": "{config['name']}",
                    "context_type": context_type,
                    "timestamp": datetime.now().isoformat()
                }}
            else:
                logger.warning(f"⚠️ {config['name']}DeepSeek分析失败: {{response.get('error')}}")
                return {{
                    "success": False,
                    "error": response.get("error", "DeepSeek分析失败"),
                    "role": "{config['name']}"
                }}
            
        except Exception as e:
            logger.error(f"❌ 调用{config['name']}DeepSeek异常: {{e}}")
            return {{
                "success": False,
                "error": str(e),
                "role": "{config['name']}"
            }}
    
    async def _enhance_with_deepseek_analysis(self, data: Dict[str, Any], analysis_type: str = "general") -> Dict[str, Any]:
        """使用DeepSeek增强分析结果"""
        try:
            # 调用角色专用DeepSeek
            deepseek_result = await self._call_role_deepseek(
                prompt=f"请分析以下数据并提供专业见解",
                context_type=analysis_type,
                context_data=data
            )
            
            if deepseek_result.get("success"):
                # 将DeepSeek分析结果融入原数据
                data["deepseek_analysis"] = deepseek_result.get("analysis", "")
                data["ai_enhanced"] = True
                data["analysis_role"] = "{config['name']}"
                data["analysis_timestamp"] = deepseek_result.get("timestamp")
                
                logger.info(f"✅ {config['name']}数据已通过DeepSeek增强")
            else:
                data["deepseek_analysis"] = "AI分析暂时不可用"
                data["ai_enhanced"] = False
                logger.warning(f"⚠️ {config['name']}DeepSeek增强失败")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ DeepSeek增强分析异常: {{e}}")
            data["deepseek_analysis"] = f"分析异常: {{str(e)}}"
            data["ai_enhanced"] = False
            return data
'''
        
        # 尝试集成到找到的文件
        integrated = False
        for file_path in automation_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否已经集成
                    if '_call_role_deepseek' not in content:
                        # 在类的最后添加方法
                        if 'class ' in content:
                            # 找到类的结束位置
                            lines = content.split('\n')
                            insert_index = -1
                            
                            # 找到最后一个方法的结束位置
                            for i in range(len(lines) - 1, -1, -1):
                                line = lines[i].strip()
                                if line and not line.startswith('#') and not line.startswith('"""'):
                                    # 在这里插入新方法
                                    lines.insert(i + 1, integration_code)
                                    insert_index = i + 1
                                    break
                            
                            if insert_index > 0:
                                # 写回文件
                                with open(file_path, 'w', encoding='utf-8') as f:
                                    f.write('\n'.join(lines))
                                
                                print(f"   ✅ 集成DeepSeek到: {file_path}")
                                integrated = True
                                break
                
                except Exception as e:
                    print(f"   ⚠️ 集成到 {file_path} 失败: {e}")
        
        if not integrated:
            print(f"   ⚠️ 未找到合适的自动化系统文件进行集成")

async def main():
    """主函数"""
    fixer = DeepSeekIntegrationFixer()
    await fixer.fix_all_roles()

if __name__ == "__main__":
    asyncio.run(main())
