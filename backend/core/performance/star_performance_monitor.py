#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业星座绩效监控系统
为七星量化投资系统提供专业级绩效监控功能
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import sqlite3
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class PerformanceMetricType(Enum):
    """绩效指标类型"""
    ACCURACY = "accuracy"                # 准确性
    EFFICIENCY = "efficiency"            # 效率
    RELIABILITY = "reliability"          # 可靠性
    RESPONSE_TIME = "response_time"      # 响应时间
    SUCCESS_RATE = "success_rate"        # 成功率
    QUALITY_SCORE = "quality_score"      # 质量评分
    RISK_ADJUSTED_RETURN = "risk_adjusted_return"  # 风险调整收益
    SHARPE_RATIO = "sharpe_ratio"        # 夏普比率
    MAX_DRAWDOWN = "max_drawdown"        # 最大回撤
    VOLATILITY = "volatility"            # 波动率
    TASK_COUNT = "task_count"            # 任务数量
    EXECUTION_COUNT = "execution_count"  # 执行次数
    ORDER_COUNT = "order_count"          # 订单数量
    TRADE_COUNT = "trade_count"          # 交易次数

@dataclass
class PerformanceRecord:
    """绩效记录"""
    id: Optional[int] = None
    star_name: str = ""
    metric_type: PerformanceMetricType = PerformanceMetricType.ACCURACY
    value: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    context: Dict[str, Any] = field(default_factory=dict)
    session_id: Optional[str] = None

@dataclass
class StarPerformanceReport:
    """星座绩效报告"""
    star_name: str
    overall_score: float
    metrics: Dict[str, float]
    target_achievement: Dict[str, float]
    record_count: int
    last_updated: Optional[str]
    trend_analysis: Dict[str, Any]
    recommendations: List[str]

class StarPerformanceMonitor:
    """专业星座绩效监控器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "data/performance/star_performance.db"
        self.star_configs = {
            "天权星": {
                "targets": {
                    PerformanceMetricType.ACCURACY: 0.90,
                    PerformanceMetricType.EFFICIENCY: 0.85,
                    PerformanceMetricType.SUCCESS_RATE: 0.88,
                    PerformanceMetricType.QUALITY_SCORE: 0.85
                },
                "weights": {
                    PerformanceMetricType.ACCURACY: 0.3,
                    PerformanceMetricType.EFFICIENCY: 0.25,
                    PerformanceMetricType.SUCCESS_RATE: 0.25,
                    PerformanceMetricType.QUALITY_SCORE: 0.2
                }
            },
            "天璇星": {
                "targets": {
                    PerformanceMetricType.ACCURACY: 0.85,
                    PerformanceMetricType.QUALITY_SCORE: 0.80,
                    PerformanceMetricType.SUCCESS_RATE: 0.82,
                    PerformanceMetricType.EFFICIENCY: 0.78
                },
                "weights": {
                    PerformanceMetricType.ACCURACY: 0.35,
                    PerformanceMetricType.QUALITY_SCORE: 0.3,
                    PerformanceMetricType.SUCCESS_RATE: 0.2,
                    PerformanceMetricType.EFFICIENCY: 0.15
                }
            },
            "天玑星": {
                "targets": {
                    PerformanceMetricType.ACCURACY: 0.95,
                    PerformanceMetricType.RELIABILITY: 0.98,
                    PerformanceMetricType.QUALITY_SCORE: 0.92,
                    PerformanceMetricType.RISK_ADJUSTED_RETURN: 0.15
                },
                "weights": {
                    PerformanceMetricType.ACCURACY: 0.25,
                    PerformanceMetricType.RELIABILITY: 0.3,
                    PerformanceMetricType.QUALITY_SCORE: 0.25,
                    PerformanceMetricType.RISK_ADJUSTED_RETURN: 0.2
                }
            },
            "天枢星": {
                "targets": {
                    PerformanceMetricType.ACCURACY: 0.82,
                    PerformanceMetricType.EFFICIENCY: 0.80,
                    PerformanceMetricType.QUALITY_SCORE: 0.75,
                    PerformanceMetricType.RESPONSE_TIME: 0.9
                },
                "weights": {
                    PerformanceMetricType.ACCURACY: 0.3,
                    PerformanceMetricType.EFFICIENCY: 0.25,
                    PerformanceMetricType.QUALITY_SCORE: 0.25,
                    PerformanceMetricType.RESPONSE_TIME: 0.2
                }
            },
            "玉衡星": {
                "targets": {
                    PerformanceMetricType.SUCCESS_RATE: 0.92,
                    PerformanceMetricType.EFFICIENCY: 0.88,
                    PerformanceMetricType.RELIABILITY: 0.95,
                    PerformanceMetricType.RESPONSE_TIME: 0.95
                },
                "weights": {
                    PerformanceMetricType.SUCCESS_RATE: 0.35,
                    PerformanceMetricType.EFFICIENCY: 0.25,
                    PerformanceMetricType.RELIABILITY: 0.25,
                    PerformanceMetricType.RESPONSE_TIME: 0.15
                }
            },
            "开阳星": {
                "targets": {
                    PerformanceMetricType.ACCURACY: 0.78,
                    PerformanceMetricType.QUALITY_SCORE: 0.75,
                    PerformanceMetricType.SUCCESS_RATE: 0.80,
                    PerformanceMetricType.EFFICIENCY: 0.75
                },
                "weights": {
                    PerformanceMetricType.ACCURACY: 0.3,
                    PerformanceMetricType.QUALITY_SCORE: 0.25,
                    PerformanceMetricType.SUCCESS_RATE: 0.25,
                    PerformanceMetricType.EFFICIENCY: 0.2
                }
            },
            "瑶光星": {
                "targets": {
                    PerformanceMetricType.EFFICIENCY: 0.85,
                    PerformanceMetricType.QUALITY_SCORE: 0.80,
                    PerformanceMetricType.RELIABILITY: 0.90,
                    PerformanceMetricType.ACCURACY: 0.82
                },
                "weights": {
                    PerformanceMetricType.EFFICIENCY: 0.3,
                    PerformanceMetricType.QUALITY_SCORE: 0.25,
                    PerformanceMetricType.RELIABILITY: 0.25,
                    PerformanceMetricType.ACCURACY: 0.2
                }
            }
        }
        
        # 初始化数据库
        self._init_database()
        logger.info("专业星座绩效监控器初始化完成")
    
    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        star_name TEXT NOT NULL,
                        metric_type TEXT NOT NULL,
                        value REAL NOT NULL,
                        timestamp TEXT NOT NULL,
                        context TEXT,
                        session_id TEXT
                    )
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_star_metric 
                    ON performance_records(star_name, metric_type)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_timestamp 
                    ON performance_records(timestamp)
                ''')
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    

    async def record_performance(self, star_name: str, metric_type: Any, value: float, context: Dict[str, Any] = None):
        """记录星座绩效 - 修复版本"""
        try:
            # 处理metric_type参数
            if isinstance(metric_type, str):
                # 字符串类型直接使用
                metric_name = metric_type
            elif hasattr(metric_type, 'value'):
                # 枚举类型
                metric_name = metric_type.value
            else:
                # 其他类型转换为字符串
                metric_name = str(metric_type)
            
            # 记录绩效数据
            performance_data = {
                "star_name": star_name,
                "metric_name": metric_name,
                "value": value,
                "context": context or {},
                "timestamp": datetime.now().isoformat()
            }
            
            # 存储到绩效数据库
            await self._store_performance_data(performance_data)
            
            logger.info(f"记录{star_name}绩效: {metric_name}={value}")
            return True
            
        except Exception as e:
            logger.error(f"记录绩效失败: {e}")
            return False

    async def _store_performance_data(self, performance_data: Dict[str, Any]) -> bool:
        """存储绩效数据"""
        try:
            # 存储到内存中的绩效记录
            if not hasattr(self, 'performance_records'):
                self.performance_records = []

            self.performance_records.append(performance_data)

            # 限制记录数量，保留最近1000条
            if len(self.performance_records) > 1000:
                self.performance_records = self.performance_records[-1000:]

            return True

        except Exception as e:
            logger.error(f"存储绩效数据失败: {e}")
            return False

# 全局实例
star_performance_monitor = StarPerformanceMonitor()

__all__ = ['StarPerformanceMonitor', 'PerformanceMetricType', 'PerformanceRecord', 
           'StarPerformanceReport', 'star_performance_monitor']
