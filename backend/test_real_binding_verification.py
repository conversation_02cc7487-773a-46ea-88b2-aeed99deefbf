#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七星真实绑定验证测试
验证每个角色的四大系统是否真正绑定
"""

import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def test_real_binding_verification():
    print('🔍 七星真实绑定验证测试...')
    print('=' * 80)
    
    binding_results = {}
    
    # 1. 测试天权星绑定
    print('\n👑 测试天权星绑定...')
    try:
        from roles.tianquan_star.services.advanced_strategy_adjustment_system import AdvancedStrategyAdjustmentSystem
        
        tianquan_service = AdvancedStrategyAdjustmentSystem()
        
        # 检查四大系统绑定
        has_memory = hasattr(tianquan_service, 'memory_system') and tianquan_service.memory_system is not None
        has_deepseek = hasattr(tianquan_service, 'deepseek_memory_config') and tianquan_service.deepseek_memory_config is not None
        has_performance = hasattr(tianquan_service, 'performance_monitor') and tianquan_service.performance_monitor is not None
        has_permission = hasattr(tianquan_service, 'permission_system') and tianquan_service.permission_system is not None
        
        binding_results['天权星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }
        
        print(f'   ✅ 天权星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["天权星"]["绑定率"]:.1f}%')
        
        # 测试记忆触发功能
        if has_memory and hasattr(tianquan_service, '_trigger_deepseek_memory'):
            await tianquan_service._trigger_deepseek_memory(
                "major_decision", 
                "测试天权星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')
        
    except Exception as e:
        print(f'   ❌ 天权星绑定检查失败: {e}')
        binding_results['天权星'] = {'绑定率': 0}
    
    # 2. 测试天璇星绑定
    print('\n🌟 测试天璇星绑定...')
    try:
        from roles.tianxuan_star.services.disc_finllm_strategy_service import DISCFinLLMStrategyService
        
        tianxuan_service = DISCFinLLMStrategyService()
        
        # 检查四大系统绑定
        has_memory = hasattr(tianxuan_service, 'memory_system') and tianxuan_service.memory_system is not None
        has_deepseek = hasattr(tianxuan_service, 'deepseek_memory_config') and tianxuan_service.deepseek_memory_config is not None
        has_performance = hasattr(tianxuan_service, 'performance_monitor') and tianxuan_service.performance_monitor is not None
        has_permission = hasattr(tianxuan_service, 'permission_system') and tianxuan_service.permission_system is not None
        
        binding_results['天璇星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }
        
        print(f'   ✅ 天璇星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["天璇星"]["绑定率"]:.1f}%')
        
        # 测试记忆触发功能
        if has_memory and hasattr(tianxuan_service, '_trigger_deepseek_memory'):
            await tianxuan_service._trigger_deepseek_memory(
                "strong_signal", 
                "测试天璇星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')
        
    except Exception as e:
        print(f'   ❌ 天璇星绑定检查失败: {e}')
        binding_results['天璇星'] = {'绑定率': 0}
    
    # 3. 测试天玑星绑定
    print('\n🛡️ 测试天玑星绑定...')
    try:
        from roles.tianji_star.services.risk_assessment_service import RiskAssessmentService
        
        tianji_service = RiskAssessmentService()
        
        # 检查四大系统绑定
        has_memory = hasattr(tianji_service, 'memory_system') and tianji_service.memory_system is not None
        has_deepseek = hasattr(tianji_service, 'deepseek_memory_config') and tianji_service.deepseek_memory_config is not None
        has_performance = hasattr(tianji_service, 'performance_monitor') and tianji_service.performance_monitor is not None
        has_permission = hasattr(tianji_service, 'permission_system') and tianji_service.permission_system is not None
        
        binding_results['天玑星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }
        
        print(f'   ✅ 天玑星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["天玑星"]["绑定率"]:.1f}%')
        
        # 测试记忆触发功能
        if has_memory and hasattr(tianji_service, '_trigger_deepseek_memory'):
            await tianji_service._trigger_deepseek_memory(
                "high_risk_event", 
                "测试天玑星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')
        
    except Exception as e:
        print(f'   ❌ 天玑星绑定检查失败: {e}')
        binding_results['天玑星'] = {'绑定率': 0}
    
    # 4. 测试天枢星绑定
    print('\n📰 测试天枢星绑定...')
    try:
        from roles.tianshu_star.services.news_driven_analysis_service import NewsDrivenAnalysisService
        
        tianshu_service = NewsDrivenAnalysisService()
        
        # 检查四大系统绑定
        has_memory = hasattr(tianshu_service, 'memory_system') and tianshu_service.memory_system is not None
        has_deepseek = hasattr(tianshu_service, 'deepseek_memory_config') and tianshu_service.deepseek_memory_config is not None
        has_performance = hasattr(tianshu_service, 'performance_monitor') and tianshu_service.performance_monitor is not None
        has_permission = hasattr(tianshu_service, 'permission_system') and tianshu_service.permission_system is not None
        
        binding_results['天枢星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }
        
        print(f'   ✅ 天枢星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["天枢星"]["绑定率"]:.1f}%')
        
        # 测试记忆触发功能
        if has_memory and hasattr(tianshu_service, '_trigger_deepseek_memory'):
            await tianshu_service._trigger_deepseek_memory(
                "breaking_news", 
                "测试天枢星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')
        
    except Exception as e:
        print(f'   ❌ 天枢星绑定检查失败: {e}')
        binding_results['天枢星'] = {'绑定率': 0}
    
    # 5. 测试玉衡星绑定
    print('\n⚖️ 测试玉衡星绑定...')
    try:
        from roles.yuheng_star.services.trading_execution_service import TradingExecutionService

        yuheng_service = TradingExecutionService()

        # 检查四大系统绑定
        has_memory = hasattr(yuheng_service, 'memory_system') and yuheng_service.memory_system is not None
        has_deepseek = hasattr(yuheng_service, 'deepseek_memory_config') and yuheng_service.deepseek_memory_config is not None
        has_performance = hasattr(yuheng_service, 'performance_monitor') and yuheng_service.performance_monitor is not None
        has_permission = hasattr(yuheng_service, 'permission_system') and yuheng_service.permission_system is not None

        binding_results['玉衡星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }

        print(f'   ✅ 玉衡星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["玉衡星"]["绑定率"]:.1f}%')

        # 测试记忆触发功能
        if has_memory and hasattr(yuheng_service, '_trigger_deepseek_memory'):
            await yuheng_service._trigger_deepseek_memory(
                "execution_completed",
                "测试玉衡星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')

    except Exception as e:
        print(f'   ❌ 玉衡星绑定检查失败: {e}')
        binding_results['玉衡星'] = {'绑定率': 0}

    # 6. 测试开阳星绑定
    print('\n🌟 测试开阳星绑定...')
    try:
        from roles.kaiyang_star.services.enhanced_stock_screening_service import EnhancedStockScreeningService

        kaiyang_service = EnhancedStockScreeningService()

        # 检查四大系统绑定
        has_memory = hasattr(kaiyang_service, 'memory_system') and kaiyang_service.memory_system is not None
        has_deepseek = hasattr(kaiyang_service, 'deepseek_memory_config') and kaiyang_service.deepseek_memory_config is not None
        has_performance = hasattr(kaiyang_service, 'performance_monitor') and kaiyang_service.performance_monitor is not None
        has_permission = hasattr(kaiyang_service, 'permission_system') and kaiyang_service.permission_system is not None

        binding_results['开阳星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }

        print(f'   ✅ 开阳星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["开阳星"]["绑定率"]:.1f}%')

        # 测试记忆触发功能
        if has_memory and hasattr(kaiyang_service, '_trigger_deepseek_memory'):
            await kaiyang_service._trigger_deepseek_memory(
                "high_score_stock",
                "测试开阳星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')

    except Exception as e:
        print(f'   ❌ 开阳星绑定检查失败: {e}')
        binding_results['开阳星'] = {'绑定率': 0}

    # 7. 测试瑶光星绑定
    print('\n💎 测试瑶光星绑定...')
    try:
        from roles.yaoguang_star.services.learning_environment_service import LearningEnvironmentService

        yaoguang_service = LearningEnvironmentService()

        # 检查四大系统绑定
        has_memory = hasattr(yaoguang_service, 'memory_system') and yaoguang_service.memory_system is not None
        has_deepseek = hasattr(yaoguang_service, 'deepseek_memory_config') and yaoguang_service.deepseek_memory_config is not None
        has_performance = hasattr(yaoguang_service, 'performance_monitor') and yaoguang_service.performance_monitor is not None
        has_permission = hasattr(yaoguang_service, 'permission_system') and yaoguang_service.permission_system is not None

        binding_results['瑶光星'] = {
            '传奇记忆系统': has_memory,
            'DeepSeek配置': has_deepseek,
            '绩效监控': has_performance,
            '层级权限': has_permission,
            '绑定率': sum([has_memory, has_deepseek, has_performance, has_permission]) / 4 * 100
        }

        print(f'   ✅ 瑶光星绑定检查完成:')
        print(f'      - 传奇记忆系统: {"✓" if has_memory else "✗"}')
        print(f'      - DeepSeek配置: {"✓" if has_deepseek else "✗"}')
        print(f'      - 绩效监控: {"✓" if has_performance else "✗"}')
        print(f'      - 层级权限: {"✓" if has_permission else "✗"}')
        print(f'      - 绑定率: {binding_results["瑶光星"]["绑定率"]:.1f}%')

        # 测试记忆触发功能
        if has_memory and hasattr(yaoguang_service, '_trigger_deepseek_memory'):
            await yaoguang_service._trigger_deepseek_memory(
                "learning_improvement",
                "测试瑶光星记忆触发功能"
            )
            print(f'      - 记忆触发测试: ✓')

    except Exception as e:
        print(f'   ❌ 瑶光星绑定检查失败: {e}')
        binding_results['瑶光星'] = {'绑定率': 0}
    
    # 6. 生成绑定报告
    print('\n' + '=' * 80)
    print('📊 七星真实绑定验证报告')
    print('=' * 80)
    
    total_binding_rate = 0
    completed_stars = 0
    
    for star_name, result in binding_results.items():
        binding_rate = result.get('绑定率', 0)
        total_binding_rate += binding_rate
        
        if binding_rate > 0:
            completed_stars += 1
            
        status = "✅ 完成" if binding_rate >= 75 else "⚠️ 部分" if binding_rate > 0 else "❌ 未绑定"
        print(f'{star_name}: {binding_rate:.1f}% {status}')
    
    overall_rate = total_binding_rate / len(binding_results)
    print(f'\n🎯 总体绑定率: {overall_rate:.1f}%')
    print(f'📈 已完成绑定: {completed_stars}/7 个星座')
    
    if overall_rate >= 75:
        print('🏆 绑定状态: 优秀')
    elif overall_rate >= 50:
        print('✅ 绑定状态: 良好')
    elif overall_rate >= 25:
        print('⚠️ 绑定状态: 需要改进')
    else:
        print('❌ 绑定状态: 严重不足')
    
    print('\n📝 下一步工作:')
    for star_name, result in binding_results.items():
        if result.get('绑定率', 0) < 100:
            print(f'   - 完善{star_name}的四大系统绑定')
    
    print('\n🎯 结论: 已开始真实绑定工作，需要继续完善其他星座')

if __name__ == "__main__":
    asyncio.run(test_real_binding_verification())
