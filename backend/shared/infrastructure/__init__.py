# -*- coding: utf-8 -*-
"""
共享基础设施模块
"""

from .investment_node_service import InvestmentNodeService, InvestmentNode, NodeType
from .investment_retry_service import (
InvestmentRetryService, ErrorType, RetryStrategy,
ErrorSeverity, RetryConfig
)
from .continuous_learning_service import ContinuousLearningService
from .deepseek_service import DeepSeekService

# 创建全局实例
deepseek_service = DeepSeekService()
# MCP导入已替换为本地RD-Agent
# MCP导入已替换为本地RD-Agent
from .role_permission_manager import RolePermissionManager
from .task_manager import TaskManager

__all__ = [
'InvestmentNodeService',
'InvestmentNode',
'NodeType',
'InvestmentRetryService',
'ErrorType',
'RetryStrategy',
'ErrorSeverity',
'RetryConfig',
'ContinuousLearningService',
'DeepSeekService',
'deepseek_service',
'Crawl4AIMCPService',
'MCPClient',
'RolePermissionManager',
'TaskManager'
]
