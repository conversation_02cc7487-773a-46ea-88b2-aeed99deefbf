#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星核心系统集成
整合所有核心功能：学习流程、交易自动化、Alpha158因子、统一数据收集
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from .real_learning_process_engine import real_learning_process_engine
from .real_trading_automation import real_trading_automation
from .alpha158_factor_calculator import alpha158_factor_calculator
from .unified_data_collector import unified_data_collector

logger = logging.getLogger(__name__)

class YaoguangCoreIntegration:
    """瑶光星核心系统集成"""
    
    def __init__(self):
        self.learning_engine = real_learning_process_engine
        self.trading_automation = real_trading_automation
        self.factor_calculator = alpha158_factor_calculator
        self.data_collector = unified_data_collector
        
        self.system_status = {
            "learning_engine": "initialized",
            "trading_automation": "initialized", 
            "factor_calculator": "initialized",
            "data_collector": "initialized"
        }
        
        logger.info("🌟 瑶光星核心系统集成初始化完成")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        
        # 检查数据质量
        data_quality = await self.data_collector.get_data_quality_report()
        
        # 检查学习引擎状态
        learning_sessions = len(self.learning_engine.learning_sessions)
        
        # 检查交易自动化状态
        trading_sessions = len(self.trading_automation.trading_sessions)
        
        return {
            "success": True,
            "system_name": "瑶光星核心系统",
            "version": "2.0.0",
            "status": "active",
            "components": {
                "learning_engine": {
                    "status": self.system_status["learning_engine"],
                    "sessions": learning_sessions,
                    "current_phase": self.learning_engine.current_phase.value
                },
                "trading_automation": {
                    "status": self.system_status["trading_automation"],
                    "sessions": trading_sessions,
                    "mode": self.trading_automation.trading_mode.value
                },
                "factor_calculator": {
                    "status": self.system_status["factor_calculator"],
                    "factor_count": self.factor_calculator.factor_count,
                    "calculated_factors": len(self.factor_calculator.calculated_factors)
                },
                "data_collector": {
                    "status": self.system_status["data_collector"],
                    "data_quality": data_quality.get("data_quality_score", 0) if data_quality.get("success") else 0,
                    "total_records": data_quality.get("total_records", 0) if data_quality.get("success") else 0
                }
            },
            "integration_features": [
                "学习流程：练习→研究→实盘",
                "159个Alpha158因子计算",
                "真实交易自动化逻辑",
                "统一数据收集管理",
                "A股5151只股票支持"
            ],
            "last_update": datetime.now().isoformat()
        }
    
    async def start_complete_learning_cycle(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动完整学习周期（练习→研究→实盘）"""
        
        try:
            logger.info("🎓 启动瑶光星完整学习周期")
            
            # 1. 数据准备阶段
            data_prep_result = await self._prepare_learning_data(config)
            if not data_prep_result["success"]:
                return {
                    "success": False,
                    "error": "数据准备失败",
                    "details": data_prep_result
                }
            
            # 2. 因子计算阶段
            factor_result = await self._calculate_learning_factors(config, data_prep_result["data"])
            if not factor_result["success"]:
                return {
                    "success": False,
                    "error": "因子计算失败",
                    "details": factor_result
                }
            
            # 3. 学习流程执行
            learning_config = config.copy()
            learning_config["factors"] = factor_result["factors"]
            learning_config["data"] = data_prep_result["data"]
            
            learning_result = await self.learning_engine.start_learning_cycle(learning_config)
            
            if not learning_result["success"]:
                return {
                    "success": False,
                    "error": "学习流程失败",
                    "details": learning_result
                }
            
            # 4. 如果学习成功且配置允许，启动自动化交易
            if config.get("enable_auto_trading", False) and learning_result["success"]:
                trading_config = {
                    "strategy": learning_result["learning_cycle"]["phases"]["research"]["optimizations"],
                    "stock_pool": config.get("stock_pool", []),
                    "duration_hours": config.get("trading_duration", 1)
                }
                
                trading_result = await self.trading_automation.start_automated_trading(trading_config)
                learning_result["trading_result"] = trading_result
            
            return {
                "success": True,
                "learning_cycle": learning_result,
                "integration_type": "complete_cycle",
                "completion_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"完整学习周期失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "integration_type": "complete_cycle"
            }
    
    async def _prepare_learning_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """准备学习数据"""
        
        try:
            stock_pool = config.get("stock_pool", ["000001", "000002", "600000"])
            days = config.get("data_days", 30)
            
            # 使用统一数据收集器获取数据
            batch_result = await self.data_collector.get_batch_stock_data(stock_pool, days)
            
            if not batch_result["success"]:
                return {
                    "success": False,
                    "error": "数据收集失败",
                    "details": batch_result
                }
            
            # 整理数据格式
            prepared_data = {}
            for stock_code, data_result in batch_result["results"].items():
                if data_result["success"]:
                    prepared_data[stock_code] = data_result["data"]
            
            return {
                "success": True,
                "data": prepared_data,
                "stock_count": len(prepared_data),
                "data_source": "unified_collector"
            }
            
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _calculate_learning_factors(self, config: Dict[str, Any], data: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """计算学习因子"""
        
        try:
            import pandas as pd
            
            all_factors = {}
            
            for stock_code, stock_data in data.items():
                if not stock_data:
                    continue
                
                # 转换为DataFrame
                df = pd.DataFrame(stock_data)
                
                # 确保数据类型正确
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # 计算Alpha158因子
                factor_result = self.factor_calculator.calculate_all_factors(df)
                
                if factor_result.get("success"):
                    all_factors[stock_code] = factor_result["factors"]
            
            return {
                "success": True,
                "factors": all_factors,
                "stock_count": len(all_factors),
                "factor_count": len(all_factors[list(all_factors.keys())[0]]) if all_factors else 0
            }
            
        except Exception as e:
            logger.error(f"因子计算失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_comprehensive_analysis(self, stock_code: str) -> Dict[str, Any]:
        """获取股票的综合分析"""
        
        try:
            # 1. 获取股票数据
            data_result = await self.data_collector.get_unified_stock_data(stock_code, days=60)
            
            if not data_result["success"]:
                return {
                    "success": False,
                    "error": "数据获取失败",
                    "stock_code": stock_code
                }
            
            # 2. 计算Alpha158因子
            import pandas as pd
            df = pd.DataFrame(data_result["data"])
            
            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            factor_result = self.factor_calculator.calculate_all_factors(df)
            
            # 3. 生成交易信号
            trading_signals = await self._generate_trading_signals(stock_code, df, factor_result.get("factors", {}))
            
            # 4. 风险评估
            risk_assessment = await self._assess_risk(stock_code, df)
            
            return {
                "success": True,
                "stock_code": stock_code,
                "analysis_time": datetime.now().isoformat(),
                "data_summary": {
                    "record_count": len(data_result["data"]),
                    "data_source": data_result["data_source"],
                    "date_range": {
                        "start": data_result["data"][-1]["date"] if data_result["data"] else None,
                        "end": data_result["data"][0]["date"] if data_result["data"] else None
                    }
                },
                "alpha158_factors": factor_result,
                "trading_signals": trading_signals,
                "risk_assessment": risk_assessment,
                "integration_analysis": {
                    "overall_score": self._calculate_overall_score(factor_result, trading_signals, risk_assessment),
                    "recommendation": self._generate_recommendation(factor_result, trading_signals, risk_assessment)
                }
            }
            
        except Exception as e:
            logger.error(f"综合分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code
            }
    
    async def _generate_trading_signals(self, stock_code: str, df: pd.DataFrame, factors: Dict[str, float]) -> Dict[str, Any]:
        """生成交易信号"""
        
        try:
            signals = []
            
            # 基于Alpha158因子生成信号
            if factors:
                # 动量信号
                momentum_score = factors.get("alpha081", 0) + factors.get("alpha082", 0) + factors.get("alpha083", 0)
                if momentum_score > 0.05:
                    signals.append({"type": "buy", "reason": "动量因子积极", "confidence": 0.7})
                elif momentum_score < -0.05:
                    signals.append({"type": "sell", "reason": "动量因子消极", "confidence": 0.7})
                
                # 技术指标信号
                rsi = factors.get("alpha041", 50)
                if rsi < 30:
                    signals.append({"type": "buy", "reason": "RSI超卖", "confidence": 0.6})
                elif rsi > 70:
                    signals.append({"type": "sell", "reason": "RSI超买", "confidence": 0.6})
            
            # 基于价格数据生成信号
            if len(df) >= 20:
                current_price = df['close'].iloc[-1]
                ma20 = df['close'].rolling(window=20).mean().iloc[-1]
                
                if current_price > ma20 * 1.02:
                    signals.append({"type": "buy", "reason": "价格突破20日均线", "confidence": 0.5})
                elif current_price < ma20 * 0.98:
                    signals.append({"type": "sell", "reason": "价格跌破20日均线", "confidence": 0.5})
            
            return {
                "success": True,
                "signals": signals,
                "signal_count": len(signals),
                "overall_sentiment": self._calculate_sentiment(signals)
            }
            
        except Exception as e:
            logger.error(f"交易信号生成失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _assess_risk(self, stock_code: str, df: pd.DataFrame) -> Dict[str, Any]:
        """风险评估"""
        
        try:
            risk_metrics = {}
            
            if len(df) >= 20:
                # 计算波动率
                returns = df['close'].pct_change()
                volatility = returns.std() * (252 ** 0.5)  # 年化波动率
                risk_metrics["volatility"] = volatility
                
                # 计算最大回撤
                cumulative = (1 + returns).cumprod()
                running_max = cumulative.expanding().max()
                drawdown = (cumulative - running_max) / running_max
                max_drawdown = drawdown.min()
                risk_metrics["max_drawdown"] = abs(max_drawdown)
                
                # 风险等级
                if volatility < 0.2:
                    risk_level = "低"
                elif volatility < 0.4:
                    risk_level = "中"
                else:
                    risk_level = "高"
                
                risk_metrics["risk_level"] = risk_level
            
            return {
                "success": True,
                "risk_metrics": risk_metrics,
                "assessment_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _calculate_sentiment(self, signals: List[Dict[str, Any]]) -> str:
        """计算整体情绪"""
        
        if not signals:
            return "中性"
        
        buy_signals = sum(1 for s in signals if s["type"] == "buy")
        sell_signals = sum(1 for s in signals if s["type"] == "sell")
        
        if buy_signals > sell_signals:
            return "看涨"
        elif sell_signals > buy_signals:
            return "看跌"
        else:
            return "中性"
    
    def _calculate_overall_score(self, factor_result: Dict, trading_signals: Dict, risk_assessment: Dict) -> float:
        """计算综合评分"""
        
        score = 50  # 基础分
        
        # 因子评分
        if factor_result.get("success"):
            score += 20
        
        # 交易信号评分
        if trading_signals.get("success"):
            signals = trading_signals.get("signals", [])
            if signals:
                avg_confidence = sum(s.get("confidence", 0) for s in signals) / len(signals)
                score += avg_confidence * 20
        
        # 风险评分
        if risk_assessment.get("success"):
            risk_level = risk_assessment.get("risk_metrics", {}).get("risk_level", "中")
            if risk_level == "低":
                score += 10
            elif risk_level == "高":
                score -= 10
        
        return min(100, max(0, score))
    
    def _generate_recommendation(self, factor_result: Dict, trading_signals: Dict, risk_assessment: Dict) -> str:
        """生成投资建议"""
        
        if not factor_result.get("success"):
            return "数据不足，无法给出建议"
        
        sentiment = trading_signals.get("overall_sentiment", "中性")
        risk_level = risk_assessment.get("risk_metrics", {}).get("risk_level", "中")
        
        if sentiment == "看涨" and risk_level == "低":
            return "建议买入，风险较低"
        elif sentiment == "看涨" and risk_level == "中":
            return "谨慎买入，注意风险控制"
        elif sentiment == "看跌":
            return "建议卖出或观望"
        else:
            return "保持观望，等待更明确信号"

# 全局实例
yaoguang_core_integration = YaoguangCoreIntegration()
