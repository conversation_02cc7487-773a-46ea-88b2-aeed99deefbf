#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间控制引擎
防止未来函数，确保历史回测真实性
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class TimeControlEngine:
    """时间控制引擎"""

    def __init__(self):
        self.current_backtest_time = None
        self.time_delays = {
            "news_delay": timedelta(minutes=15),      # 新闻传播延迟15分钟
            "decision_lag": timedelta(minutes=1),     # 决策延迟1分钟
            "execution_lag": timedelta(minutes=5),    # 执行延迟5分钟
            "announcement_delay": timedelta(hours=1)  # 公告传播延迟1小时
        }
        self.strict_mode = True  # 严格模式防止未来函数

        logger.info("时间控制引擎初始化完成")

    def set_backtest_time(self, backtest_time: datetime):
        """设置回测时间"""
        self.current_backtest_time = backtest_time
        logger.info(f"设置回测时间: {backtest_time}")

    def get_available_news_time(self) -> datetime:
        """获取可用新闻时间（考虑传播延迟）"""
        if not self.current_backtest_time:
            return datetime.now()

        # 新闻传播延迟15分钟
        available_time = self.current_backtest_time - self.time_delays["news_delay"]
        return available_time

    def is_data_available(self, data_time: datetime, data_type: str = "general") -> bool:
        """检查数据是否可用（防止未来函数）"""
        if not self.current_backtest_time:
            return True

        if not self.strict_mode:
            return True

        # 根据数据类型获取可用时间
        if data_type == "news":
            available_time = self.get_available_news_time()
        else:
            available_time = self.current_backtest_time

        # 数据时间必须早于可用时间
        return data_time <= available_time

# 全局时间控制引擎实例
time_control_engine = TimeControlEngine()

__all__ = ["TimeControlEngine", "time_control_engine"]
