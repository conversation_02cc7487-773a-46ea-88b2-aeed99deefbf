# 🛡️ 天玑星API文档

## 概述

天玑星是七星系统的风险管理中心，提供专业级的风险评估、投资组合分析、压力测试等服务。本文档详细描述了天玑星的所有API接口。

**基础信息**:
- **服务名称**: 天玑星风险管理中心
- **版本**: Professional v2.0
- **基础路径**: `/api/tianji`
- **认证方式**: Bearer <PERSON> (可选)
- **数据格式**: JSON

## 🔧 基础API

### 1. 健康检查

**接口**: `GET /api/tianji/health`

**描述**: 检查天玑星服务状态和各组件健康状况

**响应示例**:
```json
{
  "success": true,
  "message": "天玑星健康检查完成",
  "data": {
    "role": "天玑星-风险管理",
    "status": "healthy",
    "version": "2.0.0",
    "services": {
      "risk_assessment": "healthy",
      "knowledge_base": "healthy",
      "workflow_manager": "healthy"
    },
    "capabilities": [
      "股票风险评估",
      "投资组合风险分析",
      "实时风险监控",
      "风险预警告警",
      "风险知识库管理",
      "工作流管理"
    ]
  },
  "timestamp": "2025-06-19T21:30:00"
}
```

### 2. 服务状态

**接口**: `GET /api/tianji/status`

**描述**: 获取天玑星详细运行状态

**响应示例**:
```json
{
  "success": true,
  "message": "天玑星状态获取成功",
  "data": {
    "service_name": "天玑星风险管理中心",
    "status": "active",
    "version": "2.0.0",
    "workflow_statistics": {
      "total_executed": 156,
      "active_count": 3,
      "success_rate": 0.95
    },
    "active_workflows": [
      {
        "workflow_id": "portfolio_analysis_20250619_213000",
        "type": "portfolio_analysis",
        "status": "running"
      }
    ],
    "last_update": "2025-06-19T21:30:00"
  }
}
```

## 🛡️ 风险评估API

### 3. 股票风险评估

**接口**: `POST /api/tianji/risk/assess/stock`

**描述**: 评估单只股票的综合风险

**请求参数**:
```json
{
  "stock_code": "000001",
  "position_size": 100000,
  "market_context": {
    "market_trend": "bullish",
    "volatility_regime": "normal"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "股票风险评估完成: 000001",
  "data": {
    "stock_code": "000001",
    "risk_score": 0.65,
    "risk_level": "MEDIUM",
    "risk_breakdown": {
      "market_risk": 0.70,
      "liquidity_risk": 0.45,
      "volatility_risk": 0.80,
      "fundamental_risk": 0.55,
      "technical_risk": 0.60
    },
    "primary_risks": [
      "高波动性风险",
      "市场风险敞口",
      "技术面风险"
    ],
    "mitigation_suggestions": [
      "建议控制仓位规模",
      "设置止损点位",
      "关注技术指标变化"
    ],
    "assessment_time": "2025-06-19T21:30:00",
    "data_source": "real_data"
  }
}
```

### 4. 投资组合风险评估

**接口**: `POST /api/tianji/risk/assess/portfolio`

**描述**: 评估投资组合的整体风险

**请求参数**:
```json
{
  "portfolio": [
    {
      "stock_code": "000001",
      "market_value": 100000,
      "shares": 1000,
      "sector": "金融"
    },
    {
      "stock_code": "000002",
      "market_value": 80000,
      "shares": 800,
      "sector": "地产"
    }
  ],
  "analysis_type": "comprehensive"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "投资组合风险评估完成: 2只股票",
  "data": {
    "portfolio_summary": {
      "total_value": 180000,
      "position_count": 2,
      "effective_stocks": 1.8
    },
    "risk_metrics": {
      "portfolio_volatility": 0.25,
      "portfolio_beta": 1.15,
      "sharpe_ratio": 0.85,
      "var_95": 0.045,
      "var_99": 0.065
    },
    "risk_decomposition": {
      "top_3_risk_concentration": 0.75,
      "diversification_score": 0.25
    },
    "optimization_suggestions": [
      {
        "type": "risk_concentration",
        "priority": "high",
        "description": "前3只股票风险集中度过高，建议分散投资",
        "action": "减少高风险贡献股票的权重"
      }
    ]
  }
}
```

## 📊 风险监控API

### 5. 启动风险监控

**接口**: `POST /api/tianji/monitoring/start`

**描述**: 启动实时风险监控

**请求参数**:
```json
{
  "stock_codes": ["000001", "000002", "300001"],
  "monitoring_type": "real_time",
  "alert_threshold": 0.7
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "风险监控已启动: 3只股票",
  "data": {
    "monitoring_stocks": ["000001", "000002", "300001"],
    "monitoring_type": "real_time",
    "alert_threshold": 0.7,
    "start_time": "2025-06-19T21:30:00"
  }
}
```

### 6. 获取风险指标

**接口**: `GET /api/tianji/monitoring/metrics`

**描述**: 获取当前风险监控指标

**响应示例**:
```json
{
  "success": true,
  "message": "风险指标获取成功",
  "data": {
    "market_risk_level": "medium",
    "portfolio_var": 0.05,
    "max_drawdown": 0.12,
    "volatility": 0.18,
    "active_alerts": 3,
    "monitoring_stocks": 150,
    "last_update": "2025-06-19T21:30:00"
  }
}
```

## 🚨 风险预警API

### 7. 获取风险预警列表

**接口**: `GET /api/tianji/alerts/list?severity=all&limit=50`

**描述**: 获取风险预警信息列表

**查询参数**:
- `severity`: 预警级别 (all/high/medium/low)
- `limit`: 返回数量限制

**响应示例**:
```json
{
  "success": true,
  "message": "获取到5条风险预警",
  "data": {
    "alerts": [
      {
        "id": 1,
        "risk_type": "market_risk",
        "risk_level": "HIGH",
        "description": "市场波动率异常升高",
        "affected_symbols": "000001,000002",
        "risk_score": 0.85,
        "detection_time": "2025-06-19T21:25:00",
        "is_active": true
      }
    ],
    "total": 5,
    "severity_filter": "all"
  }
}
```

## ⚙️ 工作流管理API

### 8. 获取工作流列表

**接口**: `GET /api/tianji/workflow/list`

**描述**: 获取所有工作流状态

**响应示例**:
```json
{
  "success": true,
  "message": "工作流列表获取成功",
  "data": {
    "active_workflows": [
      {
        "workflow_id": "portfolio_analysis_20250619_213000",
        "type": "portfolio_analysis",
        "status": "running",
        "progress": 0.75
      }
    ],
    "statistics": {
      "total_executed": 156,
      "active_count": 1,
      "success_rate": 0.95
    },
    "available_types": [
      "portfolio_analysis",
      "stress_test",
      "risk_monitoring",
      "risk_assessment"
    ]
  }
}
```

### 9. 执行工作流

**接口**: `POST /api/tianji/workflow/execute`

**描述**: 执行指定类型的工作流

**请求参数**:
```json
{
  "workflow_type": "portfolio_analysis",
  "input_data": {
    "portfolio": [
      {
        "stock_code": "000001",
        "market_value": 100000,
        "shares": 1000
      }
    ]
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "工作流执行完成: portfolio_analysis",
  "data": {
    "workflow_id": "portfolio_analysis_20250619_213000",
    "status": "completed",
    "execution_time": 2.5,
    "results": {
      "final_report": {
        "analysis_id": "portfolio_analysis_20250619_213000",
        "portfolio_summary": {
          "total_value": 100000,
          "position_count": 1
        },
        "risk_metrics": {
          "portfolio_volatility": 0.20,
          "sharpe_ratio": 0.75
        }
      }
    }
  }
}
```

### 10. 获取工作流状态

**接口**: `GET /api/tianji/workflow/status/{workflow_id}`

**描述**: 获取指定工作流的详细状态

**响应示例**:
```json
{
  "success": true,
  "message": "工作流状态获取成功",
  "data": {
    "workflow_id": "portfolio_analysis_20250619_213000",
    "status": "completed",
    "start_time": "2025-06-19T21:30:00",
    "end_time": "2025-06-19T21:32:30",
    "execution_time": 150,
    "progress": 1.0,
    "current_step": "completed",
    "total_steps": 7,
    "errors": []
  }
}
```

## 🧠 知识库管理API

### 11. 添加风险到知识库

**接口**: `POST /api/tianji/knowledge/add_risk`

**描述**: 向风险知识库添加新的风险记录

**请求参数**:
```json
{
  "risk_type": "market_risk",
  "risk_level": "HIGH",
  "description": "市场系统性风险",
  "affected_symbols": ["000001", "000002"],
  "risk_score": 0.85,
  "mitigation_suggestions": ["分散投资", "降低仓位"]
}
```

### 12. 获取股票风险记录

**接口**: `GET /api/tianji/knowledge/stock_risks/{stock_code}?risk_levels=HIGH,MEDIUM`

**描述**: 从知识库获取指定股票的风险记录

**响应示例**:
```json
{
  "success": true,
  "message": "获取股票风险成功: 000001",
  "data": {
    "stock_code": "000001",
    "risks": [
      {
        "risk_id": 1,
        "risk_type": "market_risk",
        "risk_level": "HIGH",
        "description": "市场波动风险",
        "risk_score": 0.85,
        "detection_time": "2025-06-19T21:00:00",
        "mitigation_suggestions": "建议降低仓位"
      }
    ],
    "total": 1
  }
}
```

## 🧪 压力测试API

### 13. 风险模型构建

**接口**: `POST /api/tianji/risk/model/build`

**描述**: 构建或更新风险模型

**请求参数**:
```json
{
  "model_type": "portfolio_risk",
  "model_config": {
    "lookback_period": 252,
    "confidence_level": 0.95,
    "rebalance_frequency": "monthly"
  }
}
```

### 14. 获取风险模型列表

**接口**: `GET /api/tianji/risk/model/list`

**描述**: 获取已构建的风险模型列表

**响应示例**:
```json
{
  "success": true,
  "message": "获取到2个风险模型",
  "data": {
    "models": [
      {
        "model_id": "market_risk_v1",
        "model_type": "market_risk",
        "model_name": "市场风险模型V1",
        "accuracy": 0.85,
        "created_time": "2025-06-19T10:00:00",
        "status": "active"
      }
    ],
    "total": 2
  }
}
```

## 📋 统一响应格式

所有API都遵循统一的响应格式：

```json
{
  "success": boolean,      // 请求是否成功
  "message": string,       // 响应消息
  "data": object,         // 响应数据
  "timestamp": string     // 响应时间戳
}
```

## 🚫 错误处理

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "TIANJI_001",
  "details": "详细错误信息",
  "timestamp": "2025-06-19T21:30:00"
}
```

### 常见错误码

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| TIANJI_001 | 服务不可用 | 503 |
| TIANJI_002 | 参数验证失败 | 400 |
| TIANJI_003 | 股票代码不存在 | 404 |
| TIANJI_004 | 工作流执行失败 | 500 |
| TIANJI_005 | 数据库连接失败 | 500 |

## 📊 性能指标

- **平均响应时间**: < 100ms
- **并发处理能力**: 100+ 请求/秒
- **可用性**: 99.9%
- **数据准确性**: 95%+

---

**文档版本**: v2.0.0  
**最后更新**: 2025-06-19  
**维护团队**: 天玑星开发组
