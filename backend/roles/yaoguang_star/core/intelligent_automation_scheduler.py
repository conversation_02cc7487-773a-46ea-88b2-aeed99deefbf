#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星智能自动化调度系统
根据交易时间智能切换学习模式和实盘模式
"""

import asyncio
import logging
from datetime import datetime, time, timedelta
from typing import Dict, Any, Optional
import pytz
from enum import Enum

logger = logging.getLogger(__name__)

class AutomationMode(Enum):
    """自动化模式"""
    LEARNING = "learning"
    LIVE_TRADING = "live_trading"
    IDLE = "idle"

class MarketStatus(Enum):
    """市场状态"""
    PRE_MARKET = "pre_market"      # 盘前 (8:00-9:30)
    TRADING = "trading"            # 交易中 (9:30-15:00)
    POST_MARKET = "post_market"    # 盘后 (15:00-17:00)
    CLOSED = "closed"              # 休市

class IntelligentAutomationScheduler:
    """智能自动化调度系统"""
    
    def __init__(self):
        self.service_name = "IntelligentAutomationScheduler"
        self.version = "1.0.0"
        self.is_running = False
        self.current_mode = AutomationMode.IDLE
        self.current_market_status = MarketStatus.CLOSED
        
        # 交易时间配置 (北京时间)
        self.timezone = pytz.timezone('Asia/Shanghai')
        self.trading_days = [0, 1, 2, 3, 4]  # 周一到周五
        
        # 时间配置
        self.pre_market_start = time(8, 0)    # 8:00 盘前开始
        self.trading_start = time(9, 30)      # 9:30 开盘
        self.trading_end = time(15, 0)        # 15:00 收盘
        self.post_market_end = time(17, 0)    # 17:00 盘后结束
        
        # 自动化任务
        self.learning_task = None
        self.live_trading_task = None
        self.scheduler_task = None
        
        # 统计数据
        self.daily_stats = {
            "learning_sessions": 0,
            "live_trading_sessions": 0,
            "total_trades": 0,
            "total_pnl": 0.0,
            "last_update": None
        }
        
        logger.info(f"瑶光星智能自动化调度系统 v{self.version} 初始化完成")
    
    def get_current_beijing_time(self) -> datetime:
        """获取当前北京时间"""
        return datetime.now(self.timezone)
    
    def is_trading_day(self, dt: datetime = None) -> bool:
        """判断是否为交易日"""
        if dt is None:
            dt = self.get_current_beijing_time()
        return dt.weekday() in self.trading_days
    
    def get_market_status(self, dt: datetime = None) -> MarketStatus:
        """获取市场状态"""
        if dt is None:
            dt = self.get_current_beijing_time()
        
        if not self.is_trading_day(dt):
            return MarketStatus.CLOSED
        
        current_time = dt.time()
        
        if self.pre_market_start <= current_time < self.trading_start:
            return MarketStatus.PRE_MARKET
        elif self.trading_start <= current_time < self.trading_end:
            return MarketStatus.TRADING
        elif self.trading_end <= current_time < self.post_market_end:
            return MarketStatus.POST_MARKET
        else:
            return MarketStatus.CLOSED
    
    def should_run_live_trading(self) -> bool:
        """判断是否应该运行实盘交易"""
        market_status = self.get_market_status()
        return market_status in [MarketStatus.PRE_MARKET, MarketStatus.TRADING, MarketStatus.POST_MARKET]
    
    def should_run_learning(self) -> bool:
        """判断是否应该运行学习模式"""
        market_status = self.get_market_status()
        # 休市时间或盘后时间运行学习模式
        return market_status in [MarketStatus.CLOSED, MarketStatus.POST_MARKET]
    
    async def start_scheduler(self) -> Dict[str, Any]:
        """启动智能调度器"""
        try:
            if self.is_running:
                return {"success": False, "error": "调度器已在运行"}
            
            self.is_running = True
            logger.info("🚀 启动瑶光星智能自动化调度器")
            
            # 启动调度任务
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
            
            return {
                "success": True,
                "message": "智能调度器启动成功",
                "start_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def stop_scheduler(self) -> Dict[str, Any]:
        """停止智能调度器"""
        try:
            if not self.is_running:
                return {"success": False, "error": "调度器未运行"}
            
            self.is_running = False
            logger.info("🛑 停止瑶光星智能自动化调度器")
            
            # 停止所有任务
            if self.scheduler_task:
                self.scheduler_task.cancel()
            if self.learning_task:
                self.learning_task.cancel()
            if self.live_trading_task:
                self.live_trading_task.cancel()
            
            return {
                "success": True,
                "message": "智能调度器停止成功",
                "stop_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        try:
            while self.is_running:
                current_time = self.get_current_beijing_time()
                market_status = self.get_market_status(current_time)
                
                # 更新市场状态
                if market_status != self.current_market_status:
                    logger.info(f"📊 市场状态变化: {self.current_market_status.value} -> {market_status.value}")
                    self.current_market_status = market_status
                    await self._handle_mode_switch(market_status)
                
                # 检查模式切换
                await self._check_and_switch_mode()
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
        except asyncio.CancelledError:
            logger.info("调度器循环已取消")
        except Exception as e:
            logger.error(f"调度器循环异常: {e}")
    
    async def _handle_mode_switch(self, market_status: MarketStatus):
        """处理模式切换"""
        try:
            if market_status == MarketStatus.PRE_MARKET:
                logger.info("🌅 进入盘前时间，准备实盘交易")
                await self._switch_to_live_trading()
                
            elif market_status == MarketStatus.TRADING:
                logger.info("📈 进入交易时间，执行实盘交易")
                await self._ensure_live_trading_active()
                
            elif market_status == MarketStatus.POST_MARKET:
                logger.info("🌆 进入盘后时间，生成实盘报告并开始学习")
                await self._generate_daily_trading_report()
                await self._switch_to_learning()
                
            elif market_status == MarketStatus.CLOSED:
                logger.info("🌙 进入休市时间，专注学习模式")
                await self._switch_to_learning()
                
        except Exception as e:
            logger.error(f"模式切换处理失败: {e}")
    
    async def _switch_to_live_trading(self):
        """切换到实盘交易模式"""
        try:
            # 停止学习模式
            if self.learning_task and not self.learning_task.done():
                self.learning_task.cancel()
                logger.info("🛑 停止学习模式")
            
            # 启动实盘交易
            if not self.live_trading_task or self.live_trading_task.done():
                self.live_trading_task = asyncio.create_task(self._run_live_trading())
                logger.info("🚀 启动实盘交易模式")
            
            self.current_mode = AutomationMode.LIVE_TRADING
            
        except Exception as e:
            logger.error(f"切换到实盘交易失败: {e}")
    
    async def _switch_to_learning(self):
        """切换到学习模式"""
        try:
            # 停止实盘交易（如果在运行）
            if self.live_trading_task and not self.live_trading_task.done():
                self.live_trading_task.cancel()
                logger.info("🛑 停止实盘交易模式")
            
            # 启动学习模式
            if not self.learning_task or self.learning_task.done():
                self.learning_task = asyncio.create_task(self._run_learning())
                logger.info("🚀 启动学习模式")
            
            self.current_mode = AutomationMode.LEARNING
            
        except Exception as e:
            logger.error(f"切换到学习模式失败: {e}")
    
    async def _ensure_live_trading_active(self):
        """确保实盘交易处于活跃状态"""
        try:
            if not self.live_trading_task or self.live_trading_task.done():
                self.live_trading_task = asyncio.create_task(self._run_live_trading())
                logger.info("🔄 重新启动实盘交易模式")
                
        except Exception as e:
            logger.error(f"确保实盘交易活跃失败: {e}")
    
    async def _check_and_switch_mode(self):
        """检查并切换模式"""
        try:
            should_live = self.should_run_live_trading()
            should_learn = self.should_run_learning()
            
            if should_live and self.current_mode != AutomationMode.LIVE_TRADING:
                await self._switch_to_live_trading()
            elif should_learn and self.current_mode != AutomationMode.LEARNING:
                await self._switch_to_learning()
                
        except Exception as e:
            logger.error(f"检查模式切换失败: {e}")
    
    async def _run_live_trading(self):
        """运行实盘交易"""
        try:
            logger.info("💰 开始实盘交易自动化")
            
            # 导入瑶光星统一系统
            from .unified_yaoguang_system import unified_yaoguang_system
            
            # 启动实盘交易会话
            session_config = {
                "mode": "live_trading",
                "stocks_per_session": 5,
                "enable_real_trading": True,
                "enable_risk_control": True,
                "max_position_size": 0.2,  # 最大仓位20%
                "stop_loss_ratio": 0.05,   # 5%止损
                "take_profit_ratio": 0.15  # 15%止盈
            }
            
            session_result = await unified_yaoguang_system.start_live_trading_session(session_config)
            
            if session_result.get("success"):
                self.daily_stats["live_trading_sessions"] += 1
                logger.info(f"✅ 实盘交易会话启动: {session_result['session_id']}")
                
                # 持续监控直到收盘
                while self.should_run_live_trading() and self.is_running:
                    await asyncio.sleep(300)  # 每5分钟检查一次
                    
                    # 获取交易状态
                    status = await unified_yaoguang_system.get_trading_status()
                    if status.get("trades"):
                        self.daily_stats["total_trades"] += len(status["trades"])
                        self.daily_stats["total_pnl"] += sum(t.get("pnl", 0) for t in status["trades"])
            
        except asyncio.CancelledError:
            logger.info("实盘交易任务已取消")
        except Exception as e:
            logger.error(f"实盘交易运行失败: {e}")
    
    async def _run_learning(self):
        """运行学习模式"""
        try:
            logger.info("🎓 开始学习模式自动化")
            
            # 导入瑶光星统一系统
            from .unified_yaoguang_system import unified_yaoguang_system
            
            # 启动学习会话
            session_config = {
                "mode": "learning",
                "stocks_per_session": 3,
                "data_years": 2,
                "strategy_testing_enabled": True,
                "enable_factor_generation": True,
                "enable_multi_role_collaboration": True,
                "session_duration_minutes": 30
            }
            
            session_result = await unified_yaoguang_system.start_learning_session(session_config)
            
            if session_result.get("success"):
                self.daily_stats["learning_sessions"] += 1
                logger.info(f"✅ 学习会话启动: {session_result['session_id']}")
                
                # 等待学习完成
                while self.should_run_learning() and self.is_running:
                    await asyncio.sleep(600)  # 每10分钟检查一次
                    
                    # 检查学习状态
                    status = await unified_yaoguang_system.get_learning_status()
                    if not status.get("session_active"):
                        # 学习完成，开始新的学习会话
                        await asyncio.sleep(300)  # 等待5分钟再开始新会话
                        session_result = await unified_yaoguang_system.start_learning_session(session_config)
                        if session_result.get("success"):
                            self.daily_stats["learning_sessions"] += 1
            
        except asyncio.CancelledError:
            logger.info("学习模式任务已取消")
        except Exception as e:
            logger.error(f"学习模式运行失败: {e}")
    
    async def _generate_daily_trading_report(self):
        """生成每日实盘交易报告"""
        try:
            logger.info("📋 生成每日实盘交易报告")
            
            # 导入报告生成器
            from ..services.daily_trading_report_generator import daily_trading_report_generator
            
            # 生成报告
            report_result = await daily_trading_report_generator.generate_daily_report()
            
            if report_result.get("success"):
                logger.info("✅ 每日实盘交易报告生成成功")
            else:
                logger.warning(f"每日报告生成失败: {report_result.get('error')}")
                
        except Exception as e:
            logger.error(f"生成每日报告失败: {e}")
    
    async def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        try:
            current_time = self.get_current_beijing_time()
            market_status = self.get_market_status(current_time)
            
            return {
                "is_running": self.is_running,
                "current_mode": self.current_mode.value,
                "market_status": market_status.value,
                "is_trading_day": self.is_trading_day(current_time),
                "current_time": current_time.isoformat(),
                "should_run_live_trading": self.should_run_live_trading(),
                "should_run_learning": self.should_run_learning(),
                "daily_stats": self.daily_stats,
                "tasks_status": {
                    "learning_task": "running" if self.learning_task and not self.learning_task.done() else "stopped",
                    "live_trading_task": "running" if self.live_trading_task and not self.live_trading_task.done() else "stopped",
                    "scheduler_task": "running" if self.scheduler_task and not self.scheduler_task.done() else "stopped"
                }
            }
            
        except Exception as e:
            logger.error(f"获取调度器状态失败: {e}")
            return {"error": str(e)}

# 全局实例
intelligent_automation_scheduler = IntelligentAutomationScheduler()

__all__ = ["IntelligentAutomationScheduler", "intelligent_automation_scheduler", "AutomationMode", "MarketStatus"]
