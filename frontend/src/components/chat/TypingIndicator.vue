<!--
  ⌨️ 打字指示器组件
  
  @description 显示AI角色正在输入的动画效果
  <AUTHOR>
  @since 1.0.0
-->

<template>
  <div class="typing-indicator">
    <!-- 角色头像 -->
    <div class="typing-avatars">
      <RoleAvatar
        v-for="role in roles"
        :key="role"
        :role="role"
        size="small"
        :show-status="false"
        :show-name="false"
        :clickable="false"
        class="typing-avatar"
      />
    </div>
    
    <!-- 打字动画 -->
    <div class="typing-content">
      <div class="typing-text">
        <span v-if="roles.length === 1">
          {{ getRoleName(roles[0]) }} 正在输入
        </span>
        <span v-else-if="roles.length === 2">
          {{ getRoleName(roles[0]) }} 和 {{ getRoleName(roles[1]) }} 正在输入
        </span>
        <span v-else>
          {{ roles.length }} 个角色正在输入
        </span>
      </div>
      
      <!-- 动画点点 -->
      <div class="typing-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RoleType } from '@/types/business'
import RoleAvatar from '@/components/base/RoleAvatar.vue'

// Props定义
interface Props {
  /** 正在输入的角色列表 */
  roles: RoleType[]
}

const props = defineProps<Props>()

// 角色名称映射
const roleNameMap = {
  [RoleType.INTELLIGENCE]: '情报官',
  [RoleType.ARCHITECT]: '量化架构师',
  [RoleType.RISK]: '风控总监',
  [RoleType.TRADER]: '操盘手',
  [RoleType.STOCK_MANAGER]: '股票经理',
  [RoleType.COMMANDER]: '指挥官'
}

// 获取角色名称
const getRoleName = (role: RoleType): string => {
  return roleNameMap[role] || '未知角色'
}
</script>

<style lang="scss" scoped>
@import '@/styles/tokens/index.scss';
@import '@/styles/mixins/index.scss';

.typing-indicator {
  @include flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  margin-bottom: var(--spacing-4);
  max-width: 300px;
  
  @include fade-in;
}

.typing-avatars {
  display: flex;
  gap: var(--spacing-1);
  flex-shrink: 0;
  
  .typing-avatar {
    animation: typing-bounce 1.5s ease-in-out infinite;
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

.typing-content {
  background: var(--color-background);
  border: 1px solid var(--color-border-light);
  border-radius: 18px 18px 18px 4px;
  padding: var(--spacing-3) var(--spacing-4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  .typing-text {
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-1);
  }
  
  .typing-dots {
    @include flex-start;
    gap: var(--spacing-1);
    
    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--color-primary);
      animation: typing-pulse 1.4s ease-in-out infinite;
      
      &:nth-child(1) {
        animation-delay: 0s;
      }
      
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }
}

// 动画定义
@keyframes typing-bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

@keyframes typing-pulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 响应式设计
@include respond-to(mobile) {
  .typing-indicator {
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    
    .typing-content {
      padding: var(--spacing-2) var(--spacing-3);
      
      .typing-text {
        font-size: var(--font-size-xs);
      }
      
      .typing-dots .dot {
        width: 4px;
        height: 4px;
      }
    }
  }
}
</style>
