#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑶光星时间控制引擎
严格防止未来函数，确保回测真实性
"""

import logging
from datetime import datetime, timedelta, date
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import asyncio

logger = logging.getLogger(__name__)

class TimeMode(Enum):
    """时间模式"""
    BACKTEST = "backtest"      # 回测模式
    SIMULATION = "simulation"  # 基于真实数据的计算
    LIVE = "live"             # 实盘模式

@dataclass
class TimeWindow:
    """时间窗口"""
    start_date: date
    end_date: date
    current_date: date
    mode: TimeMode
    
    def is_valid_date(self, check_date: date) -> bool:
        """检查日期是否在有效范围内"""
        return self.start_date <= check_date <= self.current_date

class TimeControlEngine:
    """时间控制引擎 - 严格防止未来函数"""
    
    def __init__(self):
        self.service_name = "TimeControlEngine"
        self.version = "1.0.0"
        
        # 当前时间窗口
        self.current_window: Optional[TimeWindow] = None
        
        # 时间控制配置
        self.config = {
            "strict_mode": True,           # 严格模式
            "allow_future_data": False,    # 禁止未来数据
            "max_lookback_days": 252,      # 最大回看天数
            "trading_calendar": "A股",      # 交易日历
            "timezone": "Asia/Shanghai"     # 时区
        }
        
        # 违规记录
        self.violations: List[Dict] = []
        
        # 统计信息
        self.stats = {
            "total_checks": 0,
            "violations_count": 0,
            "sessions_created": 0,
            "data_requests": 0
        }
        
        logger.info(f"{self.service_name} v{self.version} 初始化完成")
    
    async def create_backtest_session(self, start_date: str, end_date: str, 
                                    initial_date: str = None) -> str:
        """创建回测会话"""
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
            
            if initial_date:
                current_dt = datetime.strptime(initial_date, "%Y-%m-%d").date()
            else:
                current_dt = start_dt
            
            # 验证日期合理性
            if start_dt >= end_dt:
                raise ValueError("开始日期必须早于结束日期")
            
            if current_dt < start_dt or current_dt > end_dt:
                raise ValueError("当前日期必须在开始和结束日期之间")
            
            # 创建时间窗口
            self.current_window = TimeWindow(
                start_date=start_dt,
                end_date=end_dt,
                current_date=current_dt,
                mode=TimeMode.BACKTEST
            )
            
            session_id = f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.stats["sessions_created"] += 1
            
            logger.info(f"  回测会话创建成功: {session_id}")
            logger.info(f"时间范围: {start_date} 到 {end_date}, 当前: {current_dt}")
            
            return session_id
            
        except Exception as e:
            logger.error(f"  创建回测会话失败: {e}")
            raise
    
    def advance_time(self, days: int = 1) -> date:
        """推进时间（仅在回测模式下）"""
        if not self.current_window:
            raise RuntimeError("未创建时间会话")
        
        if self.current_window.mode != TimeMode.BACKTEST:
            raise RuntimeError("只能在回测模式下推进时间")
        
        new_date = self.current_window.current_date + timedelta(days=days)
        
        if new_date > self.current_window.end_date:
            logger.warning(f"时间推进超出结束日期: {new_date} > {self.current_window.end_date}")
            new_date = self.current_window.end_date
        
        self.current_window.current_date = new_date
        
        logger.info(f"时间推进到: {new_date}")
        return new_date
    
    def check_data_access(self, request_date: str, data_type: str = "unknown") -> bool:
        """检查数据访问是否违反时间规则"""
        if not self.current_window:
            # 无时间控制会话时，允许访问
            return True
        
        try:
            req_date = datetime.strptime(request_date, "%Y-%m-%d").date()
        except ValueError:
            logger.error(f"无效的日期格式: {request_date}")
            return False
        
        self.stats["total_checks"] += 1
        self.stats["data_requests"] += 1
        
        # 检查是否为未来数据
        if req_date > self.current_window.current_date:
            violation = {
                "type": "future_data_access",
                "request_date": request_date,
                "current_date": str(self.current_window.current_date),
                "data_type": data_type,
                "timestamp": datetime.now().isoformat(),
                "severity": "HIGH"
            }
            
            self.violations.append(violation)
            self.stats["violations_count"] += 1
            
            if self.config["strict_mode"]:
                logger.error(f"🚨 严重违规：访问未来数据 {request_date} > {self.current_window.current_date}")
                return False
            else:
                logger.warning(f"  警告：访问未来数据 {request_date}")
                return True
        
        # 检查是否超出回看范围
        max_lookback = self.current_window.current_date - timedelta(days=self.config["max_lookback_days"])
        if req_date < max_lookback:
            violation = {
                "type": "excessive_lookback",
                "request_date": request_date,
                "max_lookback_date": str(max_lookback),
                "data_type": data_type,
                "timestamp": datetime.now().isoformat(),
                "severity": "MEDIUM"
            }
            
            self.violations.append(violation)
            logger.warning(f"  超出最大回看范围: {request_date} < {max_lookback}")
        
        return True
    
    def get_valid_date_range(self) -> Tuple[str, str]:
        """获取有效的数据日期范围"""
        if not self.current_window:
            # 无时间控制时，返回默认范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=self.config["max_lookback_days"])
            return str(start_date), str(end_date)
        
        # 有时间控制时，返回受限范围
        end_date = self.current_window.current_date
        start_date = max(
            self.current_window.start_date,
            end_date - timedelta(days=self.config["max_lookback_days"])
        )
        
        return str(start_date), str(end_date)
    
    def get_current_time_info(self) -> Dict[str, Any]:
        """获取当前时间信息"""
        if not self.current_window:
            return {
                "mode": "no_control",
                "current_date": str(datetime.now().date()),
                "timezone": self.config["timezone"]
            }
        
        return {
            "mode": self.current_window.mode.value,
            "start_date": str(self.current_window.start_date),
            "end_date": str(self.current_window.end_date),
            "current_date": str(self.current_window.current_date),
            "timezone": self.config["timezone"],
            "strict_mode": self.config["strict_mode"]
        }
    
    def get_violations_report(self) -> Dict[str, Any]:
        """获取违规报告"""
        return {
            "total_violations": len(self.violations),
            "high_severity": len([v for v in self.violations if v["severity"] == "HIGH"]),
            "medium_severity": len([v for v in self.violations if v["severity"] == "MEDIUM"]),
            "recent_violations": self.violations[-10:],  # 最近10个违规
            "stats": self.stats.copy()
        }
    
    def reset_session(self):
        """重置时间会话"""
        self.current_window = None
        self.violations.clear()
        logger.info("时间控制会话已重置")

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息 - 增强版小闭环"""

        # 启动学习管理小闭环
        from backend.core.foundation.loop_manager import loop_manager, LoopType

        small_loop = loop_manager.create_loop(
            loop_type=LoopType.ROLE_INTERNAL,
            loop_name="瑶光星学习管理小闭环",
            initiator="yaoguang_star",
            initial_inputs={"operation": "get_statistics"},
            timeout_seconds=60
        )

        try:
            # 收集统计信息
            stats = {
                "service_name": "TimeControlEngine",
                "version": "1.0.0",
                "current_mode": self.current_window.mode.value if self.current_window else "no_control",
                "total_violations": len(self.violations),
                "high_severity_violations": len([v for v in self.violations if v["severity"] == "HIGH"]),
                "medium_severity_violations": len([v for v in self.violations if v["severity"] == "MEDIUM"]),
                "data_requests": self.stats.get("data_requests", 0),
                "time_advances": self.stats.get("time_advances", 0),
                "learning_effectiveness": self._calculate_learning_effectiveness(),
                "data_quality_score": self._calculate_data_quality_score(),
                "small_loop_status": "healthy"
            }

            # 完成小闭环
            loop_manager.complete_loop(
                small_loop.loop_id,
                success=True,
                final_outputs={
                    "statistics": stats,
                    "violations_analyzed": len(self.violations),
                    "learning_effectiveness": stats["learning_effectiveness"]
                }
            )

            logger.info(f"  瑶光星学习管理小闭环完成: 分析了{len(self.violations)}个违规记录")
            return stats

        except Exception as e:
            logger.error(f"  瑶光星学习管理小闭环失败: {e}")

            # 失败时也要完成小闭环
            loop_manager.complete_loop(
                small_loop.loop_id,
                success=False,
                final_outputs={"error": str(e)}
            )

            # 返回基础统计信息
            return {
                "service_name": "TimeControlEngine",
                "version": "1.0.0",
                "error": str(e),
                "small_loop_status": "error"
            }

    def _calculate_learning_effectiveness(self) -> float:
        """计算学习效果"""
        if not self.stats:
            return 0.0

        # 基于违规率计算学习效果
        total_requests = self.stats.get("data_requests", 0)
        if total_requests == 0:
            return await self._get_real_value()

        violation_rate = len(self.violations) / total_requests
        return max(0.0, 1.0 - violation_rate)

    def _calculate_data_quality_score(self) -> float:
        """计算数据质量分数"""
        if not self.violations:
            return await self._get_real_value()

        # 基于违规严重程度计算质量分数
        high_violations = len([v for v in self.violations if v["severity"] == "HIGH"])
        medium_violations = len([v for v in self.violations if v["severity"] == "MEDIUM"])
        total_violations = len(self.violations)

        if total_violations == 0:
            return await self._get_real_value()

        # 高严重度违规权重更大
        weighted_violations = high_violations * 1.0 + medium_violations * 0.5
        quality_score = max(0.0, 1.0 - (weighted_violations / (total_violations * 10)))

        return min(1.0, quality_score)

    async def close(self):
        """关闭时间控制引擎"""
        self.reset_session()
        logger.info("时间控制引擎已关闭")

# 全局时间控制引擎实例
time_control_engine = TimeControlEngine()

__all__ = [
    'TimeControlEngine',
    'TimeMode',
    'TimeWindow',
    'time_control_engine'
]
