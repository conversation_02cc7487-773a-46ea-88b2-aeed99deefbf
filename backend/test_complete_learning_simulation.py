#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整学习模拟测试
运行瑶光星学习模式的完整流程，修复所有warning和error
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_complete_learning_simulation():
    """完整学习模拟测试"""
    try:
        logger.info("🚀 开始完整学习模拟测试")
        logger.info("=" * 80)
        
        # 1. 初始化瑶光星统一系统
        logger.info("🌟 步骤1: 初始化瑶光星统一系统...")
        from roles.yaoguang_star.core.unified_yaoguang_system import unified_yaoguang_system
        
        init_result = await unified_yaoguang_system.initialize_system()
        if not init_result.get("success"):
            logger.error(f"❌ 瑶光星统一系统初始化失败: {init_result.get('error')}")
            return False
        
        logger.info("✅ 瑶光星统一系统初始化成功")
        
        # 2. 启动学习会话
        logger.info("📚 步骤2: 启动学习会话...")
        session_config = {
            "stocks_per_session": 1,
            "data_years": 1,
            "strategy_testing_enabled": True,
            "learning_mode": "comprehensive",
            "session_duration_minutes": 5  # 短时间测试
        }
        
        session_result = await unified_yaoguang_system.start_learning_session(session_config)
        if not session_result.get("success"):
            logger.error(f"❌ 学习会话启动失败: {session_result.get('error')}")
            return False
        
        session_id = session_result["session_id"]
        logger.info(f"✅ 学习会话启动成功: {session_id}")
        
        # 3. 等待学习流程运行
        logger.info("⏳ 步骤3: 等待学习流程运行...")
        await asyncio.sleep(10)  # 等待10秒让学习流程开始
        
        # 4. 检查学习状态
        logger.info("📊 步骤4: 检查学习状态...")
        status = await unified_yaoguang_system.get_learning_status()
        logger.info(f"学习状态: {status}")
        
        # 5. 测试RD-Agent因子生成
        logger.info("🔬 步骤5: 测试RD-Agent因子生成...")
        from roles.yaoguang_star.services.rd_agent_integration_service import rd_agent_integration_service
        
        factor_config = {
            "target_stocks": ["000001.XSHE"],
            "factor_count": 3,
            "research_type": "factor_generation"
        }
        
        factors = await rd_agent_integration_service.generate_new_factors(factor_config)
        if factors and len(factors) > 0:
            logger.info(f"✅ RD-Agent因子生成成功: {len(factors)} 个因子")
            for i, factor in enumerate(factors[:2]):
                logger.info(f"  因子 {i+1}: {factor.get('factor_name', 'Unknown')}")
        else:
            logger.error("❌ RD-Agent因子生成失败")
        
        # 6. 测试多角色协作
        logger.info("⭐ 步骤6: 测试多角色协作...")
        
        # 测试天枢星新闻收集
        try:
            from roles.tianshu_star.services.tianshu_automation_system import tianshu_automation_system
            news_context = {
                "stock_code": "000001.XSHE",
                "task_type": "news_collection",
                "session_id": session_id
            }
            news_result = await tianshu_automation_system.execute_market_analysis(news_context)
            if news_result.get("success"):
                logger.info("✅ 天枢星新闻收集正常")
            else:
                logger.warning("⚠️ 天枢星新闻收集异常")
        except Exception as e:
            logger.warning(f"⚠️ 天枢星测试失败: {e}")
        
        # 测试天玑星风险分析
        try:
            from roles.tianji_star.services.tianji_automation_system import tianji_automation_system
            risk_context = {
                "stock_code": "000001.XSHE",
                "task_type": "risk_analysis",
                "session_id": session_id
            }
            risk_result = await tianji_automation_system.execute_risk_analysis(risk_context)
            if risk_result.get("success"):
                logger.info("✅ 天玑星风险分析正常")
            else:
                logger.warning("⚠️ 天玑星风险分析异常")
        except Exception as e:
            logger.warning(f"⚠️ 天玑星测试失败: {e}")
        
        # 测试天璇星技术分析
        try:
            from roles.tianxuan_star.services.tianxuan_automation_system import tianxuan_automation_system
            tech_context = {
                "stock_code": "000001.XSHE",
                "task_type": "technical_analysis",
                "session_id": session_id
            }
            tech_result = await tianxuan_automation_system.execute_technical_analysis(tech_context)
            if tech_result.get("success"):
                logger.info("✅ 天璇星技术分析正常")
            else:
                logger.warning("⚠️ 天璇星技术分析异常")
        except Exception as e:
            logger.warning(f"⚠️ 天璇星测试失败: {e}")
        
        # 测试玉衡星交易执行
        try:
            from roles.yuheng_star.services.yuheng_automation_system import yuheng_automation_system
            trade_context = {
                "stock_code": "000001.XSHE",
                "task_type": "trading_automation",
                "session_id": session_id,
                "trading_decision": {
                    "action": "buy",
                    "quantity": 1000,
                    "price": 10.0
                },
                "mode": "learning"
            }
            trade_result = await yuheng_automation_system.execute_trading_automation(trade_context)
            if trade_result.get("success"):
                logger.info("✅ 玉衡星交易执行正常")
            else:
                logger.warning("⚠️ 玉衡星交易执行异常")
        except Exception as e:
            logger.warning(f"⚠️ 玉衡星测试失败: {e}")
        
        # 7. 测试四星辩论系统
        logger.info("🗣️ 步骤7: 测试四星辩论系统...")
        try:
            from roles.tianquan_star.services.enhanced_four_stars_debate import enhanced_four_stars_debate
            
            debate_context = {
                "stock_code": "000001.XSHE",
                "market_data": {"current_price": 10.0, "volume": 1000000},
                "news_analysis": {"sentiment": "neutral", "impact": "low"},
                "risk_analysis": {"risk_level": "medium", "risk_score": 0.5},
                "technical_analysis": {"trend": "sideways", "signals": ["neutral"]},
                "session_id": session_id
            }
            
            debate_result = await enhanced_four_stars_debate.conduct_comprehensive_debate(debate_context)
            if debate_result.get("success"):
                logger.info("✅ 四星辩论系统正常")
                logger.info(f"  辩论结论: {debate_result.get('final_decision', {}).get('action', 'Unknown')}")
            else:
                logger.warning("⚠️ 四星辩论系统异常")
        except Exception as e:
            logger.warning(f"⚠️ 四星辩论系统测试失败: {e}")
        
        # 8. 等待更多学习流程
        logger.info("⏳ 步骤8: 等待更多学习流程...")
        await asyncio.sleep(15)  # 再等待15秒
        
        # 9. 停止学习会话
        logger.info("🛑 步骤9: 停止学习会话...")
        stop_result = await unified_yaoguang_system.stop_current_session()
        if stop_result.get("success"):
            logger.info("✅ 学习会话停止成功")
        else:
            logger.warning(f"⚠️ 学习会话停止失败: {stop_result.get('error')}")
        
        # 10. 获取学习总结
        logger.info("📋 步骤10: 获取学习总结...")
        try:
            summary = await unified_yaoguang_system.get_session_summary(session_id)
            if summary:
                logger.info("✅ 学习总结获取成功")
                logger.info(f"  会话时长: {summary.get('duration', 'Unknown')}")
                logger.info(f"  处理股票: {summary.get('processed_stocks', 'Unknown')}")
                logger.info(f"  生成因子: {summary.get('generated_factors', 'Unknown')}")
            else:
                logger.warning("⚠️ 学习总结获取失败")
        except Exception as e:
            logger.warning(f"⚠️ 学习总结获取失败: {e}")
        
        logger.info("=" * 80)
        logger.info("🎉 完整学习模拟测试完成！")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 完整学习模拟测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始完整学习模拟测试")
    
    success = await test_complete_learning_simulation()
    
    if success:
        logger.info("🎉 完整学习模拟测试成功！")
        logger.info("💡 系统已准备好进行真实的学习模式运行")
    else:
        logger.info("❌ 完整学习模拟测试失败")
        logger.info("💡 需要进一步检查和修复问题")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
