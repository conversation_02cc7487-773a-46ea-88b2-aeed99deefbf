#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开阳星股票检测员 - 核心控制器

基于RD-Agent深度集成优化方案的开阳星重新实现
专注于全市场扫描、智能筛选和机会发现
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..services.market_scanner_engine import MarketScannerEngine
from ..services.intelligent_screening_system import IntelligentScreeningSystem
from ..services.news_impact_analyzer import NewsImpactAnalyzer
from ..services.theme_discovery_engine import ThemeDiscoveryEngine
from ..services.opportunity_push_engine import OpportunityPushEngine
from ..services.rd_agent_factor_generator import RDAgentFactorGenerator

# 导入基础角色类
from ...base_role_enhanced import RoleEnhancementMixin

logger = logging.getLogger(__name__)


class DetectorStatus(Enum):
    """检测器状态"""
    INITIALIZING = "initializing"
    SCANNING = "scanning"
    ANALYZING = "analyzing"
    IDLE = "idle"
    ERROR = "error"


@dataclass
class StockScanResult:
    """股票扫描结果"""
    stock_code: str
    scan_time: datetime
    technical_score: float
    fundamental_score: float
    news_score: float
    rd_agent_score: float
    comprehensive_score: float
    recommendation_level: str
    scan_details: Dict[str, Any]


@dataclass
class MarketOpportunity:
    """市场机会"""
    opportunity_id: str
    stock_code: str
    opportunity_type: str
    confidence_score: float
    expected_return: float
    risk_level: str
    time_horizon: str
    discovery_time: datetime
    supporting_factors: List[str]


class KaiyangStockDetector(RoleEnhancementMixin):
    """开阳星股票检测员 - 核心控制器"""
    
    def __init__(self):
        """初始化开阳星股票检测员"""
        super().__init__()
        
        # 核心服务组件
        self.market_scanner = MarketScannerEngine()
        self.screening_system = IntelligentScreeningSystem()
        self.news_analyzer = NewsImpactAnalyzer()
        self.theme_engine = ThemeDiscoveryEngine()
        self.push_engine = OpportunityPushEngine()
        self.rd_agent_generator = RDAgentFactorGenerator()
        
        # 检测器状态
        self.status = DetectorStatus.INITIALIZING
        self.last_scan_time = None
        self.scan_results = []
        self.discovered_opportunities = []
        
        # 扫描配置
        self.scan_config = {
            "scan_interval_minutes": 30,    # 扫描间隔30分钟
            "batch_size": 100,              # 每批处理100只股票
            "min_score_threshold": 0.6,     # 最低评分阈值
            "max_opportunities": 50,        # 最大机会数量
            "enable_rd_agent": True         # 启用RD-Agent
        }
        
        # 性能统计
        self.performance_stats = {
            "total_scans_completed": 0,
            "total_stocks_scanned": 0,
            "total_opportunities_found": 0,
            "avg_scan_duration": 0.0,
            "success_rate": 0.0
        }
        
        logger.info("🔍 开阳星股票检测员初始化开始")
    
    async def initialize(self):
        """初始化股票检测员"""
        
        try:
            # 1. 初始化角色增强功能
            await self.initialize_role_enhancement(
                role_id="kaiyang_stock_detector",
                role_type="stock_detection_center"
            )
            
            # 2. 初始化核心服务
            await self.market_scanner.initialize()
            await self.screening_system.initialize()
            await self.news_analyzer.initialize()
            await self.theme_engine.initialize()
            await self.push_engine.initialize()
            await self.rd_agent_generator.initialize()
            
            # 3. 启动定时扫描
            asyncio.create_task(self._start_periodic_scanning())
            
            # 4. 更新状态
            self.status = DetectorStatus.IDLE
            
            logger.info("  开阳星股票检测员初始化完成")
            logger.info("  核心功能: 全市场扫描、智能筛选、机会发现")
            
            return {
                "success": True,
                "status": self.status.value,
                "services_initialized": 6,
                "scan_config": self.scan_config
            }
            
        except Exception as e:
            self.status = DetectorStatus.ERROR
            logger.error(f"  开阳星股票检测员初始化失败: {e}")
            raise
    
    async def execute_full_market_scan(self) -> Dict[str, Any]:
        """执行全市场扫描"""
        
        scan_start_time = datetime.now()
        self.status = DetectorStatus.SCANNING
        
        try:
            logger.info("🔍 开始全市场扫描...")
            
            # 1. 执行市场扫描
            market_scan_result = await self.market_scanner.scan_entire_market()
            
            # 2. 智能筛选
            self.status = DetectorStatus.ANALYZING
            qualified_stocks = await self.screening_system.intelligent_screening(
                market_scan_result.scan_results,
                {"min_score": self.scan_config["min_score_threshold"]}
            )
            
            # 3. 新闻影响分析
            news_enhanced_stocks = []
            for stock in qualified_stocks[:20]:  # 限制前20只股票
                news_impact = await self.news_analyzer.analyze_news_impact(
                    stock.stock_code
                )
                stock.news_impact = news_impact
                news_enhanced_stocks.append(stock)
            
            # 4. 热点题材识别
            hot_themes = await self.theme_engine.discover_hot_themes(
                market_scan_result.scan_results
            )
            
            # 5. RD-Agent因子生成
            if self.scan_config["enable_rd_agent"]:
                rd_agent_factors = await self.rd_agent_generator.generate_market_factors(
                    market_scan_result.scan_results
                )
            else:
                rd_agent_factors = []
            
            # 6. 机会识别
            opportunities = await self._identify_opportunities(
                news_enhanced_stocks, hot_themes, rd_agent_factors
            )
            
            # 7. 推送机会到天权星
            push_result = await self.push_engine.push_opportunities_to_tianquan(
                news_enhanced_stocks, hot_themes
            )
            
            # 8. 更新统计信息
            scan_duration = (datetime.now() - scan_start_time).total_seconds()
            await self._update_scan_statistics(
                market_scan_result, qualified_stocks, opportunities, scan_duration
            )
            
            # 9. 保存扫描结果
            self.last_scan_time = datetime.now()
            self.scan_results = market_scan_result.scan_results
            self.discovered_opportunities = opportunities
            
            self.status = DetectorStatus.IDLE
            
            logger.info(f"  全市场扫描完成，发现 {len(opportunities)} 个机会")
            
            return {
                "success": True,
                "scan_summary": {
                    "total_stocks_scanned": market_scan_result.total_stocks_scanned,
                    "qualified_stocks": len(qualified_stocks),
                    "hot_themes": len(hot_themes),
                    "opportunities_found": len(opportunities),
                    "scan_duration": scan_duration,
                    "scan_time": scan_start_time.isoformat()
                },
                "top_opportunities": opportunities[:10],  # 返回前10个机会
                "hot_themes": hot_themes[:5],            # 返回前5个热点题材
                "push_result": push_result
            }
            
        except Exception as e:
            self.status = DetectorStatus.ERROR
            logger.error(f"  全市场扫描失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "scan_time": scan_start_time.isoformat()
            }
    
    async def scan_specific_stocks(self, stock_codes: List[str]) -> Dict[str, Any]:
        """扫描指定股票"""
        
        try:
            logger.info(f"🔍 扫描指定股票: {len(stock_codes)} 只")
            
            # 1. 批量扫描指定股票
            scan_results = await self.market_scanner.scan_stock_batch(stock_codes)
            
            # 2. 智能评分
            scored_results = []
            for result in scan_results:
                enhanced_result = await self.screening_system.calculate_comprehensive_score(
                    result
                )
                scored_results.append(enhanced_result)
            
            # 3. 排序和筛选
            qualified_results = [
                result for result in scored_results 
                if result.comprehensive_score >= self.scan_config["min_score_threshold"]
            ]
            qualified_results.sort(key=lambda x: x.comprehensive_score, reverse=True)
            
            return {
                "success": True,
                "scan_results": qualified_results,
                "total_scanned": len(stock_codes),
                "qualified_count": len(qualified_results),
                "scan_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"  扫描指定股票失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_market_opportunities(self, limit: int = 20) -> Dict[str, Any]:
        """获取市场机会"""
        
        try:
            # 返回最新发现的机会
            recent_opportunities = sorted(
                self.discovered_opportunities,
                key=lambda x: x.discovery_time,
                reverse=True
            )[:limit]
            
            return {
                "success": True,
                "opportunities": [
                    {
                        "opportunity_id": opp.opportunity_id,
                        "stock_code": opp.stock_code,
                        "opportunity_type": opp.opportunity_type,
                        "confidence_score": opp.confidence_score,
                        "expected_return": opp.expected_return,
                        "risk_level": opp.risk_level,
                        "time_horizon": opp.time_horizon,
                        "discovery_time": opp.discovery_time.isoformat(),
                        "supporting_factors": opp.supporting_factors
                    }
                    for opp in recent_opportunities
                ],
                "total_opportunities": len(self.discovered_opportunities),
                "last_scan_time": self.last_scan_time.isoformat() if self.last_scan_time else None
            }
            
        except Exception as e:
            logger.error(f"  获取市场机会失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_detector_status(self) -> Dict[str, Any]:
        """获取检测器状态"""
        
        try:
            # 1. 基础状态
            status_info = {
                "status": self.status.value,
                "last_scan_time": self.last_scan_time.isoformat() if self.last_scan_time else None,
                "active_opportunities": len(self.discovered_opportunities),
                "scan_config": self.scan_config,
                "performance_stats": self.performance_stats
            }
            
            # 2. 服务状态
            service_status = {
                "market_scanner": await self.market_scanner.get_status(),
                "screening_system": await self.screening_system.get_status(),
                "news_analyzer": await self.news_analyzer.get_status(),
                "theme_engine": await self.theme_engine.get_status(),
                "rd_agent_generator": await self.rd_agent_generator.get_status()
            }
            
            # 3. 增强状态
            enhancement_status = self.get_enhancement_status()
            
            return {
                "success": True,
                "detector": status_info,
                "services": service_status,
                "enhancement": enhancement_status
            }
            
        except Exception as e:
            logger.error(f"  获取检测器状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _start_periodic_scanning(self):
        """启动定期扫描"""
        
        while True:
            try:
                await asyncio.sleep(self.scan_config["scan_interval_minutes"] * 60)
                
                if self.status == DetectorStatus.IDLE:
                    logger.info("⏰ 启动定期全市场扫描")
                    await self.execute_full_market_scan()
                
            except Exception as e:
                logger.error(f"  定期扫描出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再继续
    
    async def _identify_opportunities(self, qualified_stocks, hot_themes, rd_agent_factors) -> List[MarketOpportunity]:
        """识别市场机会"""
        
        opportunities = []
        
        # 基于高评分股票识别机会
        for stock in qualified_stocks[:self.scan_config["max_opportunities"]]:
            opportunity = MarketOpportunity(
                opportunity_id=f"opp_{stock.stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                stock_code=stock.stock_code,
                opportunity_type="high_score_stock",
                confidence_score=stock.comprehensive_score,
                expected_return=stock.comprehensive_score * 0.1,  # 简化计算
                risk_level="medium",
                time_horizon="short_term",
                discovery_time=datetime.now(),
                supporting_factors=[
                    f"综合评分: {stock.comprehensive_score:.2f}",
                    f"技术面评分: {stock.technical_score:.2f}",
                    f"基本面评分: {stock.fundamental_score:.2f}"
                ]
            )
            opportunities.append(opportunity)
        
        return opportunities
    
    async def _update_scan_statistics(self, market_scan_result, qualified_stocks, opportunities, scan_duration):
        """更新扫描统计信息"""
        
        self.performance_stats["total_scans_completed"] += 1
        self.performance_stats["total_stocks_scanned"] += market_scan_result.total_stocks_scanned
        self.performance_stats["total_opportunities_found"] += len(opportunities)
        
        # 更新平均扫描时长
        total_scans = self.performance_stats["total_scans_completed"]
        current_avg = self.performance_stats["avg_scan_duration"]
        self.performance_stats["avg_scan_duration"] = (
            (current_avg * (total_scans - 1) + scan_duration) / total_scans
        )
        
        # 更新成功率
        self.performance_stats["success_rate"] = min(1.0, len(qualified_stocks) / max(1, market_scan_result.total_stocks_scanned))


# 全局开阳星股票检测员实例
kaiyang_stock_detector = KaiyangStockDetector()
