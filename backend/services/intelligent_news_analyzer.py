#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能新闻分析器
实现新闻 → 板块 → 股票 → 预测 → 验证 → 学习的完整链路
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio

logger = logging.getLogger(__name__)

class IntelligentNewsAnalyzer:
    """智能新闻分析器"""
    
    def __init__(self):
        self.sector_keywords = {
            "农业": ["农业", "种植", "养殖", "农产品", "粮食", "种子", "化肥", "农机", "土地", "农村"],
            "科技": ["科技", "人工智能", "芯片", "半导体", "软件", "互联网", "5G", "云计算", "大数据"],
            "新能源": ["新能源", "光伏", "风电", "电池", "储能", "充电桩", "氢能", "核电"],
            "医药": ["医药", "医疗", "生物", "疫苗", "药品", "医院", "医疗器械", "中药"],
            "金融": ["银行", "保险", "证券", "基金", "金融", "支付", "信贷", "理财"],
            "房地产": ["房地产", "地产", "住房", "建筑", "装修", "物业", "土地"],
            "消费": ["消费", "零售", "商超", "电商", "品牌", "食品", "饮料", "服装"],
            "汽车": ["汽车", "新能源车", "电动车", "汽车零部件", "车联网"],
            "军工": ["军工", "国防", "航空", "航天", "军用", "武器"],
            "环保": ["环保", "污水处理", "垃圾处理", "节能", "减排", "绿色"]
        }
        
        self.region_keywords = {
            "东北": ["东北", "黑龙江", "吉林", "辽宁", "哈尔滨", "长春", "沈阳", "大连"],
            "华北": ["北京", "天津", "河北", "山西", "内蒙古"],
            "华东": ["上海", "江苏", "浙江", "安徽", "福建", "江西", "山东"],
            "华南": ["广东", "广西", "海南", "深圳", "广州"],
            "华中": ["河南", "湖北", "湖南", "武汉", "郑州", "长沙"],
            "西南": ["重庆", "四川", "贵州", "云南", "西藏", "成都"],
            "西北": ["陕西", "甘肃", "青海", "宁夏", "新疆", "西安"]
        }
        
        self.policy_keywords = {
            "资金支持": ["亿", "万亿", "资金", "投资", "补贴", "扶持", "支持"],
            "政策利好": ["政策", "规划", "方案", "意见", "通知", "办法"],
            "税收优惠": ["税收", "减税", "免税", "优惠", "退税"],
            "准入放宽": ["准入", "放宽", "开放", "允许", "批准"],
            "监管加强": ["监管", "整顿", "规范", "限制", "禁止"]
        }
        
        # 东北地区农业相关股票池
        self.northeast_agriculture_stocks = {
            "600598": {"name": "北大荒", "region": "黑龙江", "business": "农业种植", "market_cap": "大盘"},
            "002041": {"name": "登海种业", "region": "山东", "business": "种子", "market_cap": "中盘"},
            "000876": {"name": "新希望", "region": "四川", "business": "农牧", "market_cap": "大盘"},
            "002714": {"name": "牧原股份", "region": "河南", "business": "养殖", "market_cap": "大盘"},
            "000061": {"name": "农产品", "region": "深圳", "business": "农产品流通", "market_cap": "中盘"},
            "300498": {"name": "温氏股份", "region": "广东", "business": "养殖", "market_cap": "大盘"},
            "002157": {"name": "正邦科技", "region": "江西", "business": "农牧", "market_cap": "中盘"},
            "600313": {"name": "农发种业", "region": "北京", "business": "种子", "market_cap": "中盘"}
        }
    
    async def analyze_news_comprehensive(self, news_content: str, news_title: str = "") -> Dict[str, Any]:
        """综合分析新闻内容"""
        
        logger.info(f"🔍 开始智能分析新闻: {news_title[:50]}...")
        
        analysis_result = {
            "news_title": news_title,
            "news_content": news_content[:500] + "..." if len(news_content) > 500 else news_content,
            "analysis_time": datetime.now().isoformat(),
            "sectors_identified": [],
            "regions_identified": [],
            "policy_types": [],
            "impact_assessment": {},
            "recommended_stocks": [],
            "prediction": {},
            "confidence_score": 0.0
        }
        
        # 1. 识别受影响板块
        sectors = self._identify_sectors(news_content + " " + news_title)
        analysis_result["sectors_identified"] = sectors
        
        # 2. 识别受影响地区
        regions = self._identify_regions(news_content + " " + news_title)
        analysis_result["regions_identified"] = regions
        
        # 3. 识别政策类型
        policies = self._identify_policy_types(news_content + " " + news_title)
        analysis_result["policy_types"] = policies
        
        # 4. 评估影响程度
        impact = self._assess_impact_level(news_content + " " + news_title)
        analysis_result["impact_assessment"] = impact
        
        # 5. 筛选推荐股票
        stocks = self._screen_relevant_stocks(sectors, regions, impact)
        analysis_result["recommended_stocks"] = stocks
        
        # 6. 生成预测
        prediction = self._generate_prediction(sectors, regions, policies, impact, stocks)
        analysis_result["prediction"] = prediction
        
        # 7. 计算置信度
        confidence = self._calculate_confidence(sectors, regions, policies, impact, stocks)
        analysis_result["confidence_score"] = confidence
        
        logger.info(f"  新闻分析完成，识别板块: {sectors}, 推荐股票: {len(stocks)}只")
        
        return analysis_result
    
    def _identify_sectors(self, text: str) -> List[Dict[str, Any]]:
        """识别受影响板块"""
        
        identified_sectors = []
        text_lower = text.lower()
        
        for sector, keywords in self.sector_keywords.items():
            matches = []
            for keyword in keywords:
                if keyword in text_lower:
                    matches.append(keyword)
            
            if matches:
                confidence = min(len(matches) * 0.2 + 0.3, 1.0)
                identified_sectors.append({
                    "sector": sector,
                    "matched_keywords": matches,
                    "confidence": confidence
                })
        
        # 按置信度排序
        identified_sectors.sort(key=lambda x: x["confidence"], reverse=True)
        return identified_sectors[:3]  # 返回前3个最相关的板块
    
    def _identify_regions(self, text: str) -> List[Dict[str, Any]]:
        """识别受影响地区"""
        
        identified_regions = []
        text_lower = text.lower()
        
        for region, keywords in self.region_keywords.items():
            matches = []
            for keyword in keywords:
                if keyword in text_lower:
                    matches.append(keyword)
            
            if matches:
                confidence = min(len(matches) * 0.3 + 0.4, 1.0)
                identified_regions.append({
                    "region": region,
                    "matched_keywords": matches,
                    "confidence": confidence
                })
        
        identified_regions.sort(key=lambda x: x["confidence"], reverse=True)
        return identified_regions[:2]  # 返回前2个最相关的地区
    
    def _identify_policy_types(self, text: str) -> List[Dict[str, Any]]:
        """识别政策类型"""
        
        identified_policies = []
        text_lower = text.lower()
        
        for policy_type, keywords in self.policy_keywords.items():
            matches = []
            for keyword in keywords:
                if keyword in text_lower:
                    matches.append(keyword)
            
            if matches:
                confidence = min(len(matches) * 0.25 + 0.3, 1.0)
                identified_policies.append({
                    "policy_type": policy_type,
                    "matched_keywords": matches,
                    "confidence": confidence
                })
        
        identified_policies.sort(key=lambda x: x["confidence"], reverse=True)
        return identified_policies
    
    def _assess_impact_level(self, text: str) -> Dict[str, Any]:
        """评估影响程度"""
        
        # 提取资金规模
        money_pattern = r'(\d+(?:\.\d+)?)\s*([万亿千百]?亿|万)'
        money_matches = re.findall(money_pattern, text)
        
        total_amount = 0
        for amount, unit in money_matches:
            amount = float(amount)
            if '万亿' in unit:
                total_amount += amount * 10000
            elif '千亿' in unit:
                total_amount += amount * 1000
            elif '百亿' in unit:
                total_amount += amount * 100
            elif '亿' in unit:
                total_amount += amount
            elif '万' in unit:
                total_amount += amount * 0.0001
        
        # 评估影响级别
        if total_amount >= 1000:
            impact_level = "重大利好"
            impact_score = 0.9
        elif total_amount >= 100:
            impact_level = "显著利好"
            impact_score = 0.7
        elif total_amount >= 10:
            impact_level = "一般利好"
            impact_score = 0.5
        else:
            impact_level = "轻微利好"
            impact_score = 0.3
        
        return {
            "funding_amount": total_amount,
            "impact_level": impact_level,
            "impact_score": impact_score,
            "time_horizon": "1-3个月" if total_amount >= 100 else "1-6个月"
        }
    
    def _screen_relevant_stocks(self, sectors: List[Dict], regions: List[Dict], impact: Dict) -> List[Dict[str, Any]]:
        """筛选相关股票"""
        
        recommended_stocks = []
        
        # 如果是农业板块且涉及东北地区
        agriculture_identified = any(s["sector"] == "农业" for s in sectors)
        northeast_identified = any(r["region"] == "东北" for r in regions)
        
        if agriculture_identified:
            for code, info in self.northeast_agriculture_stocks.items():
                relevance_score = 0.0
                reasons = []
                
                # 业务相关性
                if "农业" in info["business"] or "种" in info["business"]:
                    relevance_score += 0.4
                    reasons.append("主营业务高度相关")
                
                # 地区相关性
                if northeast_identified and info["region"] in ["黑龙江", "吉林", "辽宁"]:
                    relevance_score += 0.3
                    reasons.append("东北地区企业")
                elif northeast_identified:
                    relevance_score += 0.1
                    reasons.append("可能受益于政策辐射")
                
                # 市值适中性
                if info["market_cap"] in ["中盘", "大盘"]:
                    relevance_score += 0.2
                    reasons.append("市值规模适中")
                
                # 政策匹配度
                if impact["impact_score"] >= 0.7:
                    relevance_score += 0.1
                    reasons.append("政策力度较大")
                
                if relevance_score >= 0.4:  # 相关性阈值
                    recommended_stocks.append({
                        "code": code,
                        "name": info["name"],
                        "region": info["region"],
                        "business": info["business"],
                        "relevance_score": round(relevance_score, 2),
                        "reasons": reasons
                    })
        
        # 按相关性排序
        recommended_stocks.sort(key=lambda x: x["relevance_score"], reverse=True)
        return recommended_stocks[:5]  # 返回前5只最相关的股票
    
    def _generate_prediction(self, sectors: List[Dict], regions: List[Dict], 
                           policies: List[Dict], impact: Dict, stocks: List[Dict]) -> Dict[str, Any]:
        """生成预测结果"""
        
        if not stocks:
            return {"prediction_type": "无明确预测", "reason": "未找到相关股票"}
        
        # 基于影响程度预测涨幅
        impact_score = impact.get("impact_score", 0.3)
        
        if impact_score >= 0.8:
            expected_return = "15-30%"
            time_horizon = "1-2个月"
        elif impact_score >= 0.6:
            expected_return = "8-20%"
            time_horizon = "1-3个月"
        elif impact_score >= 0.4:
            expected_return = "5-15%"
            time_horizon = "2-6个月"
        else:
            expected_return = "0-10%"
            time_horizon = "3-12个月"
        
        return {
            "prediction_type": "股价上涨",
            "expected_return": expected_return,
            "time_horizon": time_horizon,
            "confidence_level": "高" if impact_score >= 0.7 else "中" if impact_score >= 0.5 else "低",
            "key_factors": [
                f"政策影响: {impact.get('impact_level', '一般')}",
                f"资金规模: {impact.get('funding_amount', 0)}亿",
                f"相关股票: {len(stocks)}只"
            ]
        }
    
    def _calculate_confidence(self, sectors: List[Dict], regions: List[Dict], 
                            policies: List[Dict], impact: Dict, stocks: List[Dict]) -> float:
        """计算整体置信度"""
        
        confidence = 0.0
        
        # 板块识别置信度
        if sectors:
            confidence += max(s["confidence"] for s in sectors) * 0.3
        
        # 地区识别置信度
        if regions:
            confidence += max(r["confidence"] for r in regions) * 0.2
        
        # 政策识别置信度
        if policies:
            confidence += max(p["confidence"] for p in policies) * 0.2
        
        # 影响评估置信度
        confidence += impact.get("impact_score", 0.3) * 0.2
        
        # 股票筛选置信度
        if stocks:
            confidence += max(s["relevance_score"] for s in stocks) * 0.1
        
        return round(min(confidence, 1.0), 2)

    async def analyze_news_impact(self, news_list: List[Dict[str, Any]], symbol: str, stock_name: str) -> Dict[str, Any]:
        """分析新闻对股票的影响"""
        try:
            if not news_list:
                return {
                    "success": False,
                    "error": "没有新闻数据",
                    "impact_analysis": {}
                }

            # 综合分析所有新闻
            total_impact_score = 0.0
            positive_news = []
            negative_news = []
            neutral_news = []

            for news in news_list:
                title = news.get("title", "")
                content = news.get("content", "")
                full_text = f"{title} {content}"

                # 分析单条新闻的影响
                news_analysis = await self.analyze_news_comprehensive(full_text, title)

                # 计算影响分数
                sentiment_score = news_analysis.get("sentiment_analysis", {}).get("sentiment_score", 0.5)
                relevance_score = news.get("relevance_score", 0.5)
                authority_score = news.get("authority_score", 0.7)

                # 综合影响分数
                impact_score = (sentiment_score - 0.5) * relevance_score * authority_score
                total_impact_score += impact_score

                # 分类新闻
                news_with_impact = {
                    **news,
                    "impact_score": impact_score,
                    "sentiment_analysis": news_analysis.get("sentiment_analysis", {}),
                    "sector_analysis": news_analysis.get("sector_analysis", {}),
                    "event_analysis": news_analysis.get("event_analysis", {})
                }

                if impact_score > 0.1:
                    positive_news.append(news_with_impact)
                elif impact_score < -0.1:
                    negative_news.append(news_with_impact)
                else:
                    neutral_news.append(news_with_impact)

            # 计算平均影响
            avg_impact = total_impact_score / len(news_list) if news_list else 0.0

            # 生成影响评估
            impact_level = "neutral"
            if avg_impact > 0.2:
                impact_level = "very_positive"
            elif avg_impact > 0.1:
                impact_level = "positive"
            elif avg_impact < -0.2:
                impact_level = "very_negative"
            elif avg_impact < -0.1:
                impact_level = "negative"

            # 生成建议
            recommendations = []
            if len(positive_news) > len(negative_news):
                recommendations.append("关注正面消息带来的投资机会")
            elif len(negative_news) > len(positive_news):
                recommendations.append("注意负面消息的风险影响")

            if abs(avg_impact) > 0.15:
                recommendations.append("建议密切关注后续新闻动态")

            return {
                "success": True,
                "symbol": symbol,
                "stock_name": stock_name,
                "impact_analysis": {
                    "total_news_count": len(news_list),
                    "avg_impact_score": round(avg_impact, 4),
                    "impact_level": impact_level,
                    "positive_news_count": len(positive_news),
                    "negative_news_count": len(negative_news),
                    "neutral_news_count": len(neutral_news),
                    "positive_news": positive_news[:3],  # 最多返回3条
                    "negative_news": negative_news[:3],  # 最多返回3条
                    "recommendations": recommendations,
                    "analysis_time": datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"新闻影响分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "impact_analysis": {}
            }

# 使用示例
async def test_news_analyzer():
    """测试新闻分析器"""
    
    analyzer = IntelligentNewsAnalyzer()
    
    # 测试新闻
    test_news = "国家准备给东北振兴提供200亿扶持农业，重点支持黑龙江、吉林、辽宁三省的现代农业发展，包括种植业、养殖业和农产品加工业。"
    
    result = await analyzer.analyze_news_comprehensive(test_news, "东北农业振兴200亿政策")
    
    print("🔍 新闻分析结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    asyncio.run(test_news_analyzer())
