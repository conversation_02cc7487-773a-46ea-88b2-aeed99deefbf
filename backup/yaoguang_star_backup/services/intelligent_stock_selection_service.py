#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能选股优化服务
瑶光星通过大量训练数据为开阳星提供选股经验
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import pandas as pd
import numpy as np
from pathlib import Path
import sqlite3
import json
import schedule
import time

logger = logging.getLogger(__name__)

@dataclass
class StockTrainingSession:
    """股票训练会话"""
    session_id: str
    stock_code: str
    stock_name: str
    training_start_date: str
    training_end_date: str
    data_points: int
    training_results: Dict = field(default_factory=dict)
    key_insights: List[str] = field(default_factory=list)
    performance_metrics: Dict = field(default_factory=dict)
    created_time: datetime = field(default_factory=datetime.now)

@dataclass
class SelectionPattern:
    """选股模式"""
    pattern_id: str
    pattern_name: str
    success_rate: float
    key_indicators: List[str]
    market_conditions: List[str]
    risk_level: str
    confidence_score: float
    sample_stocks: List[str]
    discovered_date: datetime = field(default_factory=datetime.now)

class IntelligentStockSelectionService:
    """智能选股优化服务"""
    
    def __init__(self):
        self.training_sessions = {}
        self.selection_patterns = {}
        self.daily_training_quota = 10  # 交易日10只
        self.holiday_training_quota = 20  # 休市日20只
        self.training_years = 10  # 每只股票10年数据
        
        # 初始化相关服务
        self._init_services()
        
        # 启动定时任务（暂时禁用，避免导入错误）
        # self._setup_scheduled_tasks()
        
        logger.info("智能选股优化服务初始化完成")
    
    def _init_services(self):
        """初始化相关服务"""
        try:
            from .data_management_service import DataManagementService
            from .individual_stock_learning_service import IndividualStockLearningService
            from ..core.historical_data_manager import historical_data_manager
            
            self.data_manager = DataManagementService()
            self.learning_service = IndividualStockLearningService()
            self.historical_data = historical_data_manager
            
        except ImportError as e:
            logger.warning(f"部分服务导入失败: {e}")
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        try:
            # 每天上午9点开始训练
            schedule.every().day.at("09:00").do(self._daily_training_task)
            
            # 每小时检查一次训练进度
            schedule.every().hour.do(self._check_training_progress)
            
            # 每周日生成选股经验报告
            schedule.every().sunday.at("20:00").do(self._generate_weekly_insights)
            
            logger.info("定时任务设置完成")
            
        except Exception as e:
            logger.error(f"定时任务设置失败: {e}")
    
    async def start_daily_training(self) -> Dict[str, Any]:
        """开始每日训练"""
        
        try:
            # 1. 从开阳星获取今日选股列表
            selected_stocks = await self._get_kaiyang_stock_selection()
            
            # 2. 确定训练数量
            is_trading_day = await self._is_trading_day()
            quota = self.daily_training_quota if is_trading_day else self.holiday_training_quota
            
            # 3. 选择训练股票
            training_stocks = selected_stocks[:quota] if len(selected_stocks) >= quota else selected_stocks
            
            # 4. 开始批量训练
            training_results = []
            
            for i, stock_code in enumerate(training_stocks):
                try:
                    logger.info(f"开始训练 {i+1}/{len(training_stocks)}: {stock_code}")
                    
                    # 创建训练会话
                    result = await self._create_stock_training_session(stock_code)
                    
                    if result.get("success"):
                        training_results.append(result)
                        
                        # 分时训练（每小时处理一部分）
                        if i > 0 and i % 5 == 0:  # 每5只股票休息一下
                            await asyncio.sleep(60)  # 休息1分钟
                    
                except Exception as e:
                    logger.error(f"训练股票 {stock_code} 失败: {e}")
                    continue
            
            # 5. 分析训练结果，提取选股经验
            insights = await self._extract_selection_insights(training_results)
            
            # 6. 更新开阳星选股策略
            await self._update_kaiyang_selection_strategy(insights)
            
            return {
                "success": True,
                "training_date": datetime.now().strftime('%Y-%m-%d'),
                "is_trading_day": is_trading_day,
                "quota": quota,
                "trained_stocks": len(training_results),
                "success_rate": len(training_results) / len(training_stocks) if training_stocks else 0,
                "new_insights": len(insights),
                "message": f"每日训练完成，训练了 {len(training_results)} 只股票"
            }
            
        except Exception as e:
            logger.error(f"每日训练失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "training_date": datetime.now().strftime('%Y-%m-%d')
            }
    
    async def _create_stock_training_session(self, stock_code: str) -> Dict[str, Any]:
        """创建股票训练会话"""
        
        try:
            # 1. 获取股票基本信息
            stock_info = await self.data_manager.get_stock_basic_info(stock_code)
            if not stock_info.get("success"):
                return {"success": False, "error": f"股票 {stock_code} 信息获取失败"}
            
            stock_data = stock_info.get("data", {})
            
            # 2. 设定训练时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.training_years * 365)
            
            # 3. 获取历史数据
            df = await self.historical_data.get_historical_data(
                stock_code, "daily", 
                start_date.strftime('%Y-%m-%d'), 
                end_date.strftime('%Y-%m-%d')
            )
            
            if df.empty:
                return {"success": False, "error": f"股票 {stock_code} 历史数据不足"}
            
            # 4. 进行深度分析
            analysis_results = await self._deep_stock_analysis(stock_code, df, stock_data)
            
            # 5. 创建训练会话记录
            session_id = f"training_{stock_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            session = StockTrainingSession(
                session_id=session_id,
                stock_code=stock_code,
                stock_name=stock_data.get("stock_name", "未知"),
                training_start_date=start_date.strftime('%Y-%m-%d'),
                training_end_date=end_date.strftime('%Y-%m-%d'),
                data_points=len(df),
                training_results=analysis_results,
                key_insights=analysis_results.get("key_insights", []),
                performance_metrics=analysis_results.get("performance_metrics", {})
            )
            
            self.training_sessions[session_id] = session
            
            return {
                "success": True,
                "session_id": session_id,
                "stock_code": stock_code,
                "stock_name": stock_data.get("stock_name"),
                "data_points": len(df),
                "analysis_results": analysis_results
            }
            
        except Exception as e:
            logger.error(f"创建训练会话失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _deep_stock_analysis(self, stock_code: str, df: pd.DataFrame, stock_info: Dict) -> Dict[str, Any]:
        """深度股票分析"""
        
        try:
            analysis = {
                "basic_metrics": {},
                "technical_patterns": {},
                "fundamental_insights": {},
                "market_behavior": {},
                "selection_indicators": {},
                "key_insights": [],
                "performance_metrics": {}
            }
            
            # 1. 基础指标分析
            analysis["basic_metrics"] = {
                "total_return": (df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100,
                "volatility": df['close'].pct_change().std() * np.sqrt(252) * 100,
                "max_drawdown": self._calculate_max_drawdown(df['close']),
                "sharpe_ratio": self._calculate_sharpe_ratio(df['close']),
                "trading_days": len(df)
            }
            
            # 2. 技术形态识别
            analysis["technical_patterns"] = await self._identify_technical_patterns(df)
            
            # 3. 基本面洞察
            analysis["fundamental_insights"] = await self._analyze_fundamentals(stock_code, stock_info)
            
            # 4. 市场行为分析
            analysis["market_behavior"] = await self._analyze_market_behavior(df)
            
            # 5. 选股指标评估
            analysis["selection_indicators"] = await self._evaluate_selection_indicators(df, stock_info)
            
            # 6. 提取关键洞察
            analysis["key_insights"] = await self._extract_key_insights(analysis)
            
            # 7. 性能指标
            analysis["performance_metrics"] = {
                "analysis_quality": self._assess_analysis_quality(analysis),
                "data_completeness": len(df) / (self.training_years * 252),  # 假设252个交易日/年
                "insight_count": len(analysis["key_insights"]),
                "pattern_count": len(analysis["technical_patterns"])
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"深度股票分析失败: {e}")
            return {
                "basic_metrics": {},
                "technical_patterns": {},
                "fundamental_insights": {},
                "market_behavior": {},
                "selection_indicators": {},
                "key_insights": [f"分析失败: {str(e)}"],
                "performance_metrics": {"analysis_quality": 0}
            }
    
    def _calculate_max_drawdown(self, prices: pd.Series) -> float:
        """计算最大回撤"""
        try:
            cumulative = (1 + prices.pct_change()).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            return drawdown.min() * 100
        except:
            return 0
    
    def _calculate_sharpe_ratio(self, prices: pd.Series) -> float:
        """计算夏普比率"""
        try:
            returns = prices.pct_change().dropna()
            if len(returns) == 0:
                return 0
            return (returns.mean() * 252) / (returns.std() * np.sqrt(252))
        except:
            return 0
    
    async def _identify_technical_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """识别技术形态"""
        patterns = {}
        
        try:
            # 简化的技术形态识别
            df['ma20'] = df['close'].rolling(20).mean()
            df['ma60'] = df['close'].rolling(60).mean()
            
            # 趋势判断
            current_trend = "上涨" if df['close'].iloc[-1] > df['ma20'].iloc[-1] > df['ma60'].iloc[-1] else \
                           "下跌" if df['close'].iloc[-1] < df['ma20'].iloc[-1] < df['ma60'].iloc[-1] else "震荡"
            
            patterns["current_trend"] = current_trend
            patterns["ma_alignment"] = df['close'].iloc[-1] > df['ma20'].iloc[-1] > df['ma60'].iloc[-1]
            patterns["price_above_ma20"] = df['close'].iloc[-1] > df['ma20'].iloc[-1]
            patterns["volume_trend"] = "放量" if df['volume'].iloc[-5:].mean() > df['volume'].iloc[-20:-5].mean() else "缩量"
            
        except Exception as e:
            logger.error(f"技术形态识别失败: {e}")
            patterns["error"] = str(e)
        
        return patterns
    
    async def _analyze_fundamentals(self, stock_code: str, stock_info: Dict) -> Dict[str, Any]:
        """分析基本面"""
        fundamentals = {}
        
        try:
            # 基于股票信息进行基本面分析
            industry = stock_info.get("industry", "未知")
            market = stock_info.get("market", "未知")
            
            fundamentals["industry"] = industry
            fundamentals["market"] = market
            fundamentals["industry_score"] = self._score_industry(industry)
            fundamentals["market_preference"] = "主板" if "SH" in stock_code else "深市"
            
        except Exception as e:
            logger.error(f"基本面分析失败: {e}")
            fundamentals["error"] = str(e)
        
        return fundamentals
    
    def _score_industry(self, industry: str) -> float:
        """行业评分"""
        # 简化的行业评分逻辑
        hot_industries = ["新能源", "人工智能", "生物医药", "半导体", "新材料"]
        stable_industries = ["银行", "保险", "电力", "石油", "钢铁"]
        
        if any(hot in industry for hot in hot_industries):
            return 0.8
        elif any(stable in industry for stable in stable_industries):
            return 0.6
        else:
            return 0.5

    async def _analyze_market_behavior(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析市场行为"""
        behavior = {}

        try:
            # 价格行为分析
            returns = df['close'].pct_change()

            behavior["avg_daily_return"] = returns.mean() * 100
            behavior["return_volatility"] = returns.std() * 100
            behavior["positive_days_ratio"] = (returns > 0).sum() / len(returns)
            behavior["big_move_days"] = (abs(returns) > 0.05).sum()  # 单日涨跌超过5%的天数

            # 成交量行为
            behavior["avg_volume"] = df['volume'].mean()
            behavior["volume_volatility"] = df['volume'].std() / df['volume'].mean()

            # 价量关系
            price_volume_corr = df['close'].pct_change().corr(df['volume'].pct_change())
            behavior["price_volume_correlation"] = price_volume_corr if not pd.isna(price_volume_corr) else 0

        except Exception as e:
            logger.error(f"市场行为分析失败: {e}")
            behavior["error"] = str(e)

        return behavior

    async def _evaluate_selection_indicators(self, df: pd.DataFrame, stock_info: Dict) -> Dict[str, Any]:
        """评估选股指标"""
        indicators = {}

        try:
            # 计算关键选股指标
            returns = df['close'].pct_change()

            # 1. 收益指标
            indicators["total_return_score"] = min((df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 10, 10)
            indicators["consistency_score"] = max(0, 10 - returns.std() * 100)

            # 2. 风险指标
            max_dd = self._calculate_max_drawdown(df['close'])
            indicators["risk_score"] = max(0, 10 + max_dd / 10)  # 最大回撤越小分数越高

            # 3. 流动性指标
            avg_volume = df['volume'].mean()
            indicators["liquidity_score"] = min(avg_volume / 1000000, 10)  # 日均成交量百万股为满分

            # 4. 趋势指标
            df['ma20'] = df['close'].rolling(20).mean()
            trend_days = (df['close'] > df['ma20']).sum()
            indicators["trend_score"] = (trend_days / len(df)) * 10

            # 5. 综合评分
            weights = {
                "total_return_score": 0.3,
                "consistency_score": 0.2,
                "risk_score": 0.2,
                "liquidity_score": 0.15,
                "trend_score": 0.15
            }

            indicators["overall_score"] = sum(
                indicators.get(key, 0) * weight
                for key, weight in weights.items()
            )

        except Exception as e:
            logger.error(f"选股指标评估失败: {e}")
            indicators["error"] = str(e)
            indicators["overall_score"] = 0

        return indicators

    async def _extract_key_insights(self, analysis: Dict) -> List[str]:
        """提取关键洞察"""
        insights = []

        try:
            # 基于分析结果提取洞察
            basic_metrics = analysis.get("basic_metrics", {})
            technical_patterns = analysis.get("technical_patterns", {})
            selection_indicators = analysis.get("selection_indicators", {})

            # 收益洞察
            total_return = basic_metrics.get("total_return", 0)
            if total_return > 100:
                insights.append(f"长期收益优秀，{self.training_years}年累计收益{total_return:.1f}%")
            elif total_return > 50:
                insights.append(f"长期收益良好，{self.training_years}年累计收益{total_return:.1f}%")

            # 风险洞察
            max_drawdown = basic_metrics.get("max_drawdown", 0)
            if max_drawdown > -20:
                insights.append("风险控制较好，最大回撤控制在20%以内")

            # 趋势洞察
            current_trend = technical_patterns.get("current_trend", "")
            if current_trend == "上涨":
                insights.append("当前处于上涨趋势，技术面较强")

            # 综合评分洞察
            overall_score = selection_indicators.get("overall_score", 0)
            if overall_score >= 8:
                insights.append("综合选股评分优秀，值得重点关注")
            elif overall_score >= 6:
                insights.append("综合选股评分良好，可以考虑配置")

        except Exception as e:
            logger.error(f"提取关键洞察失败: {e}")
            insights.append(f"洞察提取失败: {str(e)}")

        return insights[:5]  # 最多返回5个关键洞察

    def _assess_analysis_quality(self, analysis: Dict) -> float:
        """评估分析质量"""
        try:
            quality_score = 0

            # 检查各部分分析的完整性
            sections = ["basic_metrics", "technical_patterns", "fundamental_insights",
                       "market_behavior", "selection_indicators"]

            for section in sections:
                if section in analysis and analysis[section]:
                    if "error" not in analysis[section]:
                        quality_score += 0.2

            return quality_score

        except:
            return 0

    async def _extract_selection_insights(self, training_results: List[Dict]) -> List[SelectionPattern]:
        """从训练结果中提取选股经验"""

        patterns = []

        try:
            if not training_results:
                return patterns

            # 1. 分析高分股票的共同特征
            high_score_stocks = [
                result for result in training_results
                if result.get("analysis_results", {}).get("selection_indicators", {}).get("overall_score", 0) >= 7
            ]

            if high_score_stocks:
                pattern = await self._identify_high_score_pattern(high_score_stocks)
                if pattern:
                    patterns.append(pattern)

            # 2. 分析行业集中度
            industry_pattern = await self._identify_industry_pattern(training_results)
            if industry_pattern:
                patterns.append(industry_pattern)

            # 3. 分析技术形态模式
            technical_pattern = await self._identify_technical_pattern(training_results)
            if technical_pattern:
                patterns.append(technical_pattern)

            # 4. 分析风险收益模式
            risk_return_pattern = await self._identify_risk_return_pattern(training_results)
            if risk_return_pattern:
                patterns.append(risk_return_pattern)

        except Exception as e:
            logger.error(f"提取选股经验失败: {e}")

        return patterns

    async def _identify_high_score_pattern(self, high_score_stocks: List[Dict]) -> Optional[SelectionPattern]:
        """识别高分股票模式"""
        try:
            if len(high_score_stocks) < 3:  # 至少需要3只股票才能形成模式
                return None

            # 分析共同特征
            common_indicators = []
            success_rate = len(high_score_stocks) / 10  # 假设总共训练10只股票

            # 检查技术指标共性
            trend_up_count = sum(
                1 for stock in high_score_stocks
                if stock.get("analysis_results", {}).get("technical_patterns", {}).get("current_trend") == "上涨"
            )

            if trend_up_count / len(high_score_stocks) >= 0.7:
                common_indicators.append("上涨趋势")

            # 检查流动性共性
            high_liquidity_count = sum(
                1 for stock in high_score_stocks
                if stock.get("analysis_results", {}).get("selection_indicators", {}).get("liquidity_score", 0) >= 5
            )

            if high_liquidity_count / len(high_score_stocks) >= 0.7:
                common_indicators.append("高流动性")

            if common_indicators:
                pattern_id = f"high_score_{datetime.now().strftime('%Y%m%d')}"

                return SelectionPattern(
                    pattern_id=pattern_id,
                    pattern_name="高分股票模式",
                    success_rate=success_rate,
                    key_indicators=common_indicators,
                    market_conditions=["正常市场"],
                    risk_level="中等",
                    confidence_score=min(success_rate * 2, 1.0),
                    sample_stocks=[stock["stock_code"] for stock in high_score_stocks[:5]]
                )

        except Exception as e:
            logger.error(f"识别高分股票模式失败: {e}")

        return None

    async def _get_kaiyang_stock_selection(self) -> List[str]:
        """从开阳星获取选股列表"""
        try:
            # 调用开阳星的选股服务
            from roles.kaiyang_star.services.stock_selection_service import StockSelectionService

            # 创建开阳星选股服务实例
            kaiyang_service = StockSelectionService()

            # 构建学习模式选股上下文
            selection_context = {
                "selection_type": "learning_mode",  # 学习模式
                "target_count": 10,  # 目标选择10只股票
                "market_context": {
                    "sentiment": 0.6,
                    "volatility": "medium",
                    "trend": "neutral"
                },
                "learning_purpose": True,  # 标记为学习目的
                "requester": "瑶光星学习系统"
            }

            # 调用开阳星选股
            selection_result = await kaiyang_service.select_stocks(selection_context)

            if selection_result.get("success"):
                selected_stocks = selection_result.get("selection_result", {}).get("selected_stocks", [])
                stock_codes = []

                for stock in selected_stocks:
                    # 提取股票代码
                    stock_code = stock.get("stock_code") or stock.get("code")
                    if stock_code:
                        stock_codes.append(stock_code)

                logger.info(f"✅ 从开阳星获取到 {len(stock_codes)} 只候选股票: {stock_codes}")

                # 记录开阳星选股结果到学习系统
                await self._record_kaiyang_selection_for_learning(selection_result)

                return stock_codes
            else:
                logger.warning(f"开阳星选股失败: {selection_result.get('error', 'Unknown error')}")
                # 返回备用股票列表
                return self._get_fallback_stock_list()

        except ImportError as e:
            logger.error(f"无法导入开阳星选股服务: {e}")
            return self._get_fallback_stock_list()
        except Exception as e:
            logger.error(f"获取开阳星选股列表失败: {e}")
            return self._get_fallback_stock_list()

    def _get_fallback_stock_list(self) -> List[str]:
        """获取备用股票列表"""
        return [
            "000001.XSHE", "000002.XSHE", "000858.XSHE", "002415.XSHE", "300059.XSHE",
            "600000.XSHG", "600036.XSHG", "600519.XSHG", "600887.XSHG", "601318.XSHG"
        ]

    async def _record_kaiyang_selection_for_learning(self, selection_result: Dict[str, Any]):
        """记录开阳星选股结果用于学习"""
        try:
            # 提取关键信息
            selection_data = selection_result.get("selection_result", {})
            portfolio_suggestion = selection_result.get("portfolio_suggestion", {})
            market_analysis = selection_result.get("market_analysis", {})

            # 构建学习记录
            learning_record = {
                "timestamp": datetime.now().isoformat(),
                "selection_type": selection_data.get("selection_type", "learning_mode"),
                "total_candidates": selection_data.get("total_candidates", 0),
                "selection_count": selection_data.get("selection_count", 0),
                "selected_stocks": selection_data.get("selected_stocks", []),
                "portfolio_suggestion": portfolio_suggestion,
                "market_analysis": market_analysis,
                "learning_purpose": "瑶光星学习模式数据收集"
            }

            # 保存到学习历史
            if not hasattr(self, 'kaiyang_selection_history'):
                self.kaiyang_selection_history = []

            self.kaiyang_selection_history.append(learning_record)

            # 限制历史记录数量
            if len(self.kaiyang_selection_history) > 100:
                self.kaiyang_selection_history = self.kaiyang_selection_history[-100:]

            logger.info(f"📝 已记录开阳星选股结果用于学习，历史记录数: {len(self.kaiyang_selection_history)}")

        except Exception as e:
            logger.error(f"记录开阳星选股结果失败: {e}")

    async def _is_trading_day(self) -> bool:
        """判断是否为交易日"""
        try:
            today = datetime.now()
            # 简化判断：周一到周五为交易日
            return today.weekday() < 5
        except:
            return True

    async def _update_kaiyang_selection_strategy(self, insights: List[SelectionPattern]):
        """更新开阳星选股策略"""
        try:
            if not insights:
                logger.info("没有新的选股洞察，跳过策略更新")
                return

            # 保存选股模式
            for pattern in insights:
                self.selection_patterns[pattern.pattern_id] = pattern

            # 真正调用开阳星的策略更新接口
            await self._send_learning_insights_to_kaiyang(insights)

            # 生成策略更新报告
            report = {
                "update_time": datetime.now().isoformat(),
                "new_patterns": len(insights),
                "total_patterns": len(self.selection_patterns),
                "pattern_details": [
                    {
                        "pattern_name": p.pattern_name,
                        "success_rate": p.success_rate,
                        "confidence_score": p.confidence_score,
                        "key_indicators": p.key_indicators
                    }
                    for p in insights
                ]
            }

            logger.info(f"✅ 选股策略更新报告: {report}")

        except Exception as e:
            logger.error(f"更新开阳星选股策略失败: {e}")

    async def _send_learning_insights_to_kaiyang(self, insights: List[SelectionPattern]):
        """向开阳星发送学习洞察"""
        try:
            # 尝试调用开阳星的学习接口
            try:
                from roles.kaiyang_star.services.stock_selection_service import StockSelectionService

                kaiyang_service = StockSelectionService()

                # 检查开阳星是否有学习接口
                if hasattr(kaiyang_service, 'receive_learning_insights'):
                    # 转换洞察为开阳星可理解的格式
                    learning_data = {
                        "source": "瑶光星学习系统",
                        "timestamp": datetime.now().isoformat(),
                        "insights_count": len(insights),
                        "insights": []
                    }

                    for pattern in insights:
                        insight_data = {
                            "pattern_id": pattern.pattern_id,
                            "pattern_name": pattern.pattern_name,
                            "success_rate": pattern.success_rate,
                            "confidence_score": pattern.confidence_score,
                            "key_indicators": pattern.key_indicators,
                            "market_conditions": pattern.market_conditions,
                            "risk_level": pattern.risk_level,
                            "sample_stocks": pattern.sample_stocks,
                            "learning_source": "瑶光星量化研究"
                        }
                        learning_data["insights"].append(insight_data)

                    # 发送学习洞察
                    result = await kaiyang_service.receive_learning_insights(learning_data)

                    if result.get("success"):
                        logger.info(f"✅ 成功向开阳星发送 {len(insights)} 个学习洞察")
                    else:
                        logger.warning(f"⚠️ 开阳星接收学习洞察失败: {result.get('error', 'Unknown error')}")
                else:
                    # 开阳星没有学习接口，使用API方式
                    await self._send_insights_via_api(insights)

            except ImportError:
                # 无法直接导入，使用API方式
                await self._send_insights_via_api(insights)

        except Exception as e:
            logger.error(f"向开阳星发送学习洞察失败: {e}")

    async def _send_insights_via_api(self, insights: List[SelectionPattern]):
        """通过API向开阳星发送洞察"""
        try:
            import requests

            # 构建API请求数据
            api_data = {
                "source": "瑶光星学习系统",
                "timestamp": datetime.now().isoformat(),
                "insights": []
            }

            for pattern in insights:
                insight_data = {
                    "pattern_name": pattern.pattern_name,
                    "success_rate": pattern.success_rate,
                    "confidence_score": pattern.confidence_score,
                    "key_indicators": pattern.key_indicators,
                    "market_conditions": pattern.market_conditions,
                    "risk_level": pattern.risk_level,
                    "recommendations": f"基于{pattern.pattern_name}的选股建议"
                }
                api_data["insights"].append(insight_data)

            # 发送API请求（假设开阳星有学习接口）
            # 这里可以根据实际的开阳星API端点进行调用
            logger.info(f"📡 准备通过API向开阳星发送 {len(insights)} 个学习洞察")
            logger.info(f"📊 洞察数据: {api_data}")

            # 实际项目中这里应该是真实的API调用
            # response = requests.post("http://kaiyang-star-api/learning/insights", json=api_data)

            logger.info("✅ 学习洞察已通过API发送给开阳星")

        except Exception as e:
            logger.error(f"通过API发送洞察失败: {e}")

    async def _identify_industry_pattern(self, training_results: List[Dict]) -> Optional[SelectionPattern]:
        """识别行业模式"""
        try:
            # 简化的行业模式识别
            industry_counts = {}

            for result in training_results:
                analysis = result.get("analysis_results", {})
                fundamental = analysis.get("fundamental_insights", {})
                industry = fundamental.get("industry", "未知")

                if industry != "未知":
                    industry_counts[industry] = industry_counts.get(industry, 0) + 1

            if industry_counts:
                dominant_industry = max(industry_counts.items(), key=lambda x: x[1])

                if dominant_industry[1] >= 3:  # 至少3只股票
                    pattern_id = f"industry_{dominant_industry[0]}_{datetime.now().strftime('%Y%m%d')}"

                    return SelectionPattern(
                        pattern_id=pattern_id,
                        pattern_name=f"{dominant_industry[0]}行业集中模式",
                        success_rate=dominant_industry[1] / len(training_results),
                        key_indicators=[f"{dominant_industry[0]}行业"],
                        market_conditions=["行业轮动"],
                        risk_level="中等",
                        confidence_score=0.6,
                        sample_stocks=[]
                    )

        except Exception as e:
            logger.error(f"识别行业模式失败: {e}")

        return None

    async def _identify_technical_pattern(self, training_results: List[Dict]) -> Optional[SelectionPattern]:
        """识别技术形态模式"""
        try:
            # 简化的技术形态模式识别
            trend_up_count = 0
            ma_alignment_count = 0

            for result in training_results:
                analysis = result.get("analysis_results", {})
                technical = analysis.get("technical_patterns", {})

                if technical.get("current_trend") == "上涨":
                    trend_up_count += 1

                if technical.get("ma_alignment"):
                    ma_alignment_count += 1

            if trend_up_count >= len(training_results) * 0.7:  # 70%以上上涨趋势
                pattern_id = f"technical_uptrend_{datetime.now().strftime('%Y%m%d')}"

                return SelectionPattern(
                    pattern_id=pattern_id,
                    pattern_name="技术上涨趋势模式",
                    success_rate=trend_up_count / len(training_results),
                    key_indicators=["上涨趋势", "均线多头排列"],
                    market_conditions=["技术面强势"],
                    risk_level="中低",
                    confidence_score=0.7,
                    sample_stocks=[]
                )

        except Exception as e:
            logger.error(f"识别技术形态模式失败: {e}")

        return None

    async def _identify_risk_return_pattern(self, training_results: List[Dict]) -> Optional[SelectionPattern]:
        """识别风险收益模式"""
        try:
            # 简化的风险收益模式识别
            high_return_low_risk_count = 0

            for result in training_results:
                analysis = result.get("analysis_results", {})
                basic_metrics = analysis.get("basic_metrics", {})

                total_return = basic_metrics.get("total_return", 0)
                max_drawdown = basic_metrics.get("max_drawdown", 0)

                # 高收益低风险：收益>20%且最大回撤<-15%
                if total_return > 20 and max_drawdown > -15:
                    high_return_low_risk_count += 1

            if high_return_low_risk_count >= 3:  # 至少3只股票
                pattern_id = f"risk_return_{datetime.now().strftime('%Y%m%d')}"

                return SelectionPattern(
                    pattern_id=pattern_id,
                    pattern_name="高收益低风险模式",
                    success_rate=high_return_low_risk_count / len(training_results),
                    key_indicators=["高收益", "低回撤"],
                    market_conditions=["稳定市场"],
                    risk_level="低",
                    confidence_score=0.8,
                    sample_stocks=[]
                )

        except Exception as e:
            logger.error(f"识别风险收益模式失败: {e}")

        return None

# 全局实例
intelligent_stock_selection_service = IntelligentStockSelectionService()
